import React from 'react'

import Text from './Text'

const sizes = {
  h1: '4xl',
  h2: '3xl',
  h3: '2xl',
  h4: 'xl',
  h5: 'lg',
}

/**
 * Heading component
 * Renders headings with customizable styles and sizes.
 * @param {Object} props Component properties
 * @param {'h1' | 'h2' | 'h3' | 'h4' | 'h5'} [props.as='h1'] The HTML tag to use for the heading
 * @param {boolean} [props.capitalized=false] Whether to capitalize the text
 * @param {React.ReactNode} props.children The content of the heading
 * @param {string} [props.className=''] Additional CSS classes to apply
 * @param {string} [props.color] The text color
 * @param {string} [props.title] The title text, if different from children
 * @param {Object} [props.textSize] Custom text size styles
 * @param {string} [props.fontWeight='bold'] The font weight of the heading
 * @returns {React.ReactElement} The rendered heading component
 */
export default function Heading({
  as = 'h1',
  capitalized,
  children,
  className = '',
  color,
  title,
  textSize,
  fontWeight = 'bold',
  ...props
}) {
  return (
    <Text
      as={as}
      capitalized={capitalized}
      className={className}
      defaultColorClass="text-gray-700 dark:text-gray-200"
      color={color}
      textSize={textSize}
      defaultTextSize={sizes[as] || sizes.h1}
      fontWeight={fontWeight}
      {...props}
    >
      {title || children}
    </Text>
  )
}
