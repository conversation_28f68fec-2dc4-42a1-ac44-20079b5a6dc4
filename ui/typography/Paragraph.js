import React from 'react'

import Text from './Text'

/**
 * Paragraph component
 * Renders a paragraph with customizable styles, color, and indentation.
 * @param {Object} props Component properties
 * @param {React.ReactNode} props.children The content of the paragraph
 * @param {string} [props.className=''] Additional CSS classes to apply
 * @param {string} [props.color] The text color
 * @param {boolean} [props.indent=false] Whether to indent the paragraph
 * @param {string} [props.fontWeight] The font weight of the text
 * @param {Object} [props.textSize] Custom text size styles
 * @returns {React.ReactElement} The rendered paragraph component
 */
export default function Paragraph({
  children,
  className = '',
  color,
  indent,
  fontWeight,
  textSize,
  ...props
}) {
  const indentClass = indent ? 'px-0 sm:px-8 md:px-12 lg:px-20' : ''

  return (
    <Text
      as="p"
      className={`${indentClass} ${className}`}
      color={color}
      textSize={textSize}
      defaultTextSize="lg"
      fontWeight={fontWeight}
      {...props}
    >
      {children}
    </Text>
  )
}
