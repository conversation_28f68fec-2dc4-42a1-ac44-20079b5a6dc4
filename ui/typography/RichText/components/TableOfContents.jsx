import { slugify } from 'utils/strings'

const intentationClasses = {
  1: 'ml-4',
  2: 'ml-8',
  3: 'ml-12',
  4: 'ml-16',
  5: 'ml-20',
  6: 'ml-24',
}

/**
 * Table of Content block component
 */
export default function TableOfContent({ title, headings }) {
  return (
    <div className="not-prose flex flex-col gap-1 px-6 py-4 bg-primary-50 dark:bg-primary-800 border border-primary-200 dark:border-primary-700 rounded-lg">
      {title && (
        <label className="text-xl font-bold text-theme-900 dark:text-theme-100">
          {title}
        </label>
      )}
      <ul className="my-0 px-2 py-0">
        {headings.map((headingNode, index) => {
          const { attrs, content } = headingNode
          const { level } = attrs
          const text = content ? content.map(n => n.text).join('') : undefined
          const id = slugify(text) || `heading-${index}`

          return (
            <li
              className={`list-disc ${intentationClasses[level]}`}
              key={`${id}-${index}`}
            >
              <a
                className="font-semibold text-primary-600 dark:text-primary-100 hover:underline"
                href={`#${id}`}
              >
                {text}
              </a>
            </li>
          )
        })}
      </ul>
    </div>
  )
}
