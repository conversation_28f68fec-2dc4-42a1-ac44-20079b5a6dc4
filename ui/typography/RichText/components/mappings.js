import dynamic from 'next/dynamic'
import { getDocumentUrl } from 'utils/documents'
import { slugify } from 'utils/strings'

const BibleVerse = dynamic(() => import('ui/data-display/BibleVerse'))
const Chiastic = dynamic(() => import('ui/data-display/Chiastic'))
const CTA = dynamic(() => import('ui/data-display/CTA'))
const Picture = dynamic(() => import('ui/data-display/Picture'))
const Player = dynamic(() => import('ui/data-display/Player'))
const TextLink = dynamic(() => import('ui/navigation/TextLink'))
const Heading = dynamic(() => import('ui/typography/Heading'))
const Paragraph = dynamic(() => import('ui/typography/Paragraph'))
const TableOfContent = dynamic(() => import('./TableOfContents'))

export const mappings = {
  'paragraph': {
    render: (node, key, children, { textSize, color }) => {
      return (
        <Paragraph key={key} textSize={textSize} color={color}>
          {children}
        </Paragraph>
      )
    },
  },
  'footnote': {
    render: (node, key, children) => {
      return (
        <li
          id={`footnote-${node.attrs['data-id']}`}
          className="richtext-small not-prose prose scroll-mt-36"
          key={key}
        >
          {children}
        </li>
      )
    },
  },
  // Number of the footnote
  'footnoteReference': {
    render: (node, key) => {
      return (
        <a
          id={`reference-${node.attrs['data-id']}`}
          key={key}
          href={`#footnote-${node.attrs['data-id']}`}
          onClick={e => {
            e.preventDefault()
            const target = document.getElementById(
              `footnote-${node.attrs['data-id']}`
            )
            if (target) {
              target.scrollIntoView({ behavior: 'smooth', block: 'start' })
            }
          }}
        >
          <sup>{node.attrs.referenceNumber}</sup>
        </a>
      )
    },
  },
  // Enumerated footnotes
  'footnotes': {
    render: (node, key, children) => {
      return <ol key={key}>{children}</ol>
    },
  },

  // Table of contents
  'tableOfContents': {
    render: (
      node,
      key,
      _, // No children
      { docContent } = {}
    ) => {
      const { attrs } = node
      const { title } = attrs

      const headings = docContent?.filter(n => n.type === 'heading')

      if (!docContent || !headings?.length) return null

      return <TableOfContent key={key} title={title} headings={headings} />
    },
  },

  // Call to action
  'callToAction': {
    render: (node, key) => {
      const { attrs } = node
      const { title, ctaLabel, url, description, type } = attrs

      return (
        <CTA
          key={key}
          title={title}
          description={description}
          ctaLabel={ctaLabel}
          url={url}
          variant={type}
        />
      )
    },
  },

  'smallText': {
    render: (node, key, children) => {
      return (
        <small className="text-sm" key={key}>
          {children}
        </small>
      )
    },
  },
  'heading': {
    render: (
      node,
      key,
      children,
      { decreaseHeaders, color, headingsColor, textSize }
    ) => {
      const { attrs, content } = node
      const level = parseInt(attrs.level) + (decreaseHeaders ? 1 : 0)

      // Generate a slug for the heading based on its content
      const id = content
        ? slugify(content.map(n => n.text).join(''))
        : undefined

      return (
        <Heading
          as={`h${level}`}
          key={key}
          color={headingsColor || color}
          id={id}
          className="scroll-mt-32"
          textSize={textSize}
        >
          {children}
        </Heading>
      )
    },
  },
  'bibleVerse': {
    render: (node, key) => {
      const { bible, passage, text } = node.attrs
      return (
        <BibleVerse key={key} bible={bible} passage={passage} text={text} />
      )
    },
  },
  'chiastic': {
    render: (node, key) => {
      const { items, notation, subNotation } = node.attrs
      return (
        <div className="p-2 not-prose flex gap-0">
          <Chiastic
            key={key}
            index={0} // Initial item
            items={items}
            notation={notation}
            subNotation={subNotation}
          />
        </div>
      )
    },
  },
  'image': {
    render: (node, key) => {
      const {
        alt,
        file,
        caption,
        copyright,
        url,
        align,
        transparent,
        clickToEnlarge,
      } = node.attrs
      return (
        <Picture
          file={file}
          key={key}
          alt={alt}
          align={align}
          caption={caption}
          copyright={copyright}
          clickToEnlarge={clickToEnlarge}
          transparent={transparent}
          innerClassName="mb-8 not-prose"
          url={url}
        />
      )
    },
  },
  'media': {
    render: (node, key) => {
      const { id, provider, caption } = node.attrs
      if (!id || !provider) return null

      return <Player id={id} provider={provider} caption={caption} key={key} />
    },
  },
  'table': {
    render: (node, key, children) => {
      return (
        <div key={key} className="overflow-x-auto">
          <table className="table-auto">
            <tbody>{children}</tbody>
          </table>
        </div>
      )
    },
  },
  'tableHeader': {
    render: (node, key, children) => {
      return <th key={key}>{children}</th>
    },
  },
  'tableRow': {
    render: (node, key, children) => {
      return (
        <tr key={key} className="table-row">
          {children}
        </tr>
      )
    },
  },
  'tableCell': {
    render: (node, key, children) => {
      return (
        <td key={key} className="table-cell min-w-24">
          {children}
        </td>
      )
    },
  },
  'bulletList': {
    tag: 'ul',
    className: 'list-disc ml-6 not-prose',
  },
  'orderedList': {
    tag: 'ol',
    className: 'list-decimal ml-6 not-prose',
  },
  'listItem': {
    tag: 'li',
    className: 'dark:text-white pl-2',
  },
  'blockquote': {
    tag: 'blockquote',
    className: 'relative font-serif text-xl italic text-gray-600',
  },
  'code-block': {
    tag: 'pre',
    className:
      'my-4 p-4 overflow-x-auto whitespace-pre-wrap font-mono bg-gray-900 rounded text-warn-200',
  },
  'link': {
    render: (mark, key, children, { entityId } = {}) => {
      const { attrs } = mark
      let to = ''

      switch (attrs.type) {
        case 'email':
          to = `mailto:${attrs.href}`
          break
        case 'phone':
          to = `tel:${attrs.href}`
          break
        case 'file':
          to = getDocumentUrl(attrs.file, entityId)
          break
        default:
          to = attrs.href
          break
      }

      return (
        <TextLink
          to={to}
          key={key}
          className="text-primary-700 underline dark:text-secondary-500"
        >
          {children}
        </TextLink>
      )
    },
  },
  'bold': {
    tag: 'strong',
    className: 'bold dark:text-secondary-400',
  },
  'italic': {
    tag: 'em',
    className: 'italic',
  },
  'subscript': {
    tag: 'sub',
  },
  'superscript': {
    tag: 'sup',
  },
  'strike': {
    tag: 'strike',
    className: 'line-through',
  },
  'code': {
    tag: 'code',
    className: 'px-1 bg-gray-900 text-secondary-200 font-mono',
  },
  'hardBreak': {
    tag: 'br',
  },
  'span': {
    tag: 'span',
  },
}
