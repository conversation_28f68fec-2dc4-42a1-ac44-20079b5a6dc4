import React from 'react'
import Serialize from './components/Serialize'
import { useRichTextCustomStyles } from './helpers/useRichTextCustomStyles'
import { cn } from 'utils/cn'

/**
 * RichText component
 * @param {Object} props The component props
 * @param {string} props.bodyFont The font to apply to the body text
 * @param {string} props.bodyTextSize The text size to apply to the body text
 * @param {string} props.color The color to apply to the text
 * @param {string} props.className Additional class names to apply to the component
 * @param {boolean} props.decreaseHeaders Whether to decrease the size of headers
 * @param {Object} props.doc The tiptap document to render
 * @param {string} props.headingsColor The color to apply to the headings
 * @param {string} props.headingsFont The font to apply to the headings
 * @param {string} props.headingsTextSize The text size to apply to the headings
 * @param {string} props.heading1Font The font to apply to H1 headings
 * @param {string} props.heading1TextSize The text size to apply to H1 headings
 * @param {string} props.heading2Font The font to apply to H2 headings
 * @param {string} props.heading2TextSize The text size to apply to H2 headings
 * @param {string} props.heading3Font The font to apply to H3 headings
 * @param {string} props.heading3TextSize The text size to apply to H3 headings
 * @param {string} props.heading4Font The font to apply to H4 headings
 * @param {string} props.heading4TextSize The text size to apply to H4 headings
 * @param {string} props.id The id to apply to the component
 * @param {string} props.paragraphFont The font to apply to the paragraph text
 * @param {string} props.paragraphTextSize The text size to apply to the paragraph
 * @param {string} props.textSize The text size to apply to the component
 * @returns {React.ReactElement|null} The rendered component or null if the doc is not valid
 */
export default function RichText({
  bodyFont = 'sans',
  bodyTextSize,
  color,
  className = '',
  decreaseHeaders,
  doc,
  headingsColor,
  headingsFont = 'sans',
  headingsTextSize,
  heading1Font = 'sans',
  heading1TextSize,
  heading2Font = 'sans',
  heading2TextSize,
  heading3Font = 'sans',
  heading3TextSize,
  heading4Font = 'sans',
  heading4TextSize,
  id,
  linkColor,
  paragraphFont = 'serif',
  paragraphTextSize,
  style = {},
  textSize,
}) {
  const {
    customStyles,
    isBodySans,
    isParagraphSans,
    isHeading1Sans,
    isHeading2Sans,
    isHeading3Sans,
    isHeading4Sans,
    isHeading5Sans,
    isHeading6Sans,
  } = useRichTextCustomStyles({
    style,
    color,
    headingsColor,
    linkColor,
    bodyFont,
    headingsFont,
    heading1Font,
    heading2Font,
    heading3Font,
    heading4Font,
    paragraphFont,
  })

  const headingsTextSizeMap = {
    heading1: heading1TextSize,
    heading2: heading2TextSize,
    heading3: heading3TextSize,
    heading4: heading4TextSize,
  }

  const text =
    typeof doc === 'object' && Array.isArray(doc.content)
      ? doc.content.map((node, i) => {
          const customTextSize =
            node.type === 'heading'
              ? headingsTextSizeMap[`heading${node.attrs.level}`] ||
                headingsTextSize
              : paragraphTextSize || bodyTextSize

          return (
            <Serialize
              node={node}
              key={`node-${i}`}
              id={`node-${i}`}
              renderProps={{
                color,
                headingsColor,
                decreaseHeaders,
                docContent: doc.content, // Pass the whole doc content to the serialize function
                textSize: customTextSize || textSize, // Use custom text size or fallback to textSize prop
              }}
            />
          )
        })
      : null

  if (!text) return null

  return (
    <div
      className={cn(
        'prose max-w-none dark:prose-invert',

        // Body font
        isBodySans ? 'font-sans' : 'font-serif',

        // Paragraph font
        isParagraphSans && 'prose-p:font-sans',

        // H1
        cn(isHeading1Sans ? 'prose-h1:font-sans' : 'prose-h1:font-serif'),
        // H2
        cn(isHeading2Sans ? 'prose-h2:font-sans' : 'prose-h2:font-serif'),
        // H3
        cn(isHeading3Sans ? 'prose-h3:font-sans' : 'prose-h3:font-serif'),
        // H4
        cn(isHeading4Sans ? 'prose-h4:font-sans' : 'prose-h4:font-serif'),
        // H5
        cn(isHeading5Sans ? 'prose-h5:font-sans' : 'prose-h5:font-serif'),
        // H6
        cn(isHeading6Sans ? 'prose-h6:font-sans' : 'prose-h6:font-serif'),
        className
      )}
      id={id}
      style={customStyles}
    >
      {text}
    </div>
  )
}
