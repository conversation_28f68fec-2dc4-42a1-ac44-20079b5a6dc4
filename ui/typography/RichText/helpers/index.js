/**
 * Serializes a tiptap node to flat text
 * @param {Object} node The node to serialize
 * @param {string} key The key to use for the node
 * @returns {string|null} The serialized text or null if the node is not a text node
 */
export function serializeFlat(node, key) {
  if (node.type === 'text' && typeof node.text === 'string') {
    return node.text
  }

  const { content } = node

  return content?.map((node, i) => serializeFlat(node, `${key}-${i}`))
}

/**
 * Serializes a tiptap document to flat text
 * @param {Object} doc
 * @returns {string|null}
 */
export function getFlatText(doc) {
  if (typeof doc !== 'object' || !Array.isArray(doc.content)) return null

  return doc.content.map((n, i) => serializeFlat(n, `node-${i}`))
}

/**
 * Checks if provided value is a tiptap doc
 * @param {Object} doc
 * @returns boolean
 */
export function isDoc(doc) {
  return typeof doc === 'object' && Array.isArray(doc.content)
}

/**
 * Checks if provided doc is empty
 * @param {Object} doc
 * @returns boolean
 */
export function isDocEmpty(doc) {
  return (
    !isDoc(doc) ||
    (isDoc(doc) && doc.content.length === 1 && !doc.content[0].content)
  )
}
