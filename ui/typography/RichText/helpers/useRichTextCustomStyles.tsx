import React, { useMemo } from 'react'

export function useRichTextCustomStyles({
  style,
  color,
  headingsColor,
  linkColor,
  bodyFont,
  headingsFont,
  headingsInSans = ['4', '5', '6'], // Alps default is serif for h4 to h6
  heading1Font,
  heading2Font,
  heading3Font,
  heading4Font,
  paragraphFont,
}) {
  // Override default Tailwind CSS prose variables with custom colors
  const customColorsStyles = useMemo(
    () => ({
      ...(color && {
        //  For light mode
        '--tw-prose-body': `rgba(var(--${color}))`,
        '--tw-prose-headings': `rgba(var(--${color}))`,
      }),

      ...(headingsColor && {
        '--tw-prose-headings': `rgb(var(--${headingsColor}))`,
        '--tw-prose-invert-headings': `rgb(var(--${headingsColor}))`,
      }),

      // Link Color
      ...(linkColor && {
        '--tw-prose-links': `rgb(var(--${linkColor}))`,
        '--tw-prose-invert-links': `rgb(var(--${linkColor}))`,
      }),
    }),
    [color, headingsColor, linkColor]
  )

  // Combine custom styles with the provided style prop
  const customStyles = useMemo(
    () => ({
      ...(style || {}),
      ...customColorsStyles,
    }),
    [style, customColorsStyles]
  )

  // Set font styles:
  const areHeadingsSans = headingsFont === 'sans'

  // Body font check:
  const isBodySans = bodyFont === 'sans'
  const isParagraphSans = paragraphFont ? paragraphFont === 'sans' : isBodySans

  // Heading font checks:
  const headingsInSansMap = [
    heading1Font,
    heading2Font,
    heading3Font,
    heading4Font,
  ].reduce((acc: Record<string, boolean>, font, index) => {
    const level = (index + 1).toString()

    // Check if:
    // - The font is explicitly set to 'sans'
    // - Or if all headings are set to use 'sans'
    // - Or if the heading level is in the headingsInSans array and sansHeadings is true
    acc[level] = font
      ? font === 'sans'
      : areHeadingsSans || Boolean(headingsInSans.includes(level))

    return acc
  }, {})

  return {
    customStyles,
    isBodySans,
    isParagraphSans,
    isHeading1Sans: headingsInSansMap['1'],
    isHeading2Sans: headingsInSansMap['2'],
    isHeading3Sans: headingsInSansMap['3'],
    isHeading4Sans: headingsInSansMap['4'],
    isHeading5Sans: headingsInSansMap['5'],
    isHeading6Sans: headingsInSansMap['6'],
  }
}
