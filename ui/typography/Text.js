import React from 'react'

import { getTextColor } from 'ui/helpers/getColor'
import useFontWeight from 'ui/helpers/useFontWeight'
import useTextSize from 'ui/helpers/useTextSize'
import useTextAlign from 'ui/helpers/useTextAlign'

const styles = {
  capitalized: 'uppercase leading-none',
  normal: '',
}

/**
 * Text component
 * Renders text with customizable styles, alignment, and color.
 * @param {Object} props Component properties
 * @param {'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'p' | 'blockquote'} [props.as='p'] The HTML tag to use for the text
 * @param {'left' | 'center' | 'right' | 'justify' | Object} [props.align='left'] The text alignment
 * @param {boolean} [props.capitalized=false] Whether to capitalize the text
 * @param {React.ReactNode} props.children The content of the text
 * @param {string} [props.className=''] Additional CSS classes to apply
 * @param {string} [props.color] The text color
 * @param {string} [props.defaultColorClass=''] The default color class to apply
 * @param {string} [props.defaultTextSize] The default text size class
 * @param {'thin'|'extralight'|'light'|'normal'|'medium'|'semibold'|'bold'|'extrabold'|'black'} [props.fontWeight] The font weight of the text
 * @param {string} [props.id] The id to apply to the text element
 * @param {Object} [props.textSize] Custom text size styles
 * @param {string} [props.text] The text content, if not using children
 * @returns {React.ReactElement} The rendered text component
 */
export default function Text({
  as = 'p',
  align = 'left',
  capitalized,
  children,
  className = '',
  color,
  defaultColorClass = '',
  defaultTextSize,
  fontWeight,
  id,
  textSize,
  text,
}) {
  const alignClass = useTextAlign(align)
  const stylesClass = capitalized ? styles.capitalized : styles.normal
  const fontWeightClass = useFontWeight(fontWeight)
  const textSizeClass = useTextSize(textSize, defaultTextSize)

  const colorClass = getTextColor(
    color,
    defaultColorClass || 'text-gray-800 dark:text-primary-dark-100'
  )

  return React.createElement(
    as,
    {
      id,
      className: `${colorClass} ${color ? 'dark:' + colorClass : ''} ${alignClass} ${textSizeClass} ${fontWeightClass} ${stylesClass} ${className}`,
    },
    text || children
  )
}
