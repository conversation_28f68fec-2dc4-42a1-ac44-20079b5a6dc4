import PropTypes from 'prop-types'

import Head from 'next/head'

export default function NotFound({ title, description, headTitle, code }) {
  return (
    <div className="flex flex-col space-y-2 md:flex-row md:space-x-6 md:space-y-0 rtl:md:space-x-reverse">
      <Head>
        <title>{headTitle}</title>
      </Head>
      {code && (
        <code className="font-mono text-3xl font-bold text-gray-300 sm:text-4xl md:text-6xl">
          {code}/
        </code>
      )}
      <div>
        <h1 className="text-4xl font-bold text-gray-700">{title}</h1>
        <p className="text-lg text-gray-600">{description}</p>
      </div>
    </div>
  )
}
NotFound.propTypes = {
  code: PropTypes.string,
  description: PropTypes.node,
  headTitle: PropTypes.string,
  title: PropTypes.node,
}
