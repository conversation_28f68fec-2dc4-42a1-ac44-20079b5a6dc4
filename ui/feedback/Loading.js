import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function Loading({
  className = '',
  label = 'Loading…',
  delay = 500,
}) {
  const [loadingWait, setLoadingWait] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => setLoadingWait(false), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return (
    <div
      className={`flex w-full items-center justify-center space-x-2 overflow-hidden py-8 rtl:space-x-reverse ${
        loadingWait ? 'h-0' : 'h-6'
      } ${className}`}
    >
      <Icon className="animate-spin text-2xl" name="spinner-third" />
      <span className="font-semibold text-lg">{label}</span>
    </div>
  )
}
Loading.propTypes = {
  className: PropTypes.string,
  delay: PropTypes.number,
  label: PropTypes.string,
}
