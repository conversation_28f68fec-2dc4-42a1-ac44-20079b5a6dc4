import React, { useCallback, useState } from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { useGeoRedirect } from 'components/GeoRedirectProvider'

const Button = dynamic(() => import('ui/buttons/Button'))

export default function GeoRedirect() {
  const { t } = useTranslation()

  const [redirecting, setRedirecting] = useState(false)
  const { showGeoRedirectModal, updateUserChoice, siteGeoRedirect } =
    useGeoRedirect()

  // Redirect to the country specific site
  const onRedirect = useCallback(() => {
    setRedirecting(true)
    // updateUserChoice(true) // TODO: store value in order to automatically redirect next time?
    window.location.href = siteGeoRedirect.url
  }, [setRedirecting, siteGeoRedirect])

  // Close the modal and store the user choice in local storage
  const onClose = useCallback(() => {
    updateUserChoice(false)
  }, [updateUserChoice])

  // Do not render if the user has already made a choice or there's no geo redirect
  if (!showGeoRedirectModal) return null

  return (
    <div className="transition-colors duration-300 ease-in-out fixed inset-0 z-max flex items-center justify-center bg-black bg-opacity-25 p-4 sm:p-6 md:p-8">
      <div className="flex max-h-screen max-w-md flex-col space-y-8 overflow-hidden overflow-y-auto rounded-lg bg-white p-8 shadow-2xl">
        <div className="space-y-4 text-center">
          <h4 className="font-bold text-2xl">{t('geoRedirectTitle')}</h4>
          <p>
            {t('geoRedirectDescription', {
              country: siteGeoRedirect.countryName,
            })}
          </p>
        </div>
        <div className="flex flex-col items-stretch justify-center space-y-4 tracking-wide md:flex-row md:space-x-4 md:space-y-0 rtl:md:space-x-reverse">
          <Button
            label={t('geoRedirectGoToSite')}
            variant="primary"
            onClick={onRedirect}
            icon={redirecting ? 'spinner-third' : ''}
            iconClass="animate-spin"
          />
          <Button
            label={t('geoRedirectStayOnPage')}
            variant="tertiary"
            onClick={onClose}
          />
        </div>
      </div>
    </div>
  )
}
