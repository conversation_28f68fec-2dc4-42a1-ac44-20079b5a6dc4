import PropTypes from 'prop-types'

import clsx from 'clsx'
import { Trans, useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import Button from 'ui/buttons/Button'
import Checkbox from 'ui/data-entry/Checkbox'
import Submit from 'ui/data-entry/Submit'
import Icon from 'ui/icons/Icon'

import Alert from '../Alert'
import { useManageNewsletterSubscription } from './api'
import FormBuilder from 'ui/data-entry/FormBuilder'
import Heading from 'ui/typography/Heading'
import { useState } from 'react'

const Link = dynamic(() => import('ui/navigation/Link'))
const Form = dynamic(() => import('ui/data-entry/Form'))
const EmailFieldController = dynamic(() => import('ui/data-entry/EmailField'))

export const NewletterSubscriptionForm = ({
  className,
  successTitle,
  successDescription,
  emailLabel,
  emailPlaceholderLabel,
  buttonLabel,
  title,
  termsUrl,
  termsLinkLabel,
  termsAcceptLabel,
  termsTitle,
  fields,
  listOptions,
  action = 'subscribe',
}) => {
  const { t } = useTranslation()

  const [error, setError] = useState(null)

  const [isSuccess, setIsSuccess] = useState(false)

  const { mutate, reset, isLoading } = useManageNewsletterSubscription(action)

  if (!['subscribe', 'unsubscribe'].includes(action)) {
    return null
  }

  const onSubmit = data => {
    const selectedOptions = data.lists || []

    // The list options array is expected to be an array of objects with 'label' and 'value'
    // Of the data.lists field we have an array of selected values like lists[0] with the value true.
    const selectedValues = Object.entries(selectedOptions)
      .filter(([, value]) => value === true)
      .map(([key]) => key)

    // The selected values are the position in listOptions where we want to get the value from, and build the lists array
    const lists =
      // If only one list was selected, the field will not be shown and the value should be added to the data
      listOptions?.length === 1
        ? [listOptions[0].value]
        : selectedValues?.length
          ? selectedValues.map(index => listOptions[index].value)
          : undefined

    const mutateData = {
      ...data,
      ...(lists ? { lists } : {}),
    }

    mutate(mutateData, {
      onSuccess: data => {
        // Errors come in the success due to the axios validateStatus allowing 4xx responses
        if (data?.error) {
          setError(data.error)
          setIsSuccess(false)
          return
        } else {
          setError(null)
          setIsSuccess(true)
        }
      },
    })
  }

  if (isSuccess) {
    return (
      <div className={clsx('space-y-4', className)}>
        <h3 className="mb-1 font-bold uppercase leading-normal text-lg lg:text-xl">
          {title || t('newsletterSubscription')}
        </h3>
        <Alert
          type="success"
          title={
            successTitle ||
            t(
              action === 'subscribe'
                ? 'newsletterSubscriptionSuccessTitle'
                : 'newsletterUnsubscribeSuccessTitle'
            )
          }
          message={
            successDescription ||
            t(
              action === 'subscribe'
                ? 'newsletterSubscriptionSuccessDescription'
                : 'newsletterUnsubscribeSuccessDescription'
            )
          }
        />
        <Button
          label={t('done')}
          onClick={() => {
            reset()
            setIsSuccess(false)
            setError(null)
          }}
        />
      </div>
    )
  }

  return (
    <Form
      className={clsx('flex w-full flex-col space-y-4', className)}
      onSubmit={onSubmit}
      disabled={isLoading}
    >
      <h3 className="mb-1 font-bold uppercase leading-normal text-lg lg:text-xl">
        {title || t('newsletterSubscription')}
      </h3>
      <FormBuilder fields={fields} maxColumns={4} error={error} />
      <EmailFieldController
        name="email"
        label={emailLabel || t('emailAddress')}
        placeholder={emailPlaceholderLabel || ''}
        icon="arrow-right-long"
        iconColor="secondary"
        iconActionType="submit"
        error={
          error?.validationErrors?.find(err => err.includes('email'))
            ? t('INVALID_EMAIL')
            : undefined
        }
        renderIcon={
          isLoading
            ? () => (
                <Icon
                  name="spinner-third"
                  className="absolute right-0 top-0 flex h-full items-center justify-center px-1 py-2 text-secondary"
                  iconClass="animate-spin"
                />
              )
            : undefined
        }
        required
      />

      {termsUrl && (
        <div className="flex flex-col gap-2">
          {termsTitle && <Heading as="h4" text={termsTitle} />}
          <Checkbox
            name="terms"
            required
            label={
              termsAcceptLabel ? (
                <>
                  <span>
                    {termsAcceptLabel || t('newsletterTermsAcceptLabel')}
                  </span>

                  <span>
                    {' '}
                    <Link
                      className="text-primary-700 underline dark:text-secondary-500"
                      to={termsUrl}
                    >
                      {termsLinkLabel || termsUrl}
                    </Link>
                  </span>
                </>
              ) : (
                <Trans
                  i18nKey="acceptPrivacyPolicy"
                  components={{
                    url: (
                      <Link
                        className="text-primary-700 underline dark:text-secondary-500"
                        to={termsUrl}
                      />
                    ),
                  }}
                />
              )
            }
          />
        </div>
      )}

      {(error === 'Internal error' || error === 'UNKNOWN_ERROR') && (
        <Alert
          type="danger"
          title={t('errorNewsletterSubscriptionTitle')}
          message={t('errorNewsletterSubscriptionDescription')}
        />
      )}

      <div className="self-start">
        <Submit
          label={buttonLabel || t('subscribe')}
          icon="envelope"
          loading={isLoading}
        />
      </div>
    </Form>
  )
}

NewletterSubscriptionForm.propTypes = {
  action: PropTypes.oneOf(['subscribe', 'unsubscribe']),
  className: PropTypes.string,
  successTitle: PropTypes.string,
  successDescription: PropTypes.string,
  emailLabel: PropTypes.string,
  emailPlaceholderLabel: PropTypes.string,
  buttonLabel: PropTypes.string,
  title: PropTypes.string,
  termsUrl: PropTypes.string,
}
