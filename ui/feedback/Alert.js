import React from 'react'
import PropTypes from 'prop-types'

const styles = {
  danger:
    'text-danger-600 bg-danger-100  border-danger-300 dark:bg-danger-900 dark:text-danger-50 dark:border-danger-700',
  info: 'text-primary-600 bg-primary-100 border-primary-200 dark:bg-primary-dark-700 dark:text-primary-50 dark:border-gray-700',
  success:
    'text-success-600 bg-success-100 border-success-300 dark:bg-success-900 dark:text-success-50 dark:border-success-700',
  warning:
    'text-warning-600 bg-warning-100 border-warning-200 dark:bg-warning-700 dark:text-warning-100 dark:border-warning-500',
  neutral:
    'text-gray-600 bg-gray-100 border-gray-300 dark:bg-gray-700 dark:text-gray-50 dark:border-gray-600',
}

export default function Alert({
  actions,
  children,
  className = '',
  message,
  messageClassName,
  title,
  type = 'info',
}) {
  const typeClasses = styles[type] || styles.info

  return (
    <div className={`space-y-4 rounded border p-6 ${typeClasses} ${className}`}>
      <div className="space-y-2">
        {title && (
          <h4 className="font-bold uppercase tracking-wide text-xl">{title}</h4>
        )}
        <div className={messageClassName}>
          {children || <p className="text-lg">{message}</p>}
        </div>
      </div>
      {actions && <div className="flex justify-end">{actions}</div>}
    </div>
  )
}
Alert.propTypes = {
  children: PropTypes.node,
  actions: PropTypes.node,
  className: PropTypes.string,
  message: PropTypes.string,
  messageClassName: PropTypes.string,
  title: PropTypes.string,
  type: PropTypes.oneOf(['danger', 'info', 'neutral', 'success', 'warning']),
}
