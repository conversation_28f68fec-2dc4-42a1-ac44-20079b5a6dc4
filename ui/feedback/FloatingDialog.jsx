import PropTypes from 'prop-types'
import React, { useContext, useMemo, useState } from 'react'

import {
  FloatingFocusManager,
  FloatingOverlay,
  FloatingPortal,
  useClick,
  useDismiss,
  useFloating,
  useId,
  useInteractions,
  useMergeRefs,
  useRole,
} from '@floating-ui/react'
import Button from 'ui/buttons/Button'

// DialogContext is a React context that provides the state and interactions for a Dialog.
const DialogContext = React.createContext(null)

/**
 * useDialogContext is a hook that returns the Dialog context.
 * @returns {Object} - The Dialog context.
 */
export function useDialogContext() {
  const context = useContext(DialogContext)

  if (context == null) {
    throw new Error('Dialog components must be wrapped in <Dialog />')
  }

  return context
}

/**
 * useDialog is a hook that provides the state and interactions for a Dialog.
 * @param {Object} params - The options for the Dialog.
 * @param {boolean} params.initialOpen - If true, the Dialog will be open by default.
 * @param {boolean} params.open - If true, the Dialog will be open.
 * @param {function} params.onOpenChange - A function that will be called when the Dialog is opened or closed.
 * @returns {Object} - An object containing the Dialog state and interactions.
 */
export function useDialog({
  initialOpen = false,
  open: controlledOpen,
  onOpenChange: setControlledOpen,
}) {
  const [uncontrolledOpen, setUncontrolledOpen] = useState(initialOpen)

  const open = controlledOpen ?? uncontrolledOpen
  const setOpen = setControlledOpen ?? setUncontrolledOpen

  const data = useFloating({
    open,
    onOpenChange: setOpen,
  })

  const context = data.context

  const click = useClick(context, {
    enabled: controlledOpen == null,
  })
  const dismiss = useDismiss(context, { outsidePressEvent: 'mousedown' })
  const role = useRole(context)

  const interactions = useInteractions([click, dismiss, role])

  return useMemo(
    () => ({
      open,
      setOpen,
      ...interactions,
      ...data,
    }),
    [open, setOpen, interactions, data]
  )
}

/**
 * Dialog is a component that renders a Dialog.
 * @param {React.ReactNode} children - The content of the Dialog.
 * @param {boolean} initialOpen - If true, the Dialog will be open by default.
 * @param {boolean} open - If true, the Dialog will be open.
 * @param {function} onOpenChange - A function that will be called when the Dialog is opened or closed.
 * @returns {React.ReactNode} - The Dialog component.
 */
export function Dialog({ children, ...options }) {
  const dialog = useDialog(options)
  return (
    <DialogContext.Provider value={dialog}>{children}</DialogContext.Provider>
  )
}
Dialog.propTypes = {
  children: PropTypes.node.isRequired,
  initialOpen: PropTypes.bool,
  open: PropTypes.bool,
  onOpenChange: PropTypes.func,
}

/**
 * DialogContent is a component that renders the content of a Dialog.
 * It can be used as a wrapper for any element.
 * @param {React.ReactNode} children - The content of the Dialog.
 * @param {string} title - The title of the Dialog.
 * @param {string} description - The description of the Dialog.
 * @param {string} boxClass - The class name of the Dialog box.
 * @param {string} contentClass - The class name of the Dialog content.
 * @param {string} overlayClass - The class name of the Dialog overlay.
 * @param {function} getBoxClass - A function that returns the class name of the Dialog content.
 * @param {function} getOverlayClass - A function that returns the class name of the Dialog overlay.
 * @param {React.Ref} ref - A ref to the Dialog content.
 */
export const DialogContent = React.forwardRef(function DialogContent(
  props,
  propRef
) {
  const {
    title,
    description,
    children,
    boxClass,
    getBoxClass,
    overlayClass,
    getOverlayClass,
    contentClass,
    transparent,
    ...rest
  } = props

  const {
    context: floatingContext,
    open,
    setOpen,
    ...context
  } = useDialogContext()
  const ref = useMergeRefs([context.refs.setFloating, propRef])

  const labelId = useId()
  const descriptionId = useId()

  const overlayClasses =
    typeof getOverlayClass === 'function' ? getOverlayClass(open) : overlayClass
  const boxClasses =
    typeof getBoxClass === 'function' ? getBoxClass(open) : boxClass

  if (!floatingContext.open) return null

  return (
    <FloatingPortal>
      <FloatingOverlay
        className={`fixed inset-0 flex items-center justify-center bg-black/60 transition-all duration-500 ease-in-out ${
          open
            ? 'z-dialogOpen opacity-100 backdrop-blur-sm'
            : 'pointer-events-none z-dialog opacity-0 backdrop-blur-0'
        } ${overlayClasses}`}
        lockScroll={open}
      >
        <FloatingFocusManager context={floatingContext}>
          <div
            className={`flex-0 flex grow-0 transform-gpu flex-col gap-2 rounded-lg p-6 transition-all duration-500 ease-in-out ${
              open ? 'translate-y-0 scale-100' : 'translate-y-12 scale-90'
            } ${
              transparent
                ? ''
                : `bg-white ${open ? 'shadow-xl' : ' shadow-none'}`
            } ${boxClasses}`}
            ref={ref}
            aria-labelledby={title ? labelId : undefined}
            aria-describedby={description ? descriptionId : undefined}
            {...context.getFloatingProps(rest)}
          >
            <div className="flex items-center justify-between gap-4">
              <div className="px-4">
                {title && (
                  <h2
                    className={`font-semibold text-lg ${
                      transparent ? 'text-white' : ''
                    }`}
                    id={labelId}
                  >
                    {title}
                  </h2>
                )}
                {description && (
                  <p
                    className={`text-sm ${
                      transparent ? 'text-white/60' : 'text-gray-400'
                    }`}
                    id={descriptionId}
                  >
                    {description}
                  </p>
                )}
              </div>

              <Button
                onClick={() => setOpen(false)}
                icon="times"
                variant={transparent ? 'linkLight' : 'link'}
                size="xl"
              />
            </div>
            <div data-name="content" className={contentClass}>
              {children}
            </div>
          </div>
        </FloatingFocusManager>
      </FloatingOverlay>
    </FloatingPortal>
  )
})
DialogContent.propTypes = {
  boxClass: PropTypes.string,
  children: PropTypes.node.isRequired,
  contentClass: PropTypes.string,
  description: PropTypes.string,
  getBoxClass: PropTypes.func,
  getOverlayClass: PropTypes.func,
  overlayClass: PropTypes.string,
  title: PropTypes.string,
  transparent: PropTypes.bool,
}

/**
 * DialogTrigger is a component that opens a Dialog when clicked.
 * It can be used as a button or as a wrapper for any element.
 *
 * @param {React.ReactNode} children - The content of the trigger.
 * @param {boolean} asChild - If true, the trigger will be rendered as a wrapper for the children.
 */
export const DialogTrigger = React.forwardRef(function DialogTrigger(
  { children, asChild = false, ...props },
  propRef
) {
  const context = useDialogContext()
  const childrenRef = children.ref
  const ref = useMergeRefs([context.refs.setReference, propRef, childrenRef])

  // `asChild` allows the user to pass any element as the anchor
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(
      children,
      context.getReferenceProps({
        ref,
        ...props,
        ...children.props,
        'data-state': context.open ? 'open' : 'closed',
      })
    )
  }

  return (
    <button
      ref={ref}
      // The user can style the trigger based on the state
      data-state={context.open ? 'open' : 'closed'}
      {...context.getReferenceProps(props)}
    >
      {children}
    </button>
  )
})
DialogTrigger.propTypes = {
  children: PropTypes.node.isRequired,
  asChild: PropTypes.bool,
}
