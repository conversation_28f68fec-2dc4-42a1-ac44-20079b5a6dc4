import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))
export default function Modal({ title, children, onClickOutside }) {
  return (
    <div>
      <Clickable
        className="fixed left-0 top-0 z-50 h-screen w-screen"
        onClick={onClickOutside}
      >
        <div className="absolute h-screen w-screen bg-gray-700 opacity-25"></div>
        <div className="z-20 flex min-h-screen items-end justify-center sm:items-center">
          <div className="container z-20 max-w-xl rounded-t-md bg-white p-8 opacity-100 sm:rounded-lg">
            {title && <h1>{title}</h1>}
            {children}
          </div>
        </div>
      </Clickable>
    </div>
  )
}

Modal.propTypes = {
  title: PropTypes.string,
  onClickOutside: PropTypes.func,
}
