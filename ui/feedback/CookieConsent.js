import PropTypes from 'prop-types'
import React, { Fragment, useEffect, useState } from 'react'

import { useUserCookieConsent } from 'components/CookieConsentProvider'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import Head from 'next/head'
import Script from 'next/script'
import useCode from 'utils/useCode'
import { useGeoRedirect } from 'components/GeoRedirectProvider'

const Button = dynamic(() => import('ui/buttons/Button'))
const ButtonToggle = dynamic(() => import('ui/buttons/ButtonToggle'))
const Accordion = dynamic(() => import('ui/data-display/Accordion'))
const AccordionItem = dynamic(() =>
  import('ui/data-display/Accordion').then(m => m.AccordionItem)
)

export default function CookieConsent({
  scripts = [],
  analytics = {
    enabled: false,
    domain: '',
  },
  settings = {},
  isOnesignalConfigured,
}) {
  const { showGeoRedirectModal } = useGeoRedirect()
  const { showCookieConsent, userConsent, updateUserConsent, disabled } =
    useUserCookieConsent()
  const { functional, tracking, targeting } = userConsent || {}
  const { t } = useTranslation('cookies')

  // Sets temporary/ephimeral state for the toggle buttons
  // (so scripts are no added to the page until the user hits one of the "accept" buttons)
  const [functionalTemp, setFunctionalTemp] = useState()
  const [trackingTemp, setTrackingTemp] = useState()
  const [targetingTemp, setTargetingTemp] = useState()

  // Keeps temporary states updated with store values
  useEffect(() => {
    setFunctionalTemp(functional)
  }, [functional])

  useEffect(() => {
    setTrackingTemp(tracking)
  }, [tracking])

  useEffect(() => {
    setTargetingTemp(targeting)
  }, [targeting])

  if (showGeoRedirectModal) return null

  // If cookie consent is disabled, render all scripts and analytics anyways.
  // (website ownwers are responsible for showing the cookie consent banner in this case).
  if (disabled || settings?.disabled) {
    return (
      <>
        {scripts.map(script => (
          <HtmlScript script={script} key={script.id} />
        ))}
        {analytics.enabled && <AnalyticsScript domain={analytics.domain} />}
      </>
    )
  }

  const hasFunctionalCookies =
    scripts.some(script => script.cookieType === 'functional') ||
    isOnesignalConfigured // If OneSignal is configured, it's considered a functional cookie
  const hasTrackingCookies = scripts.some(
    script => script.cookieType === 'tracking'
  )
  const hasTargetingCookies = scripts.some(
    script => script.cookieType === 'targeting'
  )

  return (
    <div
      className={`transition-colors duration-300 ease-in-out ${
        !showCookieConsent && (!userConsent || userConsent?.necessary)
          ? 'hidden'
          : 'fixed inset-0 z-max flex items-center justify-center bg-black bg-opacity-25 p-4 sm:p-6 md:p-8'
      }`}
    >
      <div className="flex max-h-screen max-w-2xl flex-col space-y-8 overflow-hidden overflow-y-auto rounded-lg bg-white p-8 shadow-2xl">
        <div className="space-y-4">
          <h4 className="font-bold text-2xl">{t('popupTitle')}</h4>
          <p>{t('popupDescription')}</p>

          <Accordion>
            <AccordionItem
              title={t('necessary')}
              description={t('necessaryHelp')}
              extra={<ButtonToggle active disabled />}
            />
            {hasFunctionalCookies && (
              <AccordionItem
                title={t('functional')}
                description={`${t('functionalHelp')}${isOnesignalConfigured ? ` ${t('onesignalCookies')}` : ''}`}
                extra={
                  <ButtonToggle
                    active={functionalTemp}
                    onChange={() => setFunctionalTemp(!functionalTemp)}
                  />
                }
              />
            )}
            {hasTrackingCookies && (
              <AccordionItem
                title={t('tracking')}
                description={t('trackingHelp')}
                extra={
                  <ButtonToggle
                    active={trackingTemp}
                    onChange={() => setTrackingTemp(!trackingTemp)}
                  />
                }
              />
            )}
            {hasTargetingCookies && (
              <AccordionItem
                title={t('targeting')}
                description={t('targetingHelp')}
                extra={
                  <ButtonToggle
                    active={targetingTemp}
                    onChange={() => setTargetingTemp(!targetingTemp)}
                  />
                }
              />
            )}
          </Accordion>
        </div>
        <div className="flex flex-col items-stretch justify-between space-y-4 tracking-wide md:flex-row md:space-x-4 md:space-y-0 rtl:md:space-x-reverse">
          <Button
            label={t('allowAll')}
            variant="primary"
            onClick={() => {
              updateUserConsent({
                necessary: true,
                functional: true,
                tracking: true,
                targeting: true,
              })
            }}
          />
          <Button
            label={t('allowSelected')}
            variant="primary"
            onClick={() => {
              updateUserConsent({
                necessary: true,
                functional: functionalTemp,
                tracking: trackingTemp,
                targeting: targetingTemp,
              })
            }}
          />
        </div>
      </div>

      {scripts
        .filter(({ cookieType }) =>
          cookieType === 'none' ? true : userConsent?.[cookieType] ?? false
        )
        .map(script => (
          <HtmlScript script={script} key={script.id} />
        ))}

      {analytics.enabled && <AnalyticsScript domain={analytics.domain} />}
    </div>
  )
}
CookieConsent.propTypes = {
  analytics: PropTypes.shape({
    enabled: PropTypes.bool,
    domain: PropTypes.string,
  }),
  scripts: PropTypes.array,
  settings: PropTypes.shape({
    disabled: PropTypes.bool,
    cookieText: PropTypes.object,
  }),
}

export function ShowCookieConsent() {
  const { t } = useTranslation('cookies')
  const { setShowCookieConsent, disabled } = useUserCookieConsent()

  // If cookie consent is disabled, don't render anything.
  if (disabled) return null

  return (
    <a
      href="#show-cookies-preferences"
      onClick={e => {
        e.preventDefault()
        setShowCookieConsent(true)
      }}
    >
      {t('preferences')}
    </a>
  )
}

function HtmlScript({ script }) {
  const renderedHtml = useCode(
    script.code,
    script.id,
    script.head ? 'beforeInteractive' : 'afterInteractive'
  )

  const codeNodes = Array.isArray(renderedHtml)
    ? renderedHtml
    : [{ ...renderedHtml, key: `script-node-${script.id}` }]

  return script.head ? (
    <>
      {/* Add all nodes that aren't `script` tags to the head directly */}
      <Head>{codeNodes.filter(node => node.type?.name !== 'Script')}</Head>

      {/* And add all `script` tags to the body, but as useCode converts them to Next's Script components and when the head flag is set, the Script's 'beforeInteractive' strategy will set them to the head anyways (Next's Scripts cannot be added into <Head>) */}
      <Fragment>
        {codeNodes.filter(node => node.type?.name === 'Script')}
      </Fragment>
    </>
  ) : (
    <Fragment>{codeNodes}</Fragment>
  )
}
HtmlScript.propTypes = {
  script: PropTypes.shape({
    code: PropTypes.string,
    id: PropTypes.string,
    head: PropTypes.bool,
  }),
}

function AnalyticsScript({ domain }) {
  if (!domain) return null

  return (
    <Script
      defer
      data-domain={domain}
      src="https://analytics.hopeplatform.org/js/plausible.js"
      strategy="afterInteractive"
    />
  )
}
AnalyticsScript.propTypes = {
  domain: PropTypes.string,
}
