import React from 'react'
import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))

/**
 * @param {Object} props
 * @param {string} [props.description] Description text
 * @param {string} props.icon Icon name to display
 * @param {string} props.label Label text to display
 * @returns {React.ReactComponent}
 */
function NoResultsFound({ description, icon, label }) {
  return (
    <div className="flex flex-col items-center justify-center gap-8 rounded-md py-16 bg-white dark:bg-primary-dark-700">
      <Icon
        name={icon}
        size="4rem"
        className="text-primary-600 dark:text-secondary-600"
      />
      <div className="flex flex-col items-center text-gray-800 dark:text-primary-dark-100">
        <p className="text-xl">{label}</p>
        {description ? <p className="text-base">{description}</p> : null}
      </div>
    </div>
  )
}

export default NoResultsFound
