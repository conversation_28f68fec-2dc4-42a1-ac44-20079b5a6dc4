import PropTypes from 'prop-types'
import React, { useEffect, useMemo, useState } from 'react'

export default function ProgressBar({ className = '', max = 100, value }) {
  const [percentage, setPercentage] = useState(0)

  useEffect(() => {
    const timer = setTimeout(() => {
      setPercentage(parseInt((value / max) * 100))
    }, 100) // delay of 100ms

    return () => clearTimeout(timer) // cleanup on unmount
  }, [value, max])

  const percentageStyle = useMemo(() => {
    return { width: `${percentage}%`, transition: 'width 0.5s' }
  }, [percentage])

  return (
    <div className={`flex bg-gray-300 h-4 rounded-md ${className}`}>
      <div
        className={`rounded-l-md ${
          value === max ? 'rounded-r-md' : ''
        } bg-secondary-500`}
        style={percentageStyle}
      />
    </div>
  )
}
ProgressBar.propTypes = {
  className: PropTypes.string,
  max: PropTypes.number,
  value: PropTypes.number,
}
