import PropTypes from 'prop-types'
import React, { useCallback, useEffect, useRef, useState } from 'react'

import dynamic from 'next/dynamic'

const LabelIcon = dynamic(() => import('ui/data-display/LabelIcon'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function Menu({ className = '', items = [] }) {
  return (
    <ul className={className}>
      {items.map((item, key) => (
        <MenuItem key={key} {...item} />
      ))}
    </ul>
  )
}
Menu.propTypes = {
  className: PropTypes.string,
  items: PropTypes.array,
}

export function MenuItem({
  active,
  children,
  className = '',
  icon,
  iconClass,
  labelClass,
  label,
  onClick,
  url,
}) {
  const expandRef = useRef()
  const [isOpen, setIsOpen] = useState(active)

  useEffect(() => {
    if (active && !isOpen && !expandRef.current) {
      setIsOpen(true)
      expandRef.current = true
    }
  }, [active, isOpen])

  const toggleSubItems = useCallback(() => {
    setIsOpen(!isOpen)
  }, [isOpen])

  const hasChildren = children?.length > 0

  return (
    <li className="flex flex-col">
      <div
        className={`-mx-2 -my-1 flex flex-row items-start justify-between space-x-2 rounded-md px-3 transition-colors duration-100 hover:bg-primary-100 rtl:space-x-reverse ${className}`}
      >
        {url ? (
          <Link
            className={`grow py-[0.65em] normal-case leading-tight text-lg ${
              active
                ? 'font-bold text-gray-900'
                : 'font-semibold text-primary-700'
            }`}
            to={url}
            onClick={onClick}
          >
            <LabelIcon
              labelClass={labelClass}
              iconClass={iconClass}
              label={label}
              icon={icon}
            />
          </Link>
        ) : (
          <Clickable
            className={`grow py-[0.65em] font-normal ${
              hasChildren ? 'cursor-pointer' : 'cursor-default'
            }`}
            onClick={toggleSubItems}
            disabled={!hasChildren}
          >
            <LabelIcon
              labelClass={labelClass}
              iconClass={iconClass}
              label={label}
              icon={icon}
            />
          </Clickable>
        )}

        {children?.length > 0 && (
          <button
            className="mt-[0.65em] select-none rounded-full p-1 text-gray-600 transition-colors duration-100 ease-in-out hover:bg-white hover:text-primary-800"
            onClick={toggleSubItems}
          >
            <Icon name={isOpen ? 'chevron-up' : 'chevron-down'} />
          </button>
        )}
      </div>

      {children?.length > 0 && isOpen && (
        <ul className="py-2 pl-2 mb-2">
          {children.map((child, i) => (
            <SubItem key={i} {...child} />
          ))}
        </ul>
      )}
    </li>
  )
}
MenuItem.propTypes = {
  active: PropTypes.bool,
  children: PropTypes.array,
  className: PropTypes.string,
  icon: PropTypes.string,
  iconClass: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  onClick: PropTypes.func,
  url: PropTypes.string,
}

function SubItem({
  active,
  className = '',
  icon = 'minus',
  iconClass = '',
  labelClass = '',
  label,
  marked,
  url,
  onClick,
}) {
  return url ? (
    <Link
      className={`flex rounded-md py-1 pl-2 transition-colors duration-100 ease-in-out hover:bg-gray-100 ${
        active ? 'cursor-default' : ''
      } ${className}`}
      to={url}
      onClick={onClick}
    >
      <Icon
        className={`mr-2 mt-[0.3em] text-base ${
          marked ? 'text-primary-700' : 'text-gray-400'
        } ${iconClass}`}
        name={icon}
      />
      <div
        className={`${
          active ? 'font-semibold text-gray-900' : 'text-primary-700'
        } ${labelClass}`}
      >
        {label}
      </div>
    </Link>
  ) : (
    <div className={`flex py-1 pl-2 ${className}`}>
      <Icon
        className={`mr-2 mt-1 text-base ${
          marked ? 'text-primary-700' : 'text-gray-500'
        } ${iconClass}`}
        name={icon}
      />
      <div className={labelClass}>{label}</div>
    </div>
  )
}
SubItem.propTypes = {
  active: PropTypes.bool,
  className: PropTypes.string,
  icon: PropTypes.string,
  iconClass: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  marked: PropTypes.bool,
  url: PropTypes.string,
  onClick: PropTypes.func,
}
