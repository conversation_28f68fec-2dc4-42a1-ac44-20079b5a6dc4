import PropTypes from 'prop-types'
import React, { useCallback, useMemo } from 'react'

import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { usePageContext } from 'components/PageProvider'
import useToggle from 'ui/helpers/useToggle'
import { useCloseNavOnClick } from 'ui/navigation/NavigationContext'
import { isSet } from 'utils/types'

const Badge = dynamic(() => import('ui/feedback/Badge'))
const Icon = dynamic(() => import('ui/icons/Icon'))

export default function Navigation({ className = '', children }) {
  return (
    <ul id="main-menu" role="navigation" className={className}>
      {children}
    </ul>
  )
}
Navigation.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node,
}

export function ItemLevel1({
  children,
  className = '',
  count,
  label,
  icon,
  url,
  variant,
  onClick,
  onClose,
}) {
  const hasChildren = !!children
  const [open, onToggle, setOpen] = useToggle(false)
  const { design } = usePageContext()
  const isLive = variant === 'isLive'

  const handleHover = useCallback(
    e => {
      setOpen(e.type === 'mouseenter')
    },
    [setOpen]
  )

  const handleClose = useCallback(() => {
    setOpen(false)
    if (typeof onClose === 'function') onClose()
  }, [setOpen, onClose])

  return (
    <>
      {isLive && (
        <div className="relative hidden h-1.5 lg:block">
          <div className="absolute h-1.5 w-1.5 animate-pulse rounded-full  ring-2 ring-danger-600/60"></div>
          <div className="absolute h-1.5 w-1.5 rounded-full bg-danger-600 "></div>
        </div>
      )}
      <li
        className={`w-full select-none px-6 py-2 lg:w-auto lg:px-3 xl:px-4 ${className}`}
        onMouseLeave={handleHover}
        onMouseEnter={handleHover}
      >
        <span className="flex flex-row items-center justify-between space-x-1 rtl:space-x-reverse">
          <div className="flex flex-row items-center justify-center space-x-3">
            <ItemLink
              className="font-bold text-2xl lg:text-base"
              count={count}
              href={url}
              onClick={onClick}
              icon={icon}
              onClose={handleClose}
            >
              {label}
            </ItemLink>

            {isLive && (
              <div className="relative h-1.5 lg:hidden">
                <div className="absolute h-1.5 w-1.5 animate-pulse rounded-full  ring-2 ring-danger-600/60"></div>
                <div className="absolute h-1.5 w-1.5 rounded-full bg-danger-600 "></div>
              </div>
            )}
          </div>

          {hasChildren && (
            <button onClick={onToggle} className="px-1">
              <Icon
                className={`transition-transform duration-300 ease-in-out text-sm md:block ${
                  open
                    ? '-rotate-180 text-primary-700 dark:text-gray-200'
                    : 'text-gray-500 dark:text-gray-400'
                }`}
                name="chevron-down"
              />
            </button>
          )}
        </span>

        {hasChildren && (
          <div
            className={
              open
                ? `mt-2 lg:absolute lg:mt-0 ${design?.fullWidth ? 'left-0 right-0' : 'left-10 right-10'}`
                : 'hidden'
            }
          >
            <div className="lg:border-b dark:border-gray-600 lg:pt-9" />
            <ul className="bg-white dark:bg-primary-dark-900/95 lg:flex lg:flex-row lg:flex-wrap lg:border-b-4 lg:border-secondary-500 dark:lg:border-secondary-400 lg:px-6 lg:py-6 xl:px-12">
              {React.Children.map(children, child =>
                React.cloneElement(child, { onClose: handleClose })
              )}
              {/* {children} */}
            </ul>
          </div>
        )}
      </li>
    </>
  )
}
ItemLevel1.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  count: PropTypes.number,
  icon: PropTypes.string,
  label: PropTypes.node,
  variant: PropTypes.oneOf(['isLive', '']),
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  url: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
}

export function ItemLevel2({
  children,
  className = '',
  count,
  label,
  url,
  variant,
  onClick,
  onClose,
}) {
  const isLive = variant === 'isLive'

  return (
    <li
      className={`w-full select-none space-y-4 px-6 py-2 lg:w-auto lg:px-3 xl:px-5 ${className}`}
    >
      <span className="flex flex-row items-center justify-between space-x-1 rtl:space-x-reverse">
        <ItemLink
          className="font-semibold text-lg lg:uppercase lg:text-base"
          onClick={onClick}
          count={count}
          onClose={onClose}
          href={url}
        >
          <div className="flex flex-row items-center justify-between rtl:space-x-reverse">
            {label}
            {isLive && (
              <div className="relative h-1.5 w-1.5 ltr:pl-3 rtl:pr-3">
                <div className="absolute h-1.5 w-1.5 animate-pulse rounded-full  ring-2 ring-danger-600/60 dark:ring-danger-400/60"></div>
                <div className="absolute h-1.5 w-1.5 rounded-full bg-danger-600 dark:bg-danger-500"></div>
              </div>
            )}
          </div>
        </ItemLink>
      </span>
      {children && (
        <ul className="space-y-1">
          {React.Children.map(children, child =>
            React.cloneElement(child, { onClose })
          )}
        </ul>
      )}
    </li>
  )
}
ItemLevel2.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  count: PropTypes.number,
  label: PropTypes.node,
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  url: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  variant: PropTypes.string,
}

export function ItemLevel3({
  children,
  className = '',
  count,
  label,
  url,
  variant,
  onClick,
  onClose,
}) {
  const isLive = variant === 'isLive'
  const hasChildren = !!children

  return (
    <li
      className={`w-full select-none px-6 py-2 lg:w-auto lg:px-0 lg:py-1 ${className}`}
    >
      <span className="flex flex-row items-center justify-between space-x-1 rtl:space-x-reverse">
        <ItemLink
          className="flex h-auto flex-row items-center justify-between space-x-1 text-base lg:uppercase lg:text-sm"
          count={count}
          onClick={onClick}
          onClose={onClose}
          href={url}
        >
          <div className="flex flex-row items-center justify-between  rtl:space-x-reverse">
            {label}
            {isLive && (
              <div className="relative h-1.5 w-1.5 ltr:pl-3 rtl:pr-3">
                <div className="absolute h-1.5 w-1.5 animate-pulse rounded-full ring-2 ring-danger-600/60 dark:ring-danger-400/60"></div>
                <div className="absolute h-1.5 w-1.5 rounded-full bg-danger-600 dark:bg-danger-500"></div>
              </div>
            )}
          </div>
        </ItemLink>
      </span>
      {hasChildren && (
        <ul className="space-y-1">
          {React.Children.map(children, child =>
            React.cloneElement(child, { onClose })
          )}
        </ul>
      )}
    </li>
  )
}
ItemLevel3.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  count: PropTypes.number,
  icon: PropTypes.string,
  label: PropTypes.node,
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  url: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  variant: PropTypes.string,
}

function ItemLink({
  children,
  className = '',
  count,
  icon,
  onClick,
  onClose,
  href,
}) {
  const router = useRouter()
  const active = useMemo(() => router.asPath === href, [router, href])

  const handleClick = useCloseNavOnClick({ href, onClick, onClose })
  const hrefIsEmpty = !isSet(href) || href?.trim().length === 0

  const activeClasses = active ? 'text-primary-700 dark:text-gray-100' : ''
  const linkedClasses =
    hrefIsEmpty && !onClick
      ? 'cursor-default'
      : 'hover:text-primary-700 dark:hover:text-white'

  return (
    <a
      className={`grow rounded leading-loose outline-none focus-visible:ring-2 focus-visible:ring-primary-500 dark:focus-visible:ring-gray-200 lg:justify-start uppercase ${activeClasses} ${linkedClasses} ${className}`}
      onClick={hrefIsEmpty && !onClick ? null : handleClick}
      href={hrefIsEmpty ? null : href}
      tabIndex={0}
    >
      <span className="flex flex-row items-center space-x-1 rtl:space-x-reverse">
        {icon && <Icon name={icon} />}
        <span className="whitespace-nowrap">{children}</span>
        {count > 0 && (
          <Badge
            className="bg-danger-500"
            labelClass="text-white"
            label={count}
            size="sm"
          />
        )}
      </span>
    </a>
  )
}
ItemLink.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  count: PropTypes.string,
  icon: PropTypes.string,
  onClick: PropTypes.func,
  onClose: PropTypes.func,
  href: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
}
