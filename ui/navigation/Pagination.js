import React, { useCallback } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useTranslation } from 'next-i18next'

import { usePageLoading } from 'ui/feedback/PageLoading'
import usePagination from 'ui/helpers/usePagination'
import { useIsSmallScreen } from 'ui/helpers/useBreakpoint'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Select = dynamic(() => import('ui/data-entry/Select').then(m => m.Select))

const pageSizes = [5, 10, 25, 50, 100]

const pageSizeOptions = pageSizes.map(x => ({
  label: `${x}`,
  value: x,
}))

export default function Pagination({
  itemsLabel,
  onPageChange,
  onPageSizeChange,
  perPageLabel,
  showPageSize,
  showResults,
  pageSize = 10,
  total = 0,
}) {
  const { t } = useTranslation()
  const router = useRouter()
  const { setPage } = usePageLoading()
  const page = parseInt(router.query.page) || 1
  const _pageSize = parseInt(router.query.limit) || pageSize
  const totalPages =
    parseInt(total / _pageSize) + (total % _pageSize > 0 ? 1 : 0)
  const pages = usePagination({
    page,
    total: totalPages,
    surrounding: 1,
    onPageClick: onPageChange,
  })

  const smallScreen = useIsSmallScreen()

  const isLastPage = page === totalPages
  const from = total < _pageSize ? 1 : (page - 1) * _pageSize + 1
  const to =
    total < _pageSize
      ? total
      : isLastPage
        ? total
        : (page - 1) * _pageSize + _pageSize

  const handlePageChange = useCallback(
    number => {
      if (typeof onPageChange === 'function') onPageChange(number)

      setPage(router.asPath)
    },
    [router, onPageChange, setPage]
  )

  const onPageSizeSelect = useCallback(
    ({ target }) => {
      const size = target.value
      if (typeof onPageSizeChange === 'function') onPageSizeChange(size)

      setPage(router.asPath)

      router.push({
        pathname: router.pathname,
        query: { ...router.query, limit: size },
      })
    },
    [router, onPageSizeChange, setPage]
  )

  const getPageHref = React.useCallback(
    number => {
      const query = { ...router.query, page: number }
      return { pathname: router.pathname, query }
    },
    [router]
  )

  if (totalPages <= 1 && !showPageSize && !showResults) return null

  return (
    <div
      className={`flex flex-col items-center ${
        showResults || showPageSize ? 'justify-between' : 'justify-end'
      } space-y-4 uppercase lg:flex-row lg:space-y-0`}
    >
      {showResults && !smallScreen && (
        <div className="flex items-center space-x-2 font-semibold rtl:space-x-reverse">
          {itemsLabel || t('items')}{' '}
          {t('paginationResults', { from, to, total })}
        </div>
      )}
      {totalPages > 1 && (
        <div className="flex flex-row items-center justify-center">
          {pages.map(({ isCurrent, isDivider, number, icon, label }, key) => (
            <Link
              className={`p-2 font-bold ${
                !isCurrent || isDivider
                  ? 'text-primary-700 dark:text-primary-dark-100'
                  : 'dark:text-gray-400'
              } ${
                isCurrent || isDivider ? 'cursor-default' : 'cursor-pointer'
              }`}
              onClick={
                isCurrent || isDivider ? null : () => handlePageChange(number)
              }
              href={isCurrent || isDivider ? '#' : getPageHref(number)}
              key={`page-${key}`}
            >
              {icon ? <Icon name={icon} /> : label || number}
            </Link>
          ))}
        </div>
      )}
      {showPageSize && !smallScreen && (
        <div className="flex flex-row items-center justify-center space-x-2 rtl:space-x-reverse">
          <span>{perPageLabel || t('perPage')}:</span>
          <Select
            onChange={onPageSizeSelect}
            options={pageSizeOptions}
            value={_pageSize}
          />
        </div>
      )}
    </div>
  )
}
Pagination.propTypes = {
  itemsLabel: PropTypes.string,
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func,
  pageSize: PropTypes.number,
  perPageLabel: PropTypes.string,
  showPageSize: PropTypes.bool,
  showResults: PropTypes.bool,
  total: PropTypes.number,
}
