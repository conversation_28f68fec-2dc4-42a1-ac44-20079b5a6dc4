import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function TextLink({
  className = '',
  children,
  external,
  label,
  onClick,
  to,
  underline,
}) {
  if (!to && !onClick) return children ? children : label

  return (
    <Link
      className={className}
      basic
      external={external}
      underline={underline}
      onClick={onClick}
      to={to}
    >
      {children || label}
      {external && <Icon className="pt-1 pl-1 text-sm" name="external-link" />}
    </Link>
  )
}
TextLink.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node,
  external: PropTypes.bool,
  label: PropTypes.string,
  onClick: PropTypes.func,
  to: PropTypes.string,
  underline: PropTypes.bool,
}
