import React, { useRef } from 'react'
import PropTypes from 'prop-types'
import clsx from 'clsx'
import { usePageContext } from 'components/PageProvider'
import useScrollPosition from 'hooks/useScrollPosition'

import AppearanceSwitch from 'ui/layout/AppearanceSwitch'
import { HEADER_HEIGHT_MD } from 'ui/layout/Header'

export default function TopMenu({ items, title, showAppearanceSwitch }) {
  const topMenuRef = useRef(null)
  const { design, darkMode } = usePageContext()
  const scrollPosition = useScrollPosition(topMenuRef)

  if (!Array.isArray(items)) return null

  const transparentHeader = design?.header?.transparent

  return (
    <div
      className={clsx(
        '',
        transparentHeader ? 'duration-100 transition-all' : '',
        !transparentHeader || scrollPosition > HEADER_HEIGHT_MD
          ? darkMode
            ? 'dark text-primary-dark-100'
            : 'bg-primary-800 text-primary-300'
          : ''
      )}
      ref={topMenuRef}
    >
      <div
        className={`flex h-8 w-full flex-row items-center justify-start space-x-8 overflow-x-scroll px-6 no-scrollbar rtl:space-x-reverse md:justify-end md:px-6 xl:px-12 ${design?.fullWidth ? '' : 'mx-auto max-w-screen-xl'}`}
      >
        {title && <p className="flex-shrink-0">{title}</p>}
        <ul className="flex flex-shrink-0 flex-row space-x-4 rtl:space-x-reverse lg:space-x-6">
          {items.map((item, index) => (
            <div
              key={`top-menu-item-${item.key}-${index}`}
              className="flex items-center space-x-2"
            >
              {item.variant === 'isLive' && (
                <div className="relative h-1.5 w-1.5">
                  <div className="absolute h-1.5 w-1.5 animate-pulse rounded-full  ring-2 ring-danger-600/60 dark:ring-danger-400/60"></div>
                  <div className="absolute h-1.5 w-1.5 rounded-full bg-danger-600 dark:bg-danger-500"></div>
                </div>
              )}
              <li
                className={`text-primary-100 ${transparentHeader && scrollPosition <= HEADER_HEIGHT_MD ? 'dark:text-white' : 'dark:text-gray-300/80'}`}
              >
                <a href={item.url} className="uppercase hover:text-white">
                  {item.label}
                </a>
              </li>
            </div>
          ))}
        </ul>
        {showAppearanceSwitch && <AppearanceSwitch />}
      </div>
    </div>
  )
}
TopMenu.propTypes = {
  items: PropTypes.array,
  title: PropTypes.string,
  showAppearanceSwitch: PropTypes.bool,
}
