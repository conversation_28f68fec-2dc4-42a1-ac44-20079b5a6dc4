import PropTypes from 'prop-types'
import React from 'react'

import {
  FloatingPortal,
  offset,
  shift,
  size as sizeMiddleware,
  useFloating,
} from '@floating-ui/react'
import { Combobox, Transition } from '@headlessui/react'
import clsx from 'clsx'
import dynamic from 'next/dynamic'

import { usePageContext } from 'components/PageProvider'
import useInputClasses from '../Field/useInputClasses'

const Icon = dynamic(() => import('ui/icons/Icon'))

/**
 * Basic Autocomplete component without Field
 */
export function Autocomplete({
  name,
  className = '',
  disabled,
  children,
  displayIcon,
  displayValue,
  onChange,
  onSearch,
  placeholder,
  value = '',
  variant,
  size,
  hasError,
}) {
  const { darkMode } = usePageContext()

  const inputClasses = useInputClasses({
    className,
    disabled,
    variant,
    hasError,
    size,
  })

  const { floatingStyles, refs } = useFloating({
    placement: 'bottom-start',
    transform: false,
    middleware: [
      shift(),
      offset(8),
      sizeMiddleware({
        apply({ availableWidth, elements, rects }) {
          Object.assign(elements.floating.style, {
            maxWidth: `${Math.min(availableWidth, rects.reference.width)}px`,
          })
        },
      }),
    ],
  })

  return (
    <Combobox name={name} value={value} onChange={onChange} disabled={disabled}>
      {({ open }) => (
        <>
          <div
            className="relative flex w-full flex-row items-center"
            ref={refs.setReference}
          >
            {displayIcon && (
              <Icon
                className="absolute h-10 w-10 border border-transparent p-2 text-gray-500 dark:text-gray-400"
                name={displayIcon}
              />
            )}
            <Combobox.Input
              autoComplete="off" // prevent browser's suggestions
              placeholder={placeholder}
              displayValue={displayValue}
              onChange={e => onSearch(e.target.value)}
              className={clsx(
                inputClasses,
                'pr-4',
                { 'pl-10': displayIcon },
                { 'pl-4': !displayIcon }
              )}
            />
          </div>
          <FloatingPortal id={name}>
            <div className={darkMode ? 'dark' : ''}>
              <Transition
                show={open && children?.length > 0}
                enter="transition duration-100 ease-out"
                enterFrom="transform scale-95 opacity-0"
                enterTo="transform scale-100 opacity-100"
                leave="transition duration-75 ease-out"
                leaveFrom="transform scale-100 opacity-100"
                leaveTo="transform scale-95 opacity-0"
                ref={refs.setFloating}
                style={floatingStyles}
                className={clsx(
                  `z-max max-h-64 w-full overflow-y-auto shadow-xl scrollbar-thin scrollbar-track-gray-50 scrollbar-thumb-primary-600 scrollbar-track-rounded-full scrollbar-thumb-rounded-full hover:cursor-pointer`,
                  inputClasses
                )}
              >
                <Combobox.Options static>{children}</Combobox.Options>
              </Transition>
            </div>
          </FloatingPortal>
        </>
      )}
    </Combobox>
  )
}
Autocomplete.propTypes = {
  name: PropTypes.string,
  className: PropTypes.string,
  children: PropTypes.node,
  disabled: PropTypes.bool,
  displayIcon: PropTypes.string,
  displayValue: PropTypes.func,
  onChange: PropTypes.func,
  onSearch: PropTypes.func,
  placeholder: PropTypes.node,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.bool,
  ]),
  hasError: PropTypes.bool,
  variant: PropTypes.oneOf([
    'white',
    'light',
    'dark',
    'rounded-light',
    'rounded-dark',
  ]),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
}
