import PropTypes from 'prop-types'
import React from 'react'

import Field from '../Field'
import { Autocomplete } from './Autocomplete'
import { AutocompleteOption } from './AutocompleteOption'
import { AutocompletePlaceholder } from './AutocompletePlaceholder'

export function AutocompleteField({
  children,
  className,
  disabled,
  displayIcon,
  displayValue,
  error,
  help,
  label,
  name,
  onChange,
  onSearch,
  placeholder,
  required,
  value,
  variant,
  size,
}) {
  return (
    <Field
      className={className}
      name={name}
      label={label}
      help={help}
      error={error}
      required={required}
      disabled={disabled}
    >
      <Autocomplete
        disabled={disabled}
        displayIcon={displayIcon}
        displayValue={displayValue}
        onChange={onChange}
        onSearch={onSearch}
        placeholder={placeholder}
        value={value}
        variant={variant}
        size={size}
      >
        {children}
      </Autocomplete>
    </Field>
  )
}
AutocompleteField.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  displayIcon: PropTypes.node,
  displayValue: PropTypes.func,
  error: PropTypes.object,
  help: PropTypes.string,
  label: PropTypes.string,
  name: PropTypes.string,
  onChange: PropTypes.func,
  onSearch: PropTypes.func,
  placeholder: PropTypes.node,
  required: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  variant: PropTypes.oneOf([
    'white',
    'light',
    'dark',
    'rounded-light',
    'rounded-dark',
  ]),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
}
AutocompleteField.Option = AutocompleteOption
AutocompleteField.Option.displayName = 'Autocomplete.Option'
AutocompleteField.Placeholder = AutocompletePlaceholder
AutocompleteField.Placeholder.displayName = 'Autocomplete.Placeholder'
