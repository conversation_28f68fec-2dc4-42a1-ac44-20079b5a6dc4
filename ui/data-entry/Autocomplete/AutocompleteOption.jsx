import PropTypes from 'prop-types'
import React from 'react'

import { Combobox } from '@headlessui/react'

import Icon from 'ui/icons/Icon'

/**
 * Autocomplete's Option
 */
export function AutocompleteOption({ value, label, disabled }) {
  return (
    <Combobox.Option value={value} disabled={disabled}>
      {({ active, selected }) => (
        <span
          className={`flex justify-between gap-4 py-1 px-4 ${
            active ? 'bg-primary-100 dark:bg-primary-dark-800' : ''
          } ${selected ? 'font-semibold' : ''} ${disabled ? 'opacity-75' : ''}`}
        >
          <span>{label}</span>
          <span className="text-primary-400">
            {selected && <Icon name="check" />}
          </span>
        </span>
      )}
    </Combobox.Option>
  )
}
AutocompleteOption.propTypes = {
  disabled: PropTypes.bool,
  label: PropTypes.node,
  selected: PropTypes.bool,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.bool,
    PropTypes.object,
  ]),
}
