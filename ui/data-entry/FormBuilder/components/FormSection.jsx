import PropTypes from 'prop-types'
import { useId } from 'react'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))

export function FormSection({ children, icon, title }) {
  const labelId = useId()

  return (
    <section
      role="group"
      aria-labelledby={labelId}
      className="mt-4 border-b border-neutral-200 pb-8"
    >
      <header className="mb-4 flex items-center space-x-2">
        {icon && (
          <Icon name={icon} className="text-primary-800 h-6 w-6 shrink-0" />
        )}
        <h3 id={labelId} className="text-lg font-bold">
          {title}
        </h3>
      </header>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-5">
        {children}
      </div>
    </section>
  )
}

FormSection.propTypes = {
  children: PropTypes.node,
  icon: PropTypes.string,
  title: PropTypes.string.isRequired,
}
