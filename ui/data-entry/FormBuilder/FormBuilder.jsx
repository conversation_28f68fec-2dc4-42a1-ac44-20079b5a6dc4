import PropTypes from 'prop-types'

import clsx from 'clsx'
import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const Input = dynamic(() => import('ui/data-entry/Input'))
const TextArea = dynamic(() => import('ui/data-entry/TextArea'))
const EmailField = dynamic(() => import('ui/data-entry/EmailField'))
const UsernameField = dynamic(() => import('ui/data-entry/UsernameField'))
const Checkbox = dynamic(() => import('ui/data-entry/Checkbox'))
const CheckboxGroup = dynamic(() => import('ui/data-entry/CheckboxGroup'))
const DateTime = dynamic(() => import('ui/data-entry/DateTime'))

const fieldsMap = {
  Input,
  TextArea,
  EmailField,
  UsernameField,
  Checkbox,
  DateTime,
  CheckboxGroup,
}

function typeOptionToProps(type, typeOption, options) {
  if (type === 'Checkbox') {
    return {
      lightLabel: true,
      value: typeOption || '',
    }
  }

  if (type === 'CheckboxGroup') {
    return {
      options,
      lightLabel: true,
    }
  }

  if (!typeOption) {
    return {}
  }

  if (type === 'Input' || type === 'DateTime') {
    return {
      type: typeOption,
    }
  }

  return {}
}
export function FormBuilder({
  fields = [],
  fieldsNamePath = [],
  maxColumns,
  error,
}) {
  const { t } = useTranslation()

  if (fields.length === 0) {
    return null
  }

  // Determine grid columns based on maxColumns prop or default values
  const gridColsClass = maxColumns
    ? `grid-cols-1 sm:grid-cols-2 md:grid-cols-${maxColumns}`
    : 'grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-5'

  // Map size to col-span based on maxColumns
  const getColSpanClass = size => {
    if (!maxColumns) {
      return clsx({
        'sm:col-span-1': size === 'xs',
        'sm:col-span-1 md:col-span-2': size === 'sm',
        'sm:col-span-2 md:col-span-3': size === 'md',
        'sm:col-span-2 md:col-span-4': size === 'lg',
        'sm:col-span-2 md:col-span-5': !size || size === 'xl',
      })
    }
    // Calculate col-span for custom maxColumns
    const colSpans = {
      xs: 1,
      sm: Math.max(1, Math.floor((maxColumns / 5) * 2)),
      md: Math.max(1, Math.floor((maxColumns / 5) * 3)),
      lg: Math.max(1, Math.floor((maxColumns / 5) * 4)),
      xl: maxColumns,
    }
    const span = colSpans[size] || maxColumns
    return `sm:col-span-1 md:col-span-${span}`
  }

  return (
    <div className={clsx('grid gap-4', gridColsClass)}>
      {fields.map(
        ({
          _id,
          name,
          label,
          help,
          placeholder,
          type,
          typeOption,
          size,
          required,
          options,
        }) => {
          const Field = fieldsMap[type]

          if (!Field) {
            return null
          }

          const namePath = [...fieldsNamePath, name].join('.')

          const getFieldError = (fieldName, error) => {
            const errorMap = {
              SMS: 'INVALID_PHONE',
              FIRSTNAME: 'INVALID_FIRSTNAME',
              LASTNAME: 'INVALID_LASTNAME',
              // Add more field-specific error mappings as needed
            }
            return error && error === errorMap[fieldName]
              ? errorMap[fieldName]
              : undefined
          }

          const fieldError = getFieldError(name, error)

          return (
            <Field
              {...typeOptionToProps(type, typeOption, options)}
              key={_id}
              name={namePath}
              label={label}
              help={help}
              placeholder={placeholder}
              required={required}
              className={getColSpanClass(size)}
              error={t(fieldError)}
            />
          )
        }
      )}
    </div>
  )
}

FormBuilder.propTypes = {
  fields: PropTypes.array,
  fieldsNamePath: PropTypes.array,
}
