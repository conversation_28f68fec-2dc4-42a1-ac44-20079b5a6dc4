import React from 'react'
import PropTypes from 'prop-types'

import { Controller, useFormContext } from 'react-hook-form'

export default function Hidden({ value, name }) {
  const { control } = useFormContext()

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      render={() => <input value={value} name={name} type="hidden" />}
    />
  )
}

Hidden.propTypes = {
  value: PropTypes.string,
  name: PropTypes.string.isRequired,
}
