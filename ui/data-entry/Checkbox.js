import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { Controller, useFormContext } from 'react-hook-form'

import Icon from 'ui/icons/Icon'

import useRules from './useRules'

const Label = dynamic(() => import('ui/data-entry/Label'))
const Field = dynamic(() => import('./Field'))

const variantClasses = {
  default: {
    wrapper: 'flex flex-row items-start space-x-3 rtl:space-x-reverse',
    wrapperChecked: '',
    wrapperUnchecked: '',
    input: 'cursor-pointer form-checkbox mt-1 border-gray-500 text-primary-700',
    label: '',
    iconChecked: '',
    iconUnchecked: '',
  },
  chip: {
    wrapper: 'border rounded-lg transition duration-200 bg-opacity-50',
    wrapperChecked: 'border-secondary-400 bg-secondary-300',
    wrapperUnchecked: 'border-gray-300 hover:bg-gray-50',
    input: 'hidden',
    label: 'px-4 py-3',
    iconChecked: 'text-secondary-500',
    iconUnchecked: 'text-gray-300',
  },
}

function getClassName(variant, destination) {
  const _variant = variantClasses[variant] || variantClasses.default
  return _variant[destination]
}

export function Checkbox({
  checked,
  className = '',
  id,
  lightLabel,
  name,
  required,
  label,
  variant = 'default',
  ...props
}) {
  const _id = id || name
  return (
    <div
      className={`cursor-pointer ${getClassName(variant, 'wrapper')} ${
        checked
          ? getClassName(variant, 'wrapperChecked')
          : getClassName(variant, 'wrapperUnchecked')
      } ${className}`}
    >
      <input
        id={_id}
        name={name}
        type="checkbox"
        className={getClassName(variant, 'input')}
        checked={checked}
        {...props}
      />
      <div className="flex space-x-2">
        <Label
          className={`leading-tight w-full ${getClassName(variant, 'label')}`}
          htmlFor={_id}
          lightLabel={lightLabel}
          text={label}
          required={required}
          prefix={
            getClassName(variant, 'iconChecked') && (
              <Icon
                name={checked ? 'square-xmark' : 'square'}
                className={
                  checked
                    ? getClassName(variant, 'iconChecked')
                    : getClassName(variant, 'iconUnchecked')
                }
                size={24}
              />
            )
          }
        />
      </div>
    </div>
  )
}
Checkbox.propTypes = {
  checked: PropTypes.bool,
  className: PropTypes.string,
  id: PropTypes.string,
  label: PropTypes.node,
  lightLabel: PropTypes.bool,
  name: PropTypes.string,
  onChange: PropTypes.func,
  required: PropTypes.bool,
  variant: PropTypes.oneOf(['default', 'chip']),
}

export function CheckboxField({
  className,
  disabled,
  error,
  help,
  id,
  label,
  labelClass,
  labelPrefix,
  lightLabel,
  name,
  onChange,
  required,
  value,
  variant,
}) {
  return (
    <Field
      className={className}
      error={error}
      extrasClass="ml-4"
      help={help}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
      name={name}
      required={required}
    >
      <Checkbox
        id={id}
        name={name}
        label={label}
        lightLabel={lightLabel}
        onChange={onChange}
        value={value}
        checked={!!value}
        disabled={disabled}
        required={required}
        variant={variant}
      />
    </Field>
  )
}
CheckboxField.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.object,
  help: PropTypes.string,
  id: PropTypes.string,
  label: PropTypes.node,
  labelPrefix: PropTypes.node,
  labelClass: PropTypes.string,
  lightLabel: PropTypes.bool,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  required: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  variant: PropTypes.oneOf(['default', 'chip']),
}

export default function CheckboxController({
  className,
  defaultValue = '',
  disabled,
  help,
  id,
  label,
  labelClass,
  labelPrefix,
  lightLabel,
  name,
  onChange,
  required,
  variant,
}) {
  const { control } = useFormContext()

  const rules = useRules({ required })

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field, fieldState }) => (
        <CheckboxField
          className={className}
          error={fieldState.error}
          extrasClass="ml-4"
          help={help}
          labelClass={labelClass}
          labelPrefix={labelPrefix}
          lightLabel={lightLabel}
          id={id}
          name={name}
          label={label}
          required={required}
          onChange={() => {
            field.onChange(!field.value)
            if (typeof onChange === 'function') onChange(!field.value)
          }}
          value={field.value}
          disabled={disabled}
          variant={variant}
        />
      )}
    />
  )
}
CheckboxController.propTypes = {
  className: PropTypes.string,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  disabled: PropTypes.bool,
  help: PropTypes.string,
  id: PropTypes.string,
  label: PropTypes.node,
  labelPrefix: PropTypes.node,
  labelClass: PropTypes.string,
  lightLabel: PropTypes.bool,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  required: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  variant: PropTypes.oneOf(['default', 'chip']),
}
