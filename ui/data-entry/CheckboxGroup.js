import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Checkbox = dynamic(() => import('./Checkbox'))
const Field = dynamic(() => import('./Field'))

export default function CheckboxGroup({
  className,
  disabled,
  error,
  help,
  label,
  labelClass,
  labelPrefix,
  name,
  options = [],
}) {
  const hasOptions = Array.isArray(options) && options?.length > 0

  return (
    <Field
      className={className}
      error={error}
      help={help}
      label={label}
      labelClass={labelClass}
      labelPrefix={labelPrefix}
    >
      <div className="space-y-2">
        {hasOptions
          ? options?.map((option, key) => (
              <Checkbox
                key={key}
                id={`${name}-${key}`}
                {...option}
                disabled={disabled}
                name={`${name}[${key}]`}
              />
            ))
          : null}
      </div>
    </Field>
  )
}
CheckboxGroup.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  help: PropTypes.string,
  label: PropTypes.node,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.node,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
    })
  ),
}
