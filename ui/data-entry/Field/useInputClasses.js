import clsx from 'clsx'

const baseClasses =
  'w-full border border-gray-400 focus:outline-none focus:border-primary-500 dark:focus:border-warning-300'
const whiteClasses =
  'bg-white dark:bg-primary-dark-700 text-gray-700 dark:text-primary-dark-100 text placeholder:text-gray-500'
const lightClasses =
  'bg-white text-gray-800 text placeholder:text-gray-200 focus:ring-primary-600'
const lightErrorClasses =
  'bg-danger-100 placeholder:text-danger-300 focus:ring-danger-400'
const darkClasses =
  'bg-gray-700 text-white placeholder:text-gray-300 focus:ring-primary-200 focus:ring-offset-gray-800'
const darkErrorClasses =
  'bg-danger-400 text-danger-50 placeholder:text-danger-200 focus:ring-danger-700'

const baseRoundedClasses = 'rounded-md'
const disabledClasses = 'cursor-not-allowed opacity-50'

const sizeClasses = {
  sm: 'py-2',
  md: 'py-3',
  lg: 'py-4 text-base',
}

const variantClasses = {
  white: whiteClasses,
  light: lightClasses,
  dark: darkClasses,
}

const variantErrorClasses = {
  white: lightErrorClasses,
  light: lightErrorClasses,
  dark: darkErrorClasses,
}

export default function useInputClasses({
  className,
  disabled = false,
  hasError = false,
  variant = 'white',
  size = 'md',
}) {
  return clsx(
    baseClasses,
    baseRoundedClasses,
    hasError ? variantErrorClasses[variant] : variantClasses[variant],
    sizeClasses[size],
    disabled ? disabledClasses : '',
    className
  )
}
