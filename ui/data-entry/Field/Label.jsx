import PropTypes from 'prop-types'
import React from 'react'

import clsx from 'clsx'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import { useFormVariant } from '../Form'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function Label({
  className = '',
  colon,
  htmlFor,
  prefix,
  required,
  text,
  variant,
  inline = false,
}) {
  const { t } = useTranslation()
  const { isDark } = useFormVariant(variant)

  if (!text) return null

  return (
    <label
      className={clsx(
        'flex flex-row items-center space-x-2 leading-6 rtl:space-x-reverse',
        isDark ? 'text-gray-200' : 'text-gray-700',
        className
      )}
      htmlFor={htmlFor}
    >
      <span>
        {prefix && <span className="mr-1">{prefix}</span>}
        <span className={clsx({ 'font-semibold': !inline })}>{text}</span>
        {colon && <span>:</span>}
      </span>

      {required && (
        <Icon
          className={clsx('h-3 w-3', {
            'text-danger-400': isDark,
            'text-danger-600': !isDark,
          })}
          name="asterisk"
          title={t('required')}
        />
      )}
    </label>
  )
}

Label.propTypes = {
  className: PropTypes.string,
  colon: PropTypes.bool,
  htmlFor: PropTypes.string,
  prefix: PropTypes.node,
  required: PropTypes.bool,
  text: PropTypes.node,
  t: PropTypes.func,
  variant: PropTypes.string,
  inline: PropTypes.bool,
}
