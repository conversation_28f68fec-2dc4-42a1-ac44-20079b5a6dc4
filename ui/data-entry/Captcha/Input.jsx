import React, { useCallback, useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

import generateCode from './helpers/generateCode'
import paintCaptcha from './helpers/paintCaptcha'

const Input = dynamic(() => import('ui/data-entry/Input').then(m => m.Input))
const Icon = dynamic(() => import('ui/icons/Icon'))

export default function CaptchaInput({
  captchaClass = '', // className of captcha image
  charsCount = 5, // count of characters that captcha should be made with
  className = '', // className of captcha and retry button container div
  height = 52, // height of captcha image
  onChange, // function that returns current shown captcha code
  placeholder = 'Enter captcha code', // text displayed when check input is empty
  retry = true, // whether captcha has retry functionality
  retryLabel, // label for the retry link
  width = 150, // width of captcha image
}) {
  const { t } = useTranslation()
  const [inputCode, setInputCode] = useState('')
  const [captchaCode, setCaptchaCode] = useState()
  const [isValid, setIsValid] = useState()
  const canvasRef = useRef()

  // Generate captcha code and paint it on canvas
  const generateCaptcha = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    // Generate captcha code
    const code = generateCode(charsCount)

    // Paint captcha code on canvas
    paintCaptcha(canvas, code, width, height)

    // Update captcha code state
    setCaptchaCode(code)
  }, [charsCount, width, height])

  // Generate captcha code on mount
  useEffect(() => {
    generateCaptcha()
  }, [generateCaptcha])

  // Reset captcha code when retry is clicked
  const resetCaptcha = useCallback(
    e => {
      e.preventDefault()
      // Generate new captcha code
      generateCaptcha()
    },
    [generateCaptcha]
  )

  // Check if input value matches captcha code
  const handleChange = useCallback(
    e => {
      // Get input value and convert to uppercase
      const value = `${e.target.value}`.toUpperCase()

      // Update input value
      setInputCode(value)

      // Check if input value matches captcha code
      const isValid = value === captchaCode

      // Update validity state
      setIsValid(isValid)

      // If onChange is a function,
      if (typeof onChange === 'function') {
        // call it with validity state
        onChange(isValid)
      }
    },
    [captchaCode, onChange]
  )

  return (
    <div className={`flex flex-col gap-4 justify-start ${className}`}>
      <div className="flex flex-row gap-4 items-start justify-start">
        <div className="flex flex-col gap-2">
          <div>
            <canvas
              className={`rounded-md border-2 border-secondary-500 bg-secondary-400 ${captchaClass}`}
              width={width}
              height={height}
              ref={canvasRef}
              style={{ pointerEvents: 'none' }}
            />
          </div>
          {retry && (
            <a
              className="flex items-center gap-2 cursor-pointer select-none text-primary-700 hover:underline"
              onClick={resetCaptcha}
              onKeyUp={resetCaptcha}
              role="link"
              tabIndex="-1"
            >
              <Icon name="arrow-rotate-right" />
              {retryLabel || t('captchaResetCode')}
            </a>
          )}
        </div>

        <div className="flex flex-row items-center gap-2">
          <Input
            onChange={handleChange}
            placeholder={placeholder}
            value={inputCode}
            className="text-xl h-14"
          />
          {isValid && (
            <div>
              <Icon
                className="flex w-10 h-10 items-center justify-center rounded-full bg-success-600 text-xl text-white"
                name="check"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
CaptchaInput.propTypes = {
  captchaClass: PropTypes.string,
  charsCount: PropTypes.number,
  className: PropTypes.string,
  width: PropTypes.number,
  height: PropTypes.number,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  retry: PropTypes.bool,
  retryLabel: PropTypes.string,
}
