import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useFormContext } from 'react-hook-form'

const Button = dynamic(() => import('ui/buttons/Button'))

const disabledModes = ['onBlur', 'onChange', 'onTouched']

export default function Submit({
  disabled,
  icon,
  iconClass = '',
  label = 'Submit',
  loading,
}) {
  const { formState, onSubmit, validationMode } = useFormContext()
  const { isValid, isSubmitting } = formState

  const isSubmitDisabled =
    disabled || isSubmitting
      ? true
      : disabledModes.includes(validationMode)
        ? !isValid
        : false

  return (
    <Button
      disabled={isSubmitDisabled}
      label={label}
      onClick={onSubmit}
      icon={loading ? 'spinner-third' : icon}
      iconClass={loading ? 'animate-spin' : iconClass}
      type="submit"
    />
  )
}
Submit.propTypes = {
  disabled: PropTypes.bool,
  icon: PropTypes.string,
  iconClass: PropTypes.string,
  label: PropTypes.string,
  loading: PropTypes.bool,
}
