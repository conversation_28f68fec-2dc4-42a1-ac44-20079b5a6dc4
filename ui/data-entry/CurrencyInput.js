import React, { useCallback, useRef, useState } from 'react'

import Field from './Field'
import { CurrencyFormat } from 'utils/currency'

export default function CurrencyInput({
  className,
  currency,
  label,
  labelClass,
  name,
  error,
  onChange,
  value = 0, // Expected in Cents
  required = false,
  showChecked = true,
  showRequired = true,
  maxAmount = 999999, // IMPORTANT: in Cents!
}) {
  const [hasFocus, setHasFocus] = useState(false)
  const inputRef = useRef()

  const _onChange = useCallback(
    e => {
      const text = e.target.value
      const amount = text ? parseInt(text, 10) : 0
      const valid = amount <= maxAmount

      if (valid) {
        onChange(amount) // Update form value
      }
    },
    [maxAmount, onChange]
  )

  const _onFocus = useCallback(() => {
    setHasFocus(true)

    if (inputRef.current) {
      const inputElement = inputRef.current
      inputElement.setSelectionRange(
        inputElement.value.length,
        inputElement.value.length
      )
    }
  }, [])

  const _setFocus = useCallback(
    event => {
      event.preventDefault()
      event.stopPropagation()

      inputRef?.current.focus()
    },
    [inputRef]
  )

  const hasValue = value > 0

  return (
    <Field
      error={error}
      hasFocus={hasFocus}
      hasValue={hasValue}
      label={label}
      labelClass={labelClass}
      required={required}
      showChecked={showChecked}
      showRequired={showRequired}
      onlyInput
    >
      <div
        className={`group flex flex-row items-center justify-start ${className}`}
      >
        <input
          className="w-0 text-transparent"
          pattern="[0-9]*"
          name={name}
          value={`${value}`}
          ref={inputRef}
          type="text"
          inputMode="numeric"
          onChange={_onChange}
          onFocus={_onFocus}
          maxLength={maxAmount.toString().length}
        />
        <button
          onClick={_setFocus}
          className={`h-11 w-full rounded border border-[#e6e6e6] bg-white shadow-sm transition-all duration-300 ease-in-out group-focus-within:border-primary-300 group-focus-within:ring-[3px] group-focus-within:ring-primary-100  `}
        >
          <div
            className={`rounded-t font-sans slashed-zero tabular-nums tracking-tighter transition-all duration-100 ease-in-out text-lg ${
              hasValue ? ' font-semibold ' : ' '
            }`}
          >
            <CurrencyFormat amount={value} currency={currency} />
          </div>
        </button>
      </div>
    </Field>
  )
}
