import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const Label = dynamic(() => import('./Label'))

const errorMessages = {
  required: 'VALIDATION_ERROR_REQUIRED', // indicates that the input must have a value before the form can be submitted (true)
  maxLength: 'VALIDATION_ERROR_INVALID_TOO_LONG', // The maximum length of the value to accept for this input (6).
  minLength: 'VALIDATION_ERROR_INVALID_TOO_SHORT', // The minimum length of the value to accept for this input (2).
  max: 'VALIDATION_ERROR_INVALID_TOO_BIG', // The maximum value to accept for this input (3).
  min: 'VALIDATION_ERROR_INVALID_TOO_SMALL', // The minimum value to accept for this input (3).
  pattern: 'VALIDATION_ERROR_INVALID_FORMAT', // The regex pattern for the input ('/[A-Za-z]{3}/').
  // validate: '', // A callback function as the argument to validate ((value) => value === 'test')
  error: 'VALIDATION_ERROR_INVALID',
}

function getErrorMessage(t, error) {
  if (!error) return

  const message =
    typeof error === 'string'
      ? error
      : error.message ??
        errorMessages[error.type ?? 'error'] ??
        errorMessages['error']

  return message.startsWith('VALIDATION_ERROR_') ? t(message) : message
}

export default function Field({
  children,
  className = '',
  error,
  extrasClass = '',
  help,
  innerClass = '',
  label,
  labelClass = '',
  labelPrefix,
  name,
  required,
}) {
  const { t } = useTranslation()

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label
          className={labelClass}
          htmlFor={name}
          prefix={labelPrefix}
          required={required}
          text={label}
        />
      )}
      <div className="space-y-1">
        <div className={innerClass}>{children}</div>
        <div className={`flex flex-col space-y-1 px-2 text-sm ${extrasClass}`}>
          {help && (
            <p className="italic text-gray-600 dark:text-primary-dark-100">
              {help}
            </p>
          )}
          {error && (
            <p className="font-semibold text-danger-600 dark:text-danger-400">
              {typeof error === 'object' ? getErrorMessage(t, error) : error}
            </p>
          )}
        </div>
      </div>
    </div>
  )
}
Field.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  extrasClass: PropTypes.string,
  help: PropTypes.string,
  innerClass: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  labelClass: PropTypes.string,
  labelPrefix: PropTypes.node,
  required: PropTypes.bool,
}
