import { useTranslation } from 'next-i18next'

import InputController from './Input'
import { usernameRegex } from './constants'

export default function UsernameField(props) {
  const { t } = useTranslation()

  return (
    <InputController
      {...props}
      maxLength={120}
      minLength={2}
      pattern={{
        value: usernameRegex,
        message: t('VALIDATION_ERROR_INVALID_USERNAME', {
          fieldName: props.name,
        }),
      }}
      type="text"
    />
  )
}
