import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { Controller, useFormContext } from 'react-hook-form'

import useRules from './useRules'

const Field = dynamic(() => import('./Field'))
const Label = dynamic(() => import('./Label'))

const positionStyles = {
  top: {
    wrapper: '',
    label: '',
    inner:
      'flex flex-row sm:flex-col-reverse items-center space-x-2 rtl:space-x-reverse sm:space-x-0 sm:space-y-2 sm:space-y-reverse',
  },
  bottom: {
    wrapper: 'flex flex-col',
    label: 'mt-2',
    inner: '',
  },
  default: {
    wrapper: '',
    label: 'ml-4',
    inner: 'flex items-center',
  },
}

export function RadioField({
  className = '',
  disabled,
  error,
  help,
  id,
  label,
  labelPosition = 'default',
  labelHidden,
  labelClass = '',
  labelPrefix,
  name,
  required,
  onChange,
  value,
  checked,
}) {
  const classes = positionStyles[labelPosition]
  const _id = id || name

  return (
    <Field
      className={`${classes.wrapper} ${className}`}
      error={error}
      help={help}
      innerClass={classes.inner}
      name={name}
      required={required}
    >
      <input
        className="form-radio cursor-pointer"
        disabled={disabled}
        id={_id}
        name={name}
        type="radio"
        checked={checked}
        value={value}
        onChange={onChange}
        required={required}
      />
      {label && !labelHidden && (
        <Label
          className={`leading-tight ${labelClass} ${classes.label}`}
          htmlFor={_id}
          labelPrefix={labelPrefix}
          required={required}
          text={label}
        />
      )}
    </Field>
  )
}
RadioField.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  help: PropTypes.string,
  id: PropTypes.string,
  label: PropTypes.string,
  labelClass: PropTypes.string,
  labelHidden: PropTypes.bool,
  labelPosition: PropTypes.oneOf(['top', 'bottom', 'default']),
  labelPrefix: PropTypes.node,
  name: PropTypes.string.isRequired,
  required: PropTypes.bool,
  t: PropTypes.func,
  value: PropTypes.string.isRequired,
}

export default function RadioController({
  defaultValue = '',
  value,
  name,
  required,
  ...rest
}) {
  const { control } = useFormContext()

  const rules = useRules({ required })

  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue || ''}
      render={({ field, fieldState }) => (
        <RadioField
          error={fieldState.error}
          onChange={field.onChange}
          checked={field.value === value}
          value={value}
          name={name}
          required={required}
          {...rest}
        />
      )}
    />
  )
}
RadioController.propTypes = RadioField.propTypes
