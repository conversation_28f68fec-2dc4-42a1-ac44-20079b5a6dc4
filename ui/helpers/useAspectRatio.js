import { useResponsiveClasses } from './useResponsiveClasses'

const aspectRatios = [
  '16/9', // aspect-16/9 sm:aspect-16/9 md:aspect-16/9 lg:aspect-16/9 xl:aspect-16/9 2xl:aspect-16/9
  '1/1', // aspect-1/1 sm:aspect-1/1 md:aspect-1/1 lg:aspect-1/1 xl:aspect-1/1 2xl:aspect-1/1
  '3/4', // aspect-3/4 sm:aspect-3/4 md:aspect-3/4 lg:aspect-3/4 xl:aspect-3/4 2xl:aspect-3/4
  '4/3', // aspect-4/3 sm:aspect-4/3 md:aspect-4/3 lg:aspect-4/3 xl:aspect-4/3 2xl:aspect-4/3
  '2/3', // aspect-2/3 sm:aspect-2/3 md:aspect-2/3 lg:aspect-2/3 xl:aspect-2/3 2xl:aspect-2/3
  '9/16', // aspect-9/16 sm:aspect-9/16 md:aspect-9/16 lg:aspect-9/16 xl:aspect-9/16 2xl:aspect-9/16
  'auto', // aspect-auto sm:aspect-auto md:aspect-auto lg:aspect-auto xl:aspect-auto 2xl:aspect-auto
].reduce((acc, ratio) => ({ ...acc, [ratio]: ratio }), {})

//TODO: Merge this function with getAspectRatioClass
export default function useAspectRatio(aspectRatio) {
  return useResponsiveClasses('aspect', aspectRatios, aspectRatio)
}
