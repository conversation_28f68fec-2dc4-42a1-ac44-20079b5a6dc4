import PropTypes from 'prop-types'
import React from 'react'

import clsx from 'clsx'

import { getBackgroundColor } from './getColor'

export default function Divider({ direction, color = 'gray-200' }) {
  return (
    <div
      className={clsx(getBackgroundColor(color), {
        'h-px w-full': direction === 'horizontal',
        'h-full w-px': direction === 'vertical',
      })}
    />
  )
}

Divider.propTypes = {
  color: PropTypes.string,
  direction: PropTypes.oneOf(['horizontal', 'vertical']),
}
