import { useMemo } from 'react'
import { useImageUrl } from 'ui/data-display/Image'

export default function useBackgroundImage(
  image,
  position = 'center',
  width = 1200 // TODO: Make this work with responive images
) {
  const bgImageUrl = useImageUrl(image, width)

  if (!positions.includes(position)) position = 'center'

  return useMemo(() => {
    if (!bgImageUrl) return null
    return {
      backgroundImage: `url(${bgImageUrl})`,
      backgroundPosition: position,
    }
  }, [bgImageUrl, position])
}

const positions = [
  'top left',
  'top',
  'top right',
  'center left',
  'center',
  'center right',
  'bottom left',
  'bottom',
  'bottom right',
]
