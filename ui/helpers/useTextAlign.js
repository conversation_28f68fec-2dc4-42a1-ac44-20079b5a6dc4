import { useMemo } from 'react'
import { getResponsiveClasses } from './getResponsiveClasses'

export default function useTextAlign(align, defaultAlign = 'left') {
  return useMemo(() => {
    const alignValue = typeof align === 'object' ? align : { xs: align }
    return getResponsiveClasses(
      'text',
      alignValue,
      alignments,
      defaultAlign,
      alignmentsMap
    )
  }, [align, defaultAlign])
}

// Possible values for text alignment
const alignments = ['left', 'center', 'right', 'justify']

// Uses logical values for text alignment (e.g. 'start' instead of 'left')
const alignmentsMap = {
  left: 'start', // text-start sm:text-start md:text-start lg:text-start xl:text-start
  center: 'center', // text-center sm:text-center md:text-center lg:text-center xl:text-center
  right: 'end', // text-end sm:text-end md:text-end lg:text-end xl:text-end
  justify: 'justify', // text-justify sm:text-justify md:text-justify lg:text-justify xl:text-justify
}
