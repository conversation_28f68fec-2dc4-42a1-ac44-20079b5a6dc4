import { useMemo } from 'react'
import { getResponsiveClasses } from './getResponsiveClasses'

export default function useTextSize(textSize, defaultSize = 'md') {
  return useMemo(
    () => getResponsiveClasses('text', textSize, textSizes, defaultSize),
    [textSize, defaultSize]
  )
}

const textSizes = [
  'xs', // text-xs sm:text-xs md:text-xs lg:text-xs xl:text-xs 2xl:text-xs
  'sm', // text-sm sm:text-sm md:text-sm lg:text-sm xl:text-sm 2xl:text-sm
  'base', // text-base sm:text-base md:text-base lg:text-base xl:text-base 2xl:text-base
  'md',
  'lg', // text-lg sm:text-lg md:text-lg lg:text-lg xl:text-lg 2xl:text-lg
  'xl', // text-xl sm:text-xl md:text-xl lg:text-xl xl:text-xl 2xl:text-xl
  '2xl', // text-2xl sm:text-2xl md:text-2xl lg:text-2xl xl:text-2xl 2xl:text-2xl
  '3xl', // text-3xl sm:text-3xl md:text-3xl lg:text-3xl xl:text-3xl 2xl:text-3xl
  '4xl', // text-4xl sm:text-4xl md:text-4xl lg:text-4xl xl:text-4xl 2xl:text-4xl
  '5xl', // text-5xl sm:text-5xl md:text-5xl lg:text-5xl xl:text-5xl 2xl:text-5xl
  '6xl', // text-6xl sm:text-6xl md:text-6xl lg:text-6xl xl:text-6xl 2xl:text-6xl
  '8xl', // text-8xl sm:text-8xl md:text-8xl lg:text-8xl xl:text-8xl 2xl:text-8xl
]
