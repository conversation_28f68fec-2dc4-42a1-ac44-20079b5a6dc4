export const bgColors = {
  'primary': 'bg-primary dark',
  'primary-50': 'bg-primary-50 dark',
  'primary-100': 'bg-primary-100 dark',
  'primary-200': 'bg-primary-200 dark',
  'primary-300': 'bg-primary-300 dark',
  'primary-400': 'bg-primary-400 dark',
  'primary-500': 'bg-primary-500 dark',
  'primary-600': 'bg-primary-600 dark',
  'primary-700': 'bg-primary-700 dark',
  'primary-800': 'bg-primary-800 dark',
  'primary-900': 'bg-primary-900 dark',

  'primary-dark': 'bg-primary-dark dark',
  'primary-dark-50': 'bg-primary-dark-50 dark',
  'primary-dark-100': 'bg-primary-dark-100 dark',
  'primary-dark-200': 'bg-primary-dark-200 dark',
  'primary-dark-300': 'bg-primary-dark-300 dark',
  'primary-dark-400': 'bg-primary-dark-400 dark',
  'primary-dark-500': 'bg-primary-dark-500 dark',
  'primary-dark-600': 'bg-primary-dark-600 dark',
  'primary-dark-700': 'bg-primary-dark-700 dark',
  'primary-dark-800': 'bg-primary-dark-800 dark',
  'primary-dark-900': 'bg-primary-dark-900 dark',

  'secondary': 'bg-secondary',
  'secondary-50': 'bg-secondary-50',
  'secondary-100': 'bg-secondary-100',
  'secondary-200': 'bg-secondary-200',
  'secondary-300': 'bg-secondary-300',
  'secondary-400': 'bg-secondary-400',
  'secondary-500': 'bg-secondary-500',
  'secondary-600': 'bg-secondary-600',
  'secondary-700': 'bg-secondary-700',
  'secondary-800': 'bg-secondary-800',
  'secondary-900': 'bg-secondary-900',

  'tertiary': 'bg-tertiary',
  'tertiary-50': 'bg-tertiary-50',
  'tertiary-100': 'bg-tertiary-100',
  'tertiary-200': 'bg-tertiary-200',
  'tertiary-300': 'bg-tertiary-300',
  'tertiary-400': 'bg-tertiary-400',
  'tertiary-500': 'bg-tertiary-500',
  'tertiary-600': 'bg-tertiary-600',
  'tertiary-700': 'bg-tertiary-700',
  'tertiary-800': 'bg-tertiary-800',
  'tertiary-900': 'bg-tertiary-900',

  'gray': 'bg-gray-400',
  'gray-50': 'bg-gray-50',
  'gray-100': 'bg-gray-100',
  'gray-200': 'bg-gray-200',
  'gray-300': 'bg-gray-300',
  'gray-400': 'bg-gray-400',
  'gray-500': 'bg-gray-500',
  'gray-600': 'bg-gray-600',
  'gray-700': 'bg-gray-700',
  'gray-800': 'bg-gray-800',
  'gray-900': 'bg-gray-900',

  'black': 'bg-black',
  'white': 'bg-white',
  'transparent': 'bg-transparent',
  'current': 'bg-current',
}

export const textColors = {
  'primary': 'text-primary dark', // dark:text-primary
  'primary-50': 'text-primary-50 dark', // dark:text-primary-50
  'primary-100': 'text-primary-100 dark', // dark:text-primary-100
  'primary-200': 'text-primary-200 dark', // dark:text-primary-200
  'primary-300': 'text-primary-300 dark', // dark:text-primary-300
  'primary-400': 'text-primary-400 dark', // dark:text-primary-400
  'primary-500': 'text-primary-500 dark', // dark:text-primary-500
  'primary-600': 'text-primary-600 dark', // dark:text-primary-600
  'primary-700': 'text-primary-700 dark', // dark:text-primary-700
  'primary-800': 'text-primary-800 dark', // dark:text-primary-800
  'primary-900': 'text-primary-900 dark', // dark:text-primary-900

  'primary-dark': 'text-primary-dark dark', // dark:text-primary-dark
  'primary-dark-50': 'text-primary-dark-50 dark', // dark:text-primary-dark-50
  'primary-dark-100': 'text-primary-dark-100 dark', // dark:text-primary-dark-100
  'primary-dark-200': 'text-primary-dark-200 dark', // dark:text-primary-dark-200
  'primary-dark-300': 'text-primary-dark-300 dark', // dark:text-primary-dark-300
  'primary-dark-400': 'text-primary-dark-400 dark', // dark:text-primary-dark-400
  'primary-dark-500': 'text-primary-dark-500 dark', // dark:text-primary-dark-500
  'primary-dark-600': 'text-primary-dark-600 dark', // dark:text-primary-dark-600
  'primary-dark-700': 'text-primary-dark-700 dark', // dark:text-primary-dark-700
  'primary-dark-800': 'text-primary-dark-800 dark', // dark:text-primary-dark-800
  'primary-dark-900': 'text-primary-dark-900 dark', // dark:text-primary-dark-900

  'secondary': 'text-secondary', // dark:text-secondary
  'secondary-50': 'text-secondary-50', // dark:text-secondary-50
  'secondary-100': 'text-secondary-100', // dark:text-secondary-100
  'secondary-200': 'text-secondary-200', // dark:text-secondary-200
  'secondary-300': 'text-secondary-300', // dark:text-secondary-300
  'secondary-400': 'text-secondary-400', // dark:text-secondary-400
  'secondary-500': 'text-secondary-500', // dark:text-secondary-500
  'secondary-600': 'text-secondary-600', // dark:text-secondary-600
  'secondary-700': 'text-secondary-700', // dark:text-secondary-700
  'secondary-800': 'text-secondary-800', // dark:text-secondary-800
  'secondary-900': 'text-secondary-900', // dark:text-secondary-900

  'tertiary': 'text-tertiary', // dark:text-tertiary
  'tertiary-50': 'text-tertiary-50', // dark:text-tertiary-50
  'tertiary-100': 'text-tertiary-100', // dark:text-tertiary-100
  'tertiary-200': 'text-tertiary-200', // dark:text-tertiary-200
  'tertiary-300': 'text-tertiary-300', // dark:text-tertiary-300
  'tertiary-400': 'text-tertiary-400', // dark:text-tertiary-400
  'tertiary-500': 'text-tertiary-500', // dark:text-tertiary-500
  'tertiary-600': 'text-tertiary-600', // dark:text-tertiary-600
  'tertiary-700': 'text-tertiary-700', // dark:text-tertiary-700
  'tertiary-800': 'text-tertiary-800', // dark:text-tertiary-800
  'tertiary-900': 'text-tertiary-900', // dark:text-tertiary-900

  'danger-50': 'text-danger-50', // dark:text-danger-50
  'danger-100': 'text-danger-100', // dark:text-danger-100
  'danger-200': 'text-danger-200', // dark:text-danger-200
  'danger-300': 'text-danger-300', // dark:text-danger-300
  'danger-400': 'text-danger-400', // dark:text-danger-400
  'danger-500': 'text-danger-500', // dark:text-danger-500
  'danger-600': 'text-danger-600', // dark:text-danger-600
  'danger-700': 'text-danger-700', // dark:text-danger-700
  'danger-800': 'text-danger-800', // dark:text-danger-800
  'danger-900': 'text-danger-900', // dark:text-danger-900

  'gray': 'text-gray-400',
  'gray-50': 'text-gray-50', // dark:text-gray-50
  'gray-100': 'text-gray-100', // dark:text-gray-100
  'gray-200': 'text-gray-200', // dark:text-gray-200
  'gray-300': 'text-gray-300', // dark:text-gray-300
  'gray-400': 'text-gray-400', // dark:text-gray-400
  'gray-500': 'text-gray-500', // dark:text-gray-500
  'gray-600': 'text-gray-600', // dark:text-gray-600
  'gray-700': 'text-gray-700', // dark:text-gray-700
  'gray-800': 'text-gray-800', // dark:text-gray-800
  'gray-900': 'text-gray-900', // dark:text-gray-900

  'black': 'text-black', // dark:text-black
  'white': 'text-white', // dark:text-white
  'transparent': 'text-transparent', // dark:text-transparent
  'current': 'text-current', // dark:text-current
}

export function getBackgroundColor(colorKey = '') {
  return bgColors[colorKey] || ''
}

export function getTextColor(color, fallbackColor = null) {
  return color ? textColors[color] : fallbackColor
}
