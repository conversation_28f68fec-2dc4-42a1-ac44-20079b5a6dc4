import { useState, useEffect } from 'react'
import throttle from 'lodash/throttle'

const config = {
  xs: 350,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
}

function getDeviceConfig(width) {
  if (!width || width < config.sm) {
    return 'xs'
  } else if (width >= config.sm && width < config.md) {
    return 'sm'
  } else if (width >= config.md && width < config.lg) {
    return 'md'
  } else if (width >= config.lg && width < config.xl) {
    return 'lg'
  } else if (width >= config.xl) {
    return 'xl'
  }
}

export default function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState()

  useEffect(() => {
    // set initial breakpoint
    if (!breakpoint) setBreakpoint(getDeviceConfig(window.innerWidth))

    // calculate breakpoint on resize
    const calcInnerWidth = throttle(function () {
      const _breakpoint = getDeviceConfig(window.innerWidth)
      if (_breakpoint !== breakpoint) {
        setBreakpoint(_breakpoint)
      }
    }, 200)
    window.addEventListener('resize', calcInnerWidth)
    return () => window.removeEventListener('resize', calcInnerWidth)
  }, [breakpoint])

  return breakpoint
}

export function useIsSmallScreen() {
  const breakpoint = useBreakpoint()
  return ['xs', 'sm', 'md'].includes(breakpoint)
}
