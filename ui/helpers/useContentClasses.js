import clsx from 'clsx'

import { useGridItemClasses } from './useGridItem'
import { useJustifySelf } from './useJustifySelf'

export function useContentClasses(props = {}, parent) {
  const gridItemClasses = useGridItemClasses(props, parent)
  const justifySelfClasses = useJustifySelf(props?.justifySelf)
  return clsx(props.className, props?.hideInPrint && 'print:hidden', gridItemClasses, justifySelfClasses)
}
