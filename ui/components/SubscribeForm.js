import React from 'react'
import PropTypes from 'prop-types'

export default function SubscribeForm({
  className = '',
  title = 'Sign up for our newsletter!',
  description = 'Stay up to date with events near you, new episodes of our hit series, and more.',
  onSubmit,
  emailPlaceholder = 'E-mail',
  signUpLabel = 'Sign up',
}) {
  return (
    <div className={`flex flex-col space-y-6  ${className}`}>
      <div className="space-y-4 text-base text-white">
        <h3 className="font-bold">{title}</h3>
        <p>{description}</p>
      </div>
      <form
        className="flex flex-col space-y-4 md:flex-row lg:flex-col xl:flex-row xl:space-y-0 xl:space-x-4 rtl:xl:space-x-reverse"
        onSubmit={onSubmit}
      >
        <input
          className="grow rounded-lg bg-white p-4 py-3 text-lg leading-loose text-gray-900 placeholder-gray-600"
          placeholder={emailPlaceholder}
          type="text"
        />

        <button
          className="rounded-lg border-2 px-4 py-3 font-bold uppercase text-white "
          type="submit"
        >
          {signUpLabel}
        </button>
      </form>
    </div>
  )
}
SubscribeForm.propTypes = {
  className: PropTypes.string,
  description: PropTypes.string,
  emailPlaceholder: PropTypes.string,
  onSubmit: PropTypes.func,
  signUpLabel: PropTypes.string,
  title: PropTypes.string,
}
