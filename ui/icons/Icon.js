import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import icons from './svg/index'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))

export default function Icon({ className = '', name, size, onClick, boxed }) {
  const IconComponent = icons[name]
  if (!IconComponent) return null

  const boxClass = boxed ? `rounded p-2` : ''

  return (
    <Clickable
      className={`items-center flex justify-center ${className} ${boxClass}`}
      onClick={onClick}
    >
      <IconComponent size={size} />
    </Clickable>
  )
}
Icon.propTypes = {
  className: PropTypes.string,
  boxed: PropTypes.bool,
  name: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
}
