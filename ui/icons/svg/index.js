import dynamic from 'next/dynamic'

export default {
  'app-store': dynamic(() => import('./AppStore')),
  'arrow-rotate-right': dynamic(() => import('./ArrowRotateRight')),
  'article': dynamic(() => import('./Article')),
  'asterisk': dynamic(() => import('./Asterisk')),
  'at': dynamic(() => import('./At')),
  'audio': dynamic(() => import('./Audio')),
  'baby': dynamic(() => import('./Baby')),
  'backward-step': dynamic(() => import('./BackwardStep')),
  'ban': dynamic(() => import('./Ban')),
  'bell': dynamic(() => import('./Bell')),
  'books': dynamic(() => import('./Books')),
  'birthday-cake': dynamic(() => import('./BirthdayCake')),
  'calendar': dynamic(() => import('./Calendar')),
  'campfire': dynamic(() => import('./Campfire')),
  'check': dynamic(() => import('./Check')),
  'chevron-down': dynamic(() => import('./ChevronDown')),
  'chevron-left': dynamic(() => import('./ChevronLeft')),
  'chevron-right': dynamic(() => import('./ChevronRight')),
  'chevron-up': dynamic(() => import('./ChevronUp')),
  'church': dynamic(() => import('./Church')),
  'circle': dynamic(() => import('./Circle')),
  'circle-check': dynamic(() => import('./CircleCheck')),
  'circle-dot': dynamic(() => import('./CircleDot')),
  'clock': dynamic(() => import('./Clock')),
  'right-from-bracket': dynamic(() => import('./RightFromBracket')),
  'download': dynamic(() => import('./Download')),
  'envelope': dynamic(() => import('./Envelope')),
  'envelope-open': dynamic(() => import('./EnvelopeOpen')),
  'expand-arrows': dynamic(() => import('./ExpandArrows')),
  'external-link': dynamic(() => import('./ExternalLink')),
  'face-awesome': dynamic(() => import('./FaceAwesome')),
  'facebook': dynamic(() => import('./Facebook')),
  'file-invoice': dynamic(() => import('./FileInvoice')),
  'flatbread': dynamic(() => import('./Flatbread')),
  'forward-step': dynamic(() => import('./ForwardStep')),
  'globe': dynamic(() => import('./Globe')),
  'google': dynamic(() => import('./Google')),
  'google-play': dynamic(() => import('./GooglePlay')),
  'heart': dynamic(() => import('./Heart')),
  'heart-filled': dynamic(() => import('./HeartFilled')),
  'hyphen': dynamic(() => import('./Hyphen')),
  'inbox': dynamic(() => import('./Inbox')),
  'info': dynamic(() => import('./Info')),
  'info-circle': dynamic(() => import('./InfoCircle')),
  'instagram': dynamic(() => import('./Instagram')),
  'language': dynamic(() => import('./Language')),
  'link': dynamic(() => import('./Link')),
  'list': dynamic(() => import('./List')),
  'lock': dynamic(() => import('./Lock')),
  'magnifying-glass': dynamic(() => import('./MagnifyingGlass')),
  'map-marker': dynamic(() => import('./MapMarker')),
  'moon-stars': dynamic(() => import('./MoonStars')),
  'page': dynamic(() => import('./Page')),
  'parking-circle': dynamic(() => import('./ParkingCircle')),
  'pause-solid': dynamic(() => import('./PauseSolid')),
  'people-dress': dynamic(() => import('./PeopleDress')),
  'play': dynamic(() => import('./Play')),
  'play-circle': dynamic(() => import('./PlayCircle')),
  'play-solid': dynamic(() => import('./PlaySolid')),
  'podcast': dynamic(() => import('./Podcast')),
  'route': dynamic(() => import('./Route')),
  'sda-logo-circular': dynamic(() => import('./SDALogoCircular')),
  'search': dynamic(() => import('./Search')),
  'shopping-basket': dynamic(() => import('./ShoppingBasket')),
  'sound-cloud': dynamic(() => import('./SoundCloud')),
  'spinner': dynamic(() => import('./Spinner')),
  'spinner-third': dynamic(() => import('./SpinnerThird')),
  'spotify': dynamic(() => import('./Spotify')),
  'stack': dynamic(() => import('./Stack')),
  'sun-bright': dynamic(() => import('./SunBright')),
  'square': dynamic(() => import('./Square')),
  'square-xmark': dynamic(() => import('./SquareXmark')),
  'telegram': dynamic(() => import('./Telegram')),
  'telephone': dynamic(() => import('./Telephone')),
  'tiktok': dynamic(() => import('./TikTok')),
  'times': dynamic(() => import('./Times')),
  'trash': dynamic(() => import('./Trash')),
  'trees': dynamic(() => import('./Trees')),
  'triangle-exclamation': dynamic(() => import('./TriangleExclamation')),
  'tv': dynamic(() => import('./Tv')),
  'twitter': dynamic(() => import('./Twitter')),
  'user': dynamic(() => import('./User')),
  'user-light': dynamic(() => import('./UserLight')),
  'user-group': dynamic(() => import('./UserGroup')),
  'user-group-light': dynamic(() => import('./UserGroupLight')),
  'video': dynamic(() => import('./Video')),
  'vimeo': dynamic(() => import('./Vimeo')),
  'volume': dynamic(() => import('./Volume')),
  'volume-xmark': dynamic(() => import('./VolumeXMark')),
  'whatsapp': dynamic(() => import('./Whatsapp')),
  'water': dynamic(() => import('./Water')),
  'wheat': dynamic(() => import('./Wheat')),
  'wheelchair': dynamic(() => import('./Wheelchair')),
  'wifi': dynamic(() => import('./WiFi')),
  'xmark': dynamic(() => import('./Xmark')),
  'youtube': dynamic(() => import('./Youtube')),
}
