import React from 'react'
import PropTypes from 'prop-types'

const style = {
  height: '1em',
  verticalAlign: '-0.125em',
  overflow: 'visible',
  boxSizing: 'content-box',
}

export default function SvgWrap({
  children,
  className = '',
  title = '',
  size,
  ...props
}) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      fill="currentColor"
      className={`inline-block ${className}`}
      style={size ? undefined : style}
      width={size}
      height={size}
      {...props}
    >
      {title && <title>{title}</title>}
      {children}
    </svg>
  )
}

SvgWrap.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  title: PropTypes.string,
  size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
}
