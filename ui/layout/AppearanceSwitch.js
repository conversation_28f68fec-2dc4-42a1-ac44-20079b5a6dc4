import React from 'react'
import PropTypes from 'prop-types'
import dynamic from 'next/dynamic'

import { usePageContext } from 'components/PageProvider'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function AppearanceSwitch({
  lightModeTextClass = 'text-primary-100',
}) {
  const { darkMode, toggleDarkMode } = usePageContext()
  return (
    <Icon
      className={darkMode ? 'text-secondary-400' : lightModeTextClass}
      onClick={toggleDarkMode}
      name={darkMode ? 'moon-stars' : 'sun-bright'}
      size={14}
    />
  )
}

AppearanceSwitch.propTypes = {
  lightModeTextClass: PropTypes.string,
}
