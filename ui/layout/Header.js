import PropTypes from 'prop-types'
import React, { useRef } from 'react'
import clsx from 'clsx'
import dynamic from 'next/dynamic'
import Link from 'next/link'

import defaultLogo from 'public/images/hope-channel-logo-color.svg'
import useScrollPosition from 'hooks/useScrollPosition'

import {
  useCloseNavOnClick,
  useNavigationContext,
} from 'ui/navigation/NavigationContext'
import { usePageContext } from 'components/PageProvider'
import AppearanceSwitch from './AppearanceSwitch'

const NavButton = dynamic(() => import('ui/buttons/NavButton'))
const Img = dynamic(() => import('ui/data-display/Image').then(m => m.Img))
const PageLoading = dynamic(() => import('ui/feedback/PageLoading'))

export const HEADER_HEIGHT_MD = 128

export default function Header({
  actions,
  logo,
  menu,
  mobileMenu,
  title,
  topMenu,
  showAppearanceSwitch,
}) {
  const headerRef = useRef(null)
  const { showNav, toggleNav } = useNavigationContext()
  const handleLogoClick = useCloseNavOnClick({ href: '/' })
  const { design, darkMode } = usePageContext()

  const scrollPosition = useScrollPosition(headerRef)

  const transparentHeader = design?.header?.transparent

  return (
    <header
      className={clsx(
        'fixed top-0 left-0 right-0 z-max',
        transparentHeader ? 'duration-300 transition-colors' : '',
        transparentHeader && scrollPosition <= HEADER_HEIGHT_MD
          ? 'bg-transparent dark text-gray-200'
          : '',
        !transparentHeader || scrollPosition > HEADER_HEIGHT_MD
          ? `shadow-sm backdrop-blur-md ${
              darkMode
                ? 'dark text-gray-200 bg-primary-dark-900/95'
                : 'bg-white text-black [@supports(backdrop-filter:blur(0px))]:bg-opacity-90'
            }`
          : ''
      )}
      role="banner"
      ref={headerRef}
    >
      <PageLoading />
      {topMenu}
      <div
        className={`relative flex h-16 w-full flex-row items-center justify-between py-4 lg:h-24 lg:py-3 lg:px-6 xl:px-12 ${design?.fullWidth ? '' : 'mx-auto max-w-screen-xl'}`}
      >
        <div className="shrink-0 pl-6 lg:pl-0">
          <Link
            href="/"
            title={title}
            className="block"
            onClick={handleLogoClick}
          >
            <Img
              alt={title}
              className="h-10 lg:h-12"
              file={typeof logo === 'object' ? logo : undefined}
              src={typeof logo === 'string' ? logo : defaultLogo}
            />
          </Link>
        </div>

        <div
          className={`hidden justify-end rtl:mr-0 lg:flex lg:flex-grow lg:flex-row ${actions ? 'lg:mr-6 rtl:lg:ml-6' : ''}`}
        >
          {menu}
        </div>

        <div className="flex shrink-0 flex-row items-center space-x-3 rtl:space-x-reverse md:space-x-5">
          {actions}
          <NavButton
            active={showNav}
            className="cursor-pointer pr-6 lg:hidden"
            onClick={toggleNav}
          />
          {showAppearanceSwitch && (
            <AppearanceSwitch lightModeTextClass="text-primary-500" />
          )}
        </div>
      </div>

      <div className={showNav ? 'border-t' : 'hidden'}>{mobileMenu}</div>
      {/* <ScreenSizes /> */}
    </header>
  )
}
Header.propTypes = {
  actions: PropTypes.node,
  logo: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
  menu: PropTypes.node,
  mobileMenu: PropTypes.node,
  title: PropTypes.string,
  topMenu: PropTypes.node,
  showAppearanceSwitch: PropTypes.bool,
}

// function ScreenSizes() {
//   return (
//     <div className="absolute top-0 rounded-b-lg left-4 p-2 bg-danger-800/80 text-white flex justify-center gap-4">
//       <div className="hidden xs:block">
//         <div className="flex items-center flex-col">
//           <div className="text-xl font-bold">XS</div>
//           <div className="font-normal text-base">350px</div>
//         </div>
//       </div>
//       <div className="hidden sm:block">
//         <div className="flex items-center flex-col">
//           <div className="text-xl font-bold">SM</div>
//           <div className="font-normal text-base">640px</div>
//         </div>
//       </div>
//       <div className="hidden md:block">
//         <div className="flex items-center flex-col">
//           <div className="text-xl font-bold">MD</div>
//           <div className="font-normal text-base">768px</div>
//         </div>
//       </div>
//       <div className="hidden lg:block">
//         <div className="flex items-center flex-col">
//           <div className="text-xl font-bold">LG</div>
//           <div className="font-normal text-base">1024px</div>
//         </div>
//       </div>
//       <div className="hidden xl:block">
//         <div className="flex items-center flex-col">
//           <div className="text-xl font-bold">XL</div>
//           <div className="font-normal text-base">1280px</div>
//         </div>
//       </div>
//       <div className="hidden 2xl:block">
//         <div className="flex items-center flex-col">
//           <div className="text-xl font-bold">2XL</div>
//           <div className="font-normal text-base">1536px</div>
//         </div>
//       </div>
//     </div>
//   )
// }
