import PropTypes from 'prop-types'
import React, { Fragment } from 'react'

import { Dialog, Transition } from '@headlessui/react'
import { useTranslation } from 'next-i18next'

import Button from 'ui/buttons/Button'

import Image from '../Image'

export default function PictureDialog({
  file,
  onClose,
  open,
  pictureAlt,
  src,
}) {
  const { t } = useTranslation()

  return (
    <Transition
      show={open}
      enter="transition duration-100 ease-out"
      enterFrom="transform scale-95 opacity-0"
      enterTo="transform scale-100 opacity-100"
      leave="transition duration-75 ease-out"
      leaveFrom="transform scale-100 opacity-100"
      leaveTo="transform scale-95 opacity-0"
      as={Fragment}
    >
      <Dialog
        className="fixed inset-0 z-max flex h-full w-full items-center justify-center px-2 sm:px-4 pt-12 pb-2 sm:pb-4 lg:px-8 lg:pb-8"
        onClose={onClose}
      >
        <div className="absolute inset-0 bg-black/80" />
        <Dialog.Panel
          as="figure"
          className="relative flex h-full w-full justify-center items-center overflow-hidden"
        >
          <Image
            alt={pictureAlt}
            src={src}
            file={file}
            className="max-w-full max-h-full"
            objectFit="contain"
          />
        </Dialog.Panel>
        <Button
          title={t('close')}
          size="lg"
          variant="tertiary"
          icon="times"
          onClick={onClose}
          className="absolute top-0 right-0 text-white"
        />
      </Dialog>
    </Transition>
  )
}

PictureDialog.propTypes = {
  file: PropTypes.object,
  onClose: PropTypes.func,
  open: PropTypes.bool,
  pictureAlt: PropTypes.string,
  src: PropTypes.string,
}
