import React, { Fragment, useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { useTranslation } from 'next-i18next'

import PictureDialog from './PictureDialog'
import { ConditionalLink } from 'ui/navigation/Link'

const Button = dynamic(() => import('ui/buttons/Button'))
const Image = dynamic(() => import('ui/data-display/Image'))
const Clickable = dynamic(() => import('ui/helpers/Clickable'))

const alignments = {
  'none': '',
  'left': 'w-full mr-0 sm:w-1/2 sm:mr-8 sm:float-left md:w-1/3',
  'right': 'w-full mr-0 sm:w-1/2 sm:ml-8 sm:float-right md:w-1/3',
  'center-full': 'w-full mx-0', // TODO: is this needed?
  'center-medium': 'w-full sm:w-4/5 md:w-2/3 mx-auto',
}

const transformOrigin = {
  center: 'origin-center',
  left: 'origin-left',
  right: 'origin-right',
}

export default function Picture({
  alt,
  align = 'none',
  aspectRatio,
  caption,
  className = '',
  copyright,
  clickToEnlarge,
  file,
  hasCustomWidth,
  id,
  imageClass = '',
  innerClassName = '',
  onClick,
  rounded = true,
  roundedTop,
  roundedBottom,
  enableAnimation,
  animationOrigin = 'center',
  sizes,
  src,
  transparent,
  url,
}) {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const hasDescription = caption || copyright
  const pictureAlt = alt || caption || copyright

  const transparentClass = transparent
    ? ''
    : 'shadow bg-gray-50 dark:bg-primary-dark-800'

  const widthClass = hasCustomWidth ? '' : 'w-full'

  const alignmentClass = alignments[align] || ''

  const roundedClass = transparent
    ? ''
    : roundedTop
      ? 'rounded-t-md'
      : roundedBottom
        ? 'rounded-b-md'
        : rounded
          ? 'rounded-md'
          : ''

  const onToggle = useCallback(() => {
    if (clickToEnlarge) {
      setOpen(!open)
    }
  }, [clickToEnlarge, open])

  const PictureWrapper = ['left', 'right'].includes(align) ? 'div' : Fragment

  return (
    <PictureWrapper>
      <Clickable
        as="figure"
        className={`relative flex flex-col justify-center ${widthClass} ${alignmentClass} ${
          enableAnimation
            ? `transition-all duration-200 hover:scale-105 ${transformOrigin[animationOrigin]}`
            : ''
        } ${className}`}
        id={id}
        onClick={clickToEnlarge ? onToggle : onClick}
      >
        <div
          className={`group relative flex w-full flex-col overflow-hidden ${transparentClass} ${roundedClass} ${innerClassName}`}
          id={id}
        >
          <ConditionalLink to={url} condition={url}>
            <div className="overflow-hidden">
              <Image
                className={`w-full ${
                  enableAnimation
                    ? 'hover:scale-105 duration-300 transition-all'
                    : ''
                } ${imageClass}`}
                alt={pictureAlt}
                aspectRatio={aspectRatio}
                src={src}
                file={file}
                sizes={sizes}
              />
            </div>
          </ConditionalLink>

          {(hasDescription || clickToEnlarge) && (
            <div
              className={`flex items-center justify-between ${
                transparent
                  ? 'absolute bottom-0 left-0 right-0 translate-y-full bg-gradient-to-b from-transparent to-gray-800 pt-4 transition-transform group-hover:translate-y-0'
                  : 'relative'
              }`}
            >
              {hasDescription ? (
                <div className="px-4 py-3 tracking-wide">
                  {caption && (
                    <div
                      className={`font-semibold text-sm md:text-base ${
                        transparent
                          ? 'text-white'
                          : 'text-gray-800 dark:text-gray-400'
                      }`}
                    >
                      {caption}
                    </div>
                  )}
                  {copyright && (
                    <div className="text-gray-500 text-xs md:text-sm">
                      {copyright}
                    </div>
                  )}
                </div>
              ) : (
                <div />
              )}
              {clickToEnlarge && (
                <Button
                  title={t('enlarge')}
                  size="lg"
                  variant="tertiary"
                  icon="expand-arrows"
                />
              )}
            </div>
          )}
        </div>
      </Clickable>
      <PictureDialog
        caption={caption}
        copyright={copyright}
        file={file}
        onClose={() => setOpen(false)}
        open={clickToEnlarge ? open : false}
        pictureAlt={pictureAlt}
        src={src}
      />
    </PictureWrapper>
  )
}
Picture.propTypes = {
  alt: PropTypes.string,
  align: PropTypes.oneOf([
    '',
    'none',
    'left',
    'right',
    'center-full',
    'center-medium',
  ]),
  aspectRatio: PropTypes.string,
  caption: PropTypes.string,
  className: PropTypes.string,
  clickToEnlarge: PropTypes.bool,
  copyright: PropTypes.string,
  file: PropTypes.object,
  hasCustomWidth: PropTypes.bool,
  id: PropTypes.string,
  imageClass: PropTypes.string,
  innerClassName: PropTypes.string,
  onClick: PropTypes.func,
  rounded: PropTypes.bool,
  roundedBottom: PropTypes.bool,
  roundedTop: PropTypes.bool,
  src: PropTypes.string,
  sizes: PropTypes.string,
  transparent: PropTypes.bool,
  url: PropTypes.string,
}
