import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Image = dynamic(() => import('./Image'))

export default function List({ className = '', children, title }) {
  return (
    <div className="space-y-4">
      {title && (
        <h3 className="font-bold uppercase leading-loose text-lg xs:block">
          {title}
        </h3>
      )}
      <div className={`space-y-6 ${className}`}>{children}</div>
    </div>
  )
}
List.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node,
  title: PropTypes.string,
}

export function ListItem({
  title,
  description,
  extras,
  image,
  imageAlt,
  imageAspectRatio,
  kicker,
}) {
  return (
    <div className="flex flex-col justify-between space-y-4 rounded-lg bg-white p-6 dark:bg-gray-700 sm:flex-row-reverse sm:space-x-6 sm:space-y-0 ltr:sm:space-x-reverse">
      {image ? (
        <div className="block w-full shrink-0 sm:w-32 md:w-48">
          <Image
            className="rounded"
            alt={imageAlt}
            file={image}
            sizes="md:192px sm:128px 350px"
            aspectRatio={imageAspectRatio}
          />
        </div>
      ) : (
        <div />
      )}
      <div className="space-y-3">
        <div className="space-y-1">
          {kicker && (
            <p className="font-semibold text-gray-700 text-sm dark:text-gray-300">
              {kicker}
            </p>
          )}
          <h3 className="font-semibold text-xl">{title}</h3>
        </div>
        {description && (
          <div className="text-gray-700 dark:text-primary-dark-100">
            {description}
          </div>
        )}
        {extras && <p className="text-gray-700">{extras}</p>}
      </div>
    </div>
  )
}
ListItem.propTypes = {
  title: PropTypes.node,
  description: PropTypes.node,
  extras: PropTypes.node,
  image: PropTypes.object,
  imageAlt: PropTypes.string,
  imageAspectRatio: PropTypes.string,
  kicker: PropTypes.node,
}
