import PropTypes from 'prop-types'
import { useCallback, useMemo, useRef } from 'react'

import clsx from 'clsx'
import Button from 'ui/buttons/Button'
import CookieBlockedContent from 'ui/feedback/CookieBlockedContent'
import { Dialog, DialogContent } from 'ui/feedback/FloatingDialog'

import { AutoplayNext } from './components/AutoplayNext'
import { PlayerMedia } from './components/PlayerMedia'
import { Poster } from './components/Poster'
import { usePlayer } from './hooks/usePlayer'
import { providers } from './utils/getPlayerUrl'

/**
 * ui/Player Component
 * @param {*} props
 */
export default function Player({
  accountId,
  autoplay,
  className = '',
  controls,
  ctaClass = '',
  ctaIcon,
  ctaLabel,
  ctaSize,
  ctaVariant,
  endsAt,
  fullSize,
  id,
  loop,
  muted,
  playerId,
  playIcon,
  playIconColor,
  playIconSize,
  playlist,
  playlistIndex,
  poster,
  provider = 'youtube',
  showPosterOverlay: initialShowPosterOverlay = false,
  sources,
  startsAt,
  title,
  variant = 'inline',
  onLightboxOpen,
  onLightboxClose,
}) {
  const embedRef = useRef(null)

  const {
    type,
    error,
    url,
    isSelfHosted,
    countDown,
    showNextEpisodeState,
    playNextEpisode,
    cancelPlayNextEpisode,
    showPosterOverlay,
    onPosterPlay,
    isDialogOpen,
    setIsDialogOpen,
    onCloseDialog,
  } = usePlayer({
    accountId,
    autoplay:
      autoplay ||
      variant === 'lightboxPoster' ||
      (variant === 'lightboxButton' && !initialShowPosterOverlay),
    controls,
    endsAt,
    id,
    initialShowPosterOverlay,
    loop,
    muted,
    playerId,
    embedRef,
    playlist,
    playlistIndex,
    poster,
    provider,
    sources,
    startsAt,
    variant,
  })

  const showPoster = variant !== 'lightboxPoster' && showPosterOverlay

  const player = useMemo(
    () => (
      <div
        className={clsx({
          'h-0 w-full rounded-md pt-[56.25%] supports-[aspect-ratio]:aspect-video supports-[aspect-ratio]:h-full supports-[aspect-ratio]:pt-0':
            variant !== 'background',
          'h-full w-full': variant === 'background',
          'bg-primary-dark-700': variant === 'inline',
          'bg-black': variant !== 'inline',
          'relative': showPoster,
        })}
        style={
          fullSize || variant === 'inline'
            ? undefined
            : {
                maxHeight: 'calc(100vh - 7.5rem)', // 7.5rem is the height of the header and padding
                maxWidth: 'calc(100vw - 3rem)', // 3rem is the width of the side padding
              }
        }
      >
        {['cloudflare', 'hls', 'mp4-hd', 'mp4-sd'].includes(provider) ? (
          <PlayerMedia
            error={error}
            isSelfHosted={isSelfHosted}
            url={url}
            type={type}
            showPosterOverlay={showPoster}
            provider={provider}
            id={id}
            title={title}
            ref={embedRef}
          />
        ) : (
          <div className="absolute left-0 top-0 h-full w-full rounded-md supports-[aspect-ratio]:relative supports-[aspect-ratio]:left-auto supports-[aspect-ratio]:top-auto">
            <CookieBlockedContent
              blockCondition={`Player-${provider}`}
              typeCondition="tracking"
              className="flex h-full w-full flex-col items-center justify-center"
              buttonSize="sm"
            >
              <PlayerMedia
                error={error}
                isSelfHosted={isSelfHosted}
                url={url}
                type={type}
                showPosterOverlay={showPoster}
                provider={provider}
                id={id}
                title={title}
                ref={embedRef}
              />
            </CookieBlockedContent>
          </div>
        )}
        {showPoster && (
          <Poster
            fillContainer={true}
            poster={poster}
            provider={provider}
            playIcon={playIcon}
            playIconSize={playIconSize}
            playIconColor={playIconColor}
            onClick={onPosterPlay}
          />
        )}
        <AutoplayNext
          state={showNextEpisodeState}
          countDown={countDown}
          playNext={playNextEpisode}
          cancel={cancelPlayNextEpisode}
        />
      </div>
    ),
    [
      provider,
      id,
      title,
      poster,
      playIcon,
      playIconSize,
      playIconColor,
      fullSize,
      variant,
      error,
      isSelfHosted,
      url,
      type,
      showPoster,
      embedRef,
      showNextEpisodeState,
      countDown,
      onPosterPlay,
      playNextEpisode,
      cancelPlayNextEpisode,
    ]
  )

  const handleOnDialogOpen = useCallback(() => {
    setIsDialogOpen(true)
    onLightboxOpen && onLightboxOpen()
  }, [onLightboxOpen, setIsDialogOpen])

  const handleOnDialogClose = useCallback(() => {
    onCloseDialog()
    onLightboxClose && onLightboxClose()
  }, [onLightboxClose, onCloseDialog])

  return (
    <div
      id={id}
      className={clsx(
        variant !== 'lightboxButton' && 'relative h-full w-full',
        className
      )}
    >
      {variant === 'lightboxButton' && (
        <Dialog open={isDialogOpen} onOpenChange={handleOnDialogClose}>
          <Button
            className={ctaClass}
            label={ctaLabel}
            size={ctaSize}
            variant={ctaVariant}
            icon={ctaIcon}
            onClick={handleOnDialogOpen}
          />
          <DialogContent
            overlayClass="bg-black/90"
            boxClass="w-screen max-w-[100vw] max-h-[100vh]"
            transparent
          >
            {isDialogOpen && player}
          </DialogContent>
        </Dialog>
      )}

      {variant === 'lightboxPoster' && (
        <Dialog open={isDialogOpen} onOpenChange={onCloseDialog}>
          <Poster
            className="aspect-video h-full w-full"
            poster={poster}
            provider={provider}
            playIcon={playIcon}
            playIconSize={playIconSize}
            playIconColor={playIconColor}
            onClick={() => setIsDialogOpen(true)}
          />
          <DialogContent
            overlayClass="bg-black/90"
            boxClass="w-screen max-w-[100vw] max-h-[100vh]"
            transparent
          >
            {isDialogOpen && player}
          </DialogContent>
        </Dialog>
      )}

      {(variant === 'inline' || variant === 'background') && player}
    </div>
  )
}
Player.propTypes = {
  accountId: PropTypes.string,
  autoplay: PropTypes.bool,
  className: PropTypes.string,
  controls: PropTypes.bool,
  ctaClass: PropTypes.string,
  ctaIcon: PropTypes.string,
  ctaLabel: PropTypes.string,
  ctaSize: PropTypes.string,
  ctaVariant: PropTypes.string,
  endsAt: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  fullSize: PropTypes.bool,
  id: PropTypes.string,
  loop: PropTypes.bool,
  muted: PropTypes.bool,
  onLightboxOpen: PropTypes.func,
  onLightboxClose: PropTypes.func,
  playerId: PropTypes.string,
  playIcon: PropTypes.object,
  playIconColor: PropTypes.string,
  playIconSize: PropTypes.object,
  playlist: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      url: PropTypes.string,
      provider: PropTypes.oneOf([
        'youtube',
        'jetstream',
        'jetstream-live',
        'vimeo',
        'cloudflare',
        'soundcloud',
        'hls',
        'mp4-hd',
        'mp4-sd',
      ]),
    })
  ),
  playlistIndex: PropTypes.number,
  poster: PropTypes.string,
  provider: PropTypes.oneOf([
    'youtube',
    'jetstream',
    'jetstream-live',
    'vimeo',
    'cloudflare',
    'soundcloud',
    'hls',
    'mp4-hd',
    'mp4-sd',
  ]),
  showPosterOverlay: PropTypes.bool,
  sources: PropTypes.arrayOf(
    PropTypes.shape({ src: PropTypes.string, type: PropTypes.string })
  ),
  startsAt: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  title: PropTypes.string,
  variant: PropTypes.string,
}
Player.providers = providers
