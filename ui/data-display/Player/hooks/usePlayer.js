import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import { useRouter } from 'next/router'

import { getPlayerConfig } from '../utils/getPlayerUrl'
import { getVideoPlayer } from '../utils/getVideoPlayer'
import { useCountdown } from './useCountdown'

const AUTOPLAY_DELAY = 5

export function usePlayer({
  accountId,
  autoplay: initialAutoplay,
  controls = true,
  endsAt,
  id,
  initialShowPosterOverlay,
  loop,
  muted,
  playerId,
  embedRef,
  playlist,
  playlistIndex,
  poster,
  provider,
  sources,
  startsAt,
  variant,
}) {
  const playerRef = useRef(null)
  const router = useRouter()

  // Set a flag to indicate that the component was hydrated
  const [mounted, setMounted] = useState(false)
  useEffect(() => setMounted(true), [])

  const [autoplay, setAutoplay] = useState(
    initialAutoplay || Boolean(router.query?.autoplay)
  )
  const [showPosterOverlay, setShowPosterOverlay] = useState(
    initialShowPosterOverlay
  )
  const [showNextEpisodeState, setShowNextEpisodeState] = useState('INITIAL')
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const {
    url,
    type,
    error,
    sources: configSources,
  } = getPlayerConfig({
    provider,
    id,
    accountId,
    playerId,
    startsAt,
    endsAt,
    autoplay: autoplay || Boolean(router.query?.autoplay),
  })

  const isLast = useMemo(
    () => (playlist ? playlistIndex === playlist.length - 1 : true),
    [playlist, playlistIndex]
  )

  const playNextEpisode = useCallback(async () => {
    if (!isLast) {
      const nextEpisode = playlist[playlistIndex + 1]
      if (nextEpisode) {
        const query = { ...router.query, autoplay: 1 }
        delete query.slug
        await router.push({
          pathname: nextEpisode.url,
          query,
        })
      }
    }
  }, [isLast, playlist, playlistIndex, router])

  const { countDown, startCountdown, resetCountdown } = useCountdown(
    AUTOPLAY_DELAY,
    playNextEpisode
  )

  const cancelPlayNextEpisode = useCallback(() => {
    resetCountdown()
    setShowNextEpisodeState('CANCELLED')
  }, [resetCountdown])

  const onEnd = useCallback(() => {
    if (!isLast && (playlist ?? []).length > 0) {
      setShowNextEpisodeState('COUNTDOWN')
      startCountdown()
    }
  }, [isLast, playlist, startCountdown])

  const onCloseDialog = useCallback(() => {
    setShowPosterOverlay(initialShowPosterOverlay)
    setIsDialogOpen(false)
    setAutoplay(initialAutoplay)
  }, [initialAutoplay, initialShowPosterOverlay])

  const options = useMemo(() => {
    const options = {
      sources: [...(sources ?? []), ...(configSources ?? [])],
      poster,
      autoplay:
        autoplay && muted
          ? 'muted'
          : autoplay || Boolean(router.query?.autoplay),
      controls,
      muted,
      loop,
    }

    if (provider === 'cloudflare' && variant === 'background') {
      options.backgroundVideo = true
    }

    return options
  }, [
    autoplay,
    configSources,
    controls,
    loop,
    muted,
    poster,
    provider,
    router.query?.autoplay,
    sources,
    variant,
  ])

  const isSelfHosted = useMemo(
    () => ['cloudflare', 'hls', 'mp4-hd', 'mp4-sd'].includes(provider),
    [provider]
  )

  const createPlayer = useCallback(async () => {
    if (!playerRef.current) {
      playerRef.current = getVideoPlayer({
        provider,
        embedRef: embedRef.current,
        onEnd,
        options,
      })
      playerRef.current?.initialise()
    }
  }, [embedRef, onEnd, options, provider])

  const destroyPlayer = useCallback(async () => {
    await playerRef.current?.cleanup()
    playerRef.current = null
  }, [playerRef])

  useEffect(() => {
    if (!mounted || !embedRef.current) return

    // Delay the DOM mutation until the next paint, which is usually enough to let React finish hydration (of the whole page, not only of the component)
    // --> useEffect ensures that the DOM nodes are there, but the hydration might still be in process
    // --> if the hydration is not complete, the player might not initialize correctly, leading to a #422 hydration mismatch error
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        createPlayer()
      })
    })

    return () => {
      // TODO: Fix BG Video not initialising after unmount on first render
      if (variant !== 'background') {
        destroyPlayer()
      }
    }
  }, [createPlayer, destroyPlayer, embedRef, mounted, onEnd, variant])

  const onPosterPlay = useCallback(() => {
    setShowPosterOverlay(false)
    setAutoplay(true)
    playerRef.current?.play()
  }, [playerRef])

  return {
    type,
    error,
    url,
    player: playerRef.current,
    isSelfHosted,
    countDown,
    showNextEpisodeState,
    playNextEpisode,
    cancelPlayNextEpisode,
    showPosterOverlay,
    onPosterPlay,
    isDialogOpen,
    setIsDialogOpen,
    onCloseDialog,
  }
}
