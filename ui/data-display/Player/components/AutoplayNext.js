import React from 'react'
import PropTypes from 'prop-types'

import Button from 'ui/buttons/Button'
import { useTranslation } from 'next-i18next'

export function AutoplayNext({ state, countDown, cancel, playNext }) {
  const { t } = useTranslation('media-library')

  if (state === 'INITIAL') {
    return null
  }

  return (
    <div className="absolute top-0 left-0 flex h-full w-full flex-col items-end justify-end space-y-2 rounded-md bg-white/90 p-8 dark:bg-black/80">
      <div className="flex space-x-4">
        {state !== 'CANCELLED' && (
          <>
            <Button label={t('cancel')} variant="secondary" onClick={cancel} />
            <Button
              label={t('playNextEpisodeIn', { countDown })}
              onClick={playNext}
              icon="spinner-third"
              iconClass="animate-spin"
            />
          </>
        )}
        {state === 'CANCELLED' && (
          <Button label={t('playNextEpisode')} onClick={playNext} />
        )}
      </div>
    </div>
  )
}
AutoplayNext.propTypes = {
  state: PropTypes.oneOf(['INITIAL', 'CANCELLED', 'PLAYING']),
  countDown: PropTypes.number,
  cancel: PropTypes.func,
  playNext: PropTypes.func,
}
