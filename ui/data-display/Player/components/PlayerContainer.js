import PropTypes from 'prop-types'
import React, { useRef } from 'react'

import clsx from 'clsx'
import <PERSON><PERSON><PERSON>lockedContent from 'ui/feedback/CookieBlockedContent'

import { usePlayer } from '../hooks/usePlayer'
import { AutoplayNext } from './AutoplayNext'
import { PlayerMedia } from './PlayerMedia'
import Poster from './Poster'

export function PlayerContainer({
  accountId,
  autoplay,
  endsAt,
  fullSize,
  id,
  initialShowPosterOverlay,
  playerId,
  playIcon,
  playIconColor,
  playIconSize,
  playlist,
  playlistIndex,
  poster,
  provider,
  sources,
  startsAt,
  title,
  variant,
}) {
  const embedRef = useRef(null)

  const {
    countDown,
    error,
    isSelfHosted,
    showNextEpisodeState,
    showPosterOverlay,
    type,
    url,
    cancelPlayNextEpisode,
    onPosterPlay,
    playNextEpisode,
  } = usePlayer({
    accountId,
    autoplay:
      autoplay ||
      variant === 'lightboxPoster' ||
      (variant === 'lightboxButton' && !initialShowPosterOverlay),
    embedRef,
    endsAt,
    id,
    initialShowPosterOverlay,
    playerId,
    playlist,
    playlistIndex,
    poster,
    provider,
    showPosterOverlay: (initialShowPosterOverlay = false),
    sources,
    startsAt,
  })

  const showPoster = variant !== 'lightboxPoster' && showPosterOverlay

  return (
    <div
      className={clsx(
        'h-0 w-full rounded-md pt-[56.25%] supports-[aspect-ratio]:aspect-video supports-[aspect-ratio]:h-full supports-[aspect-ratio]:pt-0',
        {
          'bg-gray-300': variant === 'inline',
          'bg-black': variant !== 'inline',
          'relative': showPoster,
        }
      )}
      style={
        fullSize || variant === 'inline'
          ? undefined
          : {
              maxHeight: 'calc(100vh - 7.5rem)', // 7.5rem is the height of the header and padding
              maxWidth: 'calc(100vw - 3rem)', // 3rem is the width of the side padding
            }
      }
    >
      {['hls', 'mp4-hd', 'mp4-sd'].includes(provider) ? (
        <PlayerMedia
          error={error}
          isSelfHosted={isSelfHosted}
          url={url}
          type={type}
          showPosterOverlay={showPoster}
          provider={provider}
          id={id}
          title={title}
          ref={embedRef}
        />
      ) : (
        <div className="absolute left-0 top-0 h-full w-full rounded-md supports-[aspect-ratio]:relative supports-[aspect-ratio]:left-auto supports-[aspect-ratio]:top-auto">
          <CookieBlockedContent
            blockCondition={`Player-${provider}`}
            typeCondition="tracking"
            className="flex h-full w-full flex-col items-center justify-center"
            buttonSize="sm"
          >
            <PlayerMedia
              error={error}
              isSelfHosted={isSelfHosted}
              url={url}
              type={type}
              showPosterOverlay={showPoster}
              provider={provider}
              id={id}
              title={title}
              ref={embedRef}
            />
          </CookieBlockedContent>
        </div>
      )}
      {showPoster && (
        <Poster
          fillContainer={true}
          poster={poster}
          provider={provider}
          playIcon={playIcon}
          playIconSize={playIconSize}
          playIconColor={playIconColor}
          onClick={onPosterPlay}
        />
      )}
      <AutoplayNext
        state={showNextEpisodeState}
        countDown={countDown}
        playNext={playNextEpisode}
        cancel={cancelPlayNextEpisode}
      />
    </div>
  )
}

PlayerContainer.propTypes = {
  accountId: PropTypes.string,
  autoplay: PropTypes.bool,
  endsAt: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  fullSize: PropTypes.bool,
  id: PropTypes.string,
  showPosterOverlay: PropTypes.bool,
  playerId: PropTypes.string,
  playIcon: PropTypes.object,
  playIconColor: PropTypes.string,
  playIconSize: PropTypes.object,
  playlist: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      url: PropTypes.string,
      provider: PropTypes.oneOf([
        'youtube',
        'jetstream',
        'jetstream-live',
        'vimeo',
        'cloudflare',
        'soundcloud',
        'hls',
        'mp4-hd',
        'mp4-sd',
      ]),
    })
  ),
  playlistIndex: PropTypes.number,
  poster: PropTypes.string,
  provider: PropTypes.oneOf([
    'youtube',
    'jetstream',
    'jetstream-live',
    'vimeo',
    'cloudflare',
    'soundcloud',
    'hls',
    'mp4-hd',
    'mp4-sd',
  ]),
  sources: PropTypes.arrayOf(
    PropTypes.shape({ src: PropTypes.string, type: PropTypes.string })
  ),
  startsAt: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  title: PropTypes.string,
  variant: PropTypes.string,
}
