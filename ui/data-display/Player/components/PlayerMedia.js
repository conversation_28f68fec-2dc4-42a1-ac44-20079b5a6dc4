import PropTypes from 'prop-types'
import React from 'react'

import { useTranslation } from 'next-i18next'

export const PlayerMedia = React.forwardRef(
  (
    { error, isSelfHosted, url, type, showPosterOverlay, provider, id, title },
    embedRef
  ) => {
    const { t } = useTranslation('media-library')

    if (error) {
      return (
        <div className="flex h-full w-full items-center justify-center border">
          Error: {t(error)}
        </div>
      )
    }

    if (isSelfHosted) {
      return <div className="h-full w-full" data-vjs-player="" ref={embedRef} />
    }

    if (!url || showPosterOverlay) {
      return null
    }

    return (
      <iframe
        ref={embedRef}
        allowFullScreen
        // className="absolute left-0 top-0 h-full w-full rounded-md supports-[aspect-ratio]:relative supports-[aspect-ratio]:left-auto supports-[aspect-ratio]:top-auto"
        className="absolute inset-0 h-full w-full rounded-md object-cover"
        frameBorder={0}
        height={type === 'audio' ? 'auto' : undefined}
        id={`player-${provider}-${id}`}
        key={`${provider}-${id}`}
        src={url}
        title={title}
        allow="autoplay"
      />
    )
  }
)

PlayerMedia.propTypes = {
  error: PropTypes.string,
  isSelfHosted: PropTypes.bool,
  url: PropTypes.string,
  type: PropTypes.string,
  showPosterOverlay: PropTypes.bool,
  provider: PropTypes.string,
  id: PropTypes.string,
  title: PropTypes.string,
}
