import { getFetch } from 'utils/http'

const MAP_TOKEN = process.env.NEXT_PUBLIC_MAP_TOKEN

export default function fetchMapboxGeocoder({
  term,
  typesParam,
  language,
  country,
}) {
  return getFetch(
    `/geocoding/v5/mapbox.places/${encodeURIComponent(term)}.json`,
    {
      access_token: MAP_TOKEN,
      types: typesParam,
      language: language || 'en',
      ...(country && { country }),
    },
    { baseURL: 'https://api.mapbox.com' }
  )
}
