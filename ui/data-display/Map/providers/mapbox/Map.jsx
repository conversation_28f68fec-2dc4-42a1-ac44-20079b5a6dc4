import React, { useCallback, useEffect, useRef, useState } from 'react'

import isEqual from 'lodash/isEqual'
import 'mapbox-gl/dist/mapbox-gl.css'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import ReactMapGL, { GeolocateControl, NavigationControl } from 'react-map-gl'

import fitMapToBounds from '../../helpers/fitMapToBounds'

const GeocoderControl = dynamic(() => import('./GeocoderControl'))
const MapMarker = dynamic(() => import('./MapMarker'))
const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Icon = dynamic(() => import('ui/icons/Icon'))

const MAP_TOKEN = process.env.NEXT_PUBLIC_MAP_TOKEN

/**
 * @typedef {Object} MapMarker
 * @property {Object} location
 * @property {Array} location.coordinates
 * @property {string} type
 */

/**
 * @typedef {Object} MapViewport
 * @property {number} longitude
 * @property {number} latitude
 * @property {number} zoom
 */

/**
 * @param {Object} props Component props
 * @param {Array} [props.boundingBox] The bounding box to fit the map to
 * @param {string} [props.className] The component classes
 * @param {function} [props.getMap] The function to get the map instance
 * @param {string} [props.mapStyle] The style uri of the map
 * @param {MapMarker[]} [props.markers] The markers to display on the map
 * @param {function} [props.onChange] The function to call when the map is clicked
 * @param {function} [props.onGeolocate] The function to call when the geolocate button is clicked
 * @param {function} [props.onMove] The function to call when the map is moved
 * @param {function} [props.onResult] The function to call when a search result is selected
 * @param {function} [props.renderItemPopover] The function to render the popover content
 * @param {boolean} [props.showControls] The flag to show or hide map controls
 * @param {boolean} [props.showGeolocate] The flag to show or hide the geolocate button
 * @param {boolean} [props.showSearch] The flag to show or hide the search input
 * @param {string} [props.token] The Mapbox access token
 * @param {MapViewport} [props.viewport] The initial viewport
 * @param {string} [props.id] The unique identifier for the map component
 */
export default function MapboxMap({
  boundingBox,
  className,
  getMap,
  mapStyle = 'mapbox://styles/hopesoftware/cl5jg7exh001q15p58o4zi2vn',
  markers = [],
  onChange,
  onGeolocate,
  onMove,
  onResult,
  renderItemPopover,
  markerPopoverZoom = 18,
  showControls,
  showGeolocate,
  showSearch,
  cooperativeGestures,
  resetZoom,
  resetZoomButton,
  token,
  viewport,
  id,
}) {
  const mapRef = useRef()
  const boundingBoxRef = useRef()
  const geocoderContainerRef = useRef()
  const [map, setMap] = useState()
  const [current, setCurrent] = useState(null)
  const { i18n, t } = useTranslation()

  const { longitude = 0, latitude = 0, zoom = 1 } = viewport || {}

  const mapToken = token ?? MAP_TOKEN

  // Called only once map is rendered (async)
  const onRender = useCallback(() => {
    const mapElem = mapRef?.current

    if (!mapElem) return

    // Saves map reference as state to allow other code to have access to it (with useRef is not enough)
    setMap(mapElem)

    // Ensures map has the right size
    mapElem.resize()
  }, [])

  const flyToMarker = useCallback(
    coordinates => {
      if (!Array.isArray(coordinates)) {
        if (resetZoom || coordinates === 'resetZoom') {
          fitMapToBounds(map, boundingBox, { duration: 1000, padding: 150 })
        }

        return
      }

      mapRef.current?.flyTo({
        center: coordinates,
        zoom: markerPopoverZoom,
        duration: 1000,
      })

      setCurrent(coordinates)
    },
    [boundingBox, map, markerPopoverZoom, resetZoom]
  )

  // Provide map if getMap function is requested
  useEffect(() => {
    if (map && typeof getMap === 'function') {
      getMap(map)
    }
  }, [map, getMap])

  // Load map in current language
  useEffect(() => {
    if (map && i18n?.language) {
      map.setLanguage(i18n?.language)
    }
  }, [map, i18n?.language])

  // Fit target region on viewport if boundingBox exists
  useEffect(() => {
    // Stop effect exceution when:
    if (
      !map || // - map is not yet available
      !Array.isArray(boundingBox) || // - boundingBox is not an array
      boundingBox.length !== 4 || // - boundingBox is not valid
      isEqual(boundingBoxRef.current, boundingBox) // - map already has been fit to the boundingBox
    ) {
      return undefined
    }

    // Fit map to boundingBox
    fitMapToBounds(map, boundingBox)

    // Set a ref to current boundingBox to be able to check for changes later
    boundingBoxRef.current = boundingBox

    // Forces a resize to center
    map.resize()
  }, [map, boundingBox])

  // Removes mapbox logo at start
  useEffect(() => {
    const timeout = setTimeout(() => {
      const logo = document?.getElementsByClassName('mapboxgl-ctrl-logo')[0]
      logo?.remove()
    }, 200)
    return () => clearTimeout(timeout)
  }, [])

  return (
    <div id={id} className={`relative isolate overflow-hidden ${className}`}>
      <div ref={geocoderContainerRef} className="absolute left-4 top-4 z-10" />
      <ReactMapGL
        ref={mapRef}
        reuseMaps
        attributionControl={false}
        mapStyle={mapStyle}
        mapboxAccessToken={mapToken}
        style={{ position: 'absolute', inset: 0 }}
        onRender={onRender}
        onResize={() => mapRef.current?.resize()}
        longitude={longitude}
        latitude={latitude}
        zoom={zoom}
        onClick={onChange}
        onMove={({ viewState }) => onMove(viewState)}
        locale={i18n.language}
        cooperativeGestures={cooperativeGestures}
      >
        {markers.length > 1 && resetZoomButton && (
          <Clickable
            className="absolute bottom-2 left-2 rounded-md bg-white px-3 py-2 shadow-lg text-xl hover:text-primary-600 hover:shadow-md"
            onClick={() => flyToMarker('resetZoom')}
            title={t('resetZoom')}
          >
            <Icon name="expand-arrows" />
          </Clickable>
        )}
        {showControls && <NavigationControl className="right-4 top-4" />}
        {showSearch && (
          <GeocoderControl
            mapboxAccessToken={mapToken}
            onResult={onResult}
            limit={8}
            language={i18n?.language || 'en'}
            // TODO: Expand reults with an external geocoder (like Google Maps API or similar)
          />
        )}
        {showGeolocate && (
          <GeolocateControl position="bottom-right" onGeolocate={onGeolocate} />
        )}
        {markers.map((marker, i) => {
          const { location, type } = marker
          const isCurrent = isEqual(location.coordinates, current)
          return (
            <MapMarker
              coordinates={location.coordinates}
              onClick={flyToMarker}
              current={isCurrent}
              onDragEnd={onChange}
              draggable={typeof onChange === 'function'}
              key={`marker-${i}`}
              popover={
                typeof renderItemPopover === 'function'
                  ? renderItemPopover(marker)
                  : null
              }
              type={type}
            />
          )
        })}
      </ReactMapGL>
    </div>
  )
}
