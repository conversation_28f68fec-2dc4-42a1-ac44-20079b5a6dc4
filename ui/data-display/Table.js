import React, { useCallback } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import useGoToTop from 'ui/helpers/useGoToTop'
import { getConditionalItems } from 'utils/arrays'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Pagination = dynamic(() => import('ui/navigation/Pagination'))

const alignContentMap = {
  bottom: 'align-bottom',
  middle: 'align-middle',
  top: 'align-top',
}

export default function Table({
  alignContent = 'middle',
  columns = [],
  items = [],
  onPageChange,
  onPageSizeChange,
  page = 1,
  pageSize = 20,
  sortColumn,
  sortOrder,
  total = 0,
}) {
  const goToTop = useGoToTop()

  const onPageSelect = useCallback(
    pageNumber => {
      if (typeof onPageChange === 'function') onPageChange(pageNumber)
      goToTop()
    },
    [onPageChange, goToTop]
  )

  const _columns = getConditionalItems(columns)

  return (
    <div className="space-y-8">
      <div className="w-full overflow-x-auto">
        <table className="w-full">
          {_columns && (
            <thead>
              <tr className="border-b-2">
                {_columns.map(
                  ({ name, title, onSort, className = '' }, key) => (
                    <th
                      className={`px-2 py-3 text-left font-semibold uppercase ${className}`}
                      key={key}
                    >
                      <Clickable
                        as="span"
                        className={`flex flex-row items-center space-x-2 rtl:space-x-reverse ${
                          sortColumn === name ? 'font-bold underline' : ''
                        } ${onSort ? 'cursor-pointer' : ''}`}
                        onClick={onSort}
                      >
                        <span>{title}</span>

                        {onSort && (
                          <Icon
                            name={
                              sortOrder === 'ASC'
                                ? 'chevron-down'
                                : 'chevron-up'
                            }
                          />
                        )}
                      </Clickable>
                    </th>
                  )
                )}
              </tr>
            </thead>
          )}
          {items.length > 0 && (
            <tbody className="divide-y">
              {items.map((item, key) => (
                <tr
                  className="transition-colors duration-500 ease-in-out hover:bg-primary-100"
                  key={key}
                >
                  {_columns.map(({ name, render, className = '' }, colKey) => (
                    <td
                      className={`px-2 py-3 ${alignContentMap[alignContent]} ${className}`}
                      key={colKey}
                    >
                      {render ? render(item[name], item) : item[name]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          )}
        </table>
      </div>
      <Pagination
        onPageChange={onPageSelect}
        onPageSizeChange={onPageSizeChange}
        page={page}
        pageSize={pageSize}
        showPageSize
        showResults
        total={total}
      />
    </div>
  )
}
Table.propTypes = {
  alignContent: PropTypes.oneOf(['top', 'middle', 'bottom']),
  columns: PropTypes.array.isRequired,
  items: PropTypes.array,
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func,
  page: PropTypes.number,
  pageSize: PropTypes.number,
  sortColumn: PropTypes.string,
  sortOrder: PropTypes.oneOf(['ASC', 'DESC']),
  t: PropTypes.func,
  total: PropTypes.number,
}
