import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import useSpacing from 'ui/helpers/useSpacing'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function Accordion({ children, spacing }) {
  const spacingClass = useSpacing(spacing)
  return <dl className={`flex flex-col ${spacingClass}`}>{children}</dl>
}
Accordion.propTypes = {
  children: PropTypes.node,
  spacing: PropTypes.string,
}

export function AccordionItem({ title, description, extra, defaultExpanded }) {
  const [expanded, setExpanded] = useState(defaultExpanded)

  const onToggle = useCallback(() => setExpanded(!expanded), [expanded])

  return (
    <>
      <dt className="flex flex-row items-center justify-between p-2">
        <button
          className="cursor-pointer font-semibold text-lg"
          onClick={onToggle}
        >
          {title}
        </button>
        <span className="flex flex-row items-center space-x-2 rtl:space-x-reverse">
          <span className="flex flex-row items-center">{extra}</span>
          <button
            className="cursor-pointer select-none rounded-full p-1 text-gray-600 transition-colors duration-300 ease-in-out hover:bg-gray-300 hover:text-primary-800"
            onClick={onToggle}
          >
            <Icon name={expanded ? 'chevron-up' : 'chevron-down'} />
          </button>
        </span>
      </dt>
      <dd
        className={`overflow-hidden transition-all duration-500 ease-in-out ${
          expanded ? 'max-h-screen' : 'max-h-0'
        } `}
      >
        <div className="border-t border-gray-400 px-2 py-4">{description}</div>
      </dd>
    </>
  )
}
AccordionItem.propTypes = {
  title: PropTypes.node,
  description: PropTypes.node,
  extra: PropTypes.node,
  defaultExpanded: PropTypes.bool,
}
