import PropTypes from 'prop-types'
import React from 'react'

export default function ListTitle({ title, extra, className = '' }) {
  if (!title && !extra) return null

  return (
    <div
      className={`flex items-center justify-between space-x-4 rtl:space-x-reverse ${className}`}
    >
      <h3 className="font-bold uppercase leading-normal text-lg lg:text-xl dark:text-primary-dark-300">
        {title}
      </h3>
      {extra && <div>{extra}</div>}
    </div>
  )
}
ListTitle.propTypes = {
  className: PropTypes.string,
  extra: PropTypes.node,
  title: PropTypes.node,
}
