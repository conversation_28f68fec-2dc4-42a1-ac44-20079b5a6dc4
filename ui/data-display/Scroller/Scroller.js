import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import dynamic from 'next/dynamic'

import throttle from 'lodash/throttle'
import clsx from 'clsx'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'

const NavButton = dynamic(() => import('./components/NavButton'))
const ScrollerItem = dynamic(() => import('./components/ScrollerItem'))

const maxItemsConfig = [
  {
    breakpoint: 'xs',
    maxItems: 2,
    scrollerGap: 'gap-4', // 1rem
    scrollItemWidth: 'min-w-[calc(100%/2-1rem*1/2-1rem)]', // 1rem * 1/2 - 1rem (trailing item)
  },
  {
    breakpoint: 'md',
    maxItems: 3,
    scrollerGap: 'md:gap-4', // 1rem
    scrollItemWidth: 'md:min-w-[calc(100%/3-1rem*2/3-1rem)]', // 1rem * 2/3 - 1rem (trailing item)
  },
  {
    breakpoint: 'lg',
    maxItems: 4,
    scrollerGap: 'lg:gap-8', // 2rem
    scrollItemWidth: 'lg:min-w-[calc(25%-2rem*3/4-1rem)]', // 2rem * 3/4 - 1rem (trailing item)
  },
  {
    breakpoint: 'xl',
    maxItems: 5,
    scrollerGap: 'xl:gap-8', // 2rem
    scrollItemWidth: 'xl:min-w-[calc(20%-2rem*4/5-1rem)]', // 2rem * 4/5 - 1rem (trailing item)
  },
]

/**
 * Scroller component to render a horizontal scroller with navigation buttons
 * @param {Object} props Component props
 * @param {React.ReactNode[]} props.children The items to render in the scroller
 * @param {Number} props.maxVisibleItems The maximum number of items visible in the scroller
 * @returns {React.Component} The scroller component
 */
export default function Scroller({ children, maxVisibleItems = 4 }) {
  const trackRef = useRef(null)

  const [scrollPosition, setScrollPosition] = useState(0)
  const [scrollWidth, setScrollWidth] = useState(0)
  const [trackWidth, setTrackWidth] = useState(0)

  const isMd = useMatchMedia(media.md)
  const isLg = useMatchMedia(media.lg)
  const isXl = useMatchMedia(media.xl)

  const scrollerItemsPerRow = isXl || isLg ? maxVisibleItems : isMd ? 3 : 2

  const getAnimationOrigin = index => {
    return index % scrollerItemsPerRow === 0
      ? 'left'
      : index % scrollerItemsPerRow === scrollerItemsPerRow - 1
        ? 'right'
        : 'center'
  }

  const resize = throttle(() => {
    if (trackRef.current) {
      setScrollWidth(trackRef.current.scrollWidth)
      setTrackWidth(trackRef.current.offsetWidth)
    }
  }, 200)

  useEffect(() => {
    window.addEventListener('resize', resize)

    resize()

    return () => {
      window.removeEventListener('resize', resize)
    }
  }, [resize])

  useEffect(() => {
    const handleScroll = () => {
      if (trackRef.current) {
        setScrollPosition(trackRef.current.scrollLeft)
      }
    }

    const trackEl = trackRef.current

    if (trackEl) {
      handleScroll()
    }

    trackEl?.addEventListener('scroll', handleScroll)

    return () => {
      trackEl?.removeEventListener('scroll', handleScroll)
    }
  }, [trackRef, scrollPosition])

  const onDirectionClick = useCallback(direction => {
    if (trackRef.current) {
      const trackWidth = trackRef.current.offsetWidth

      const gap =
        trackRef.current.children.length > 1
          ? trackRef.current.children[1].offsetLeft -
            trackRef.current.children[0].clientWidth
          : 0

      trackRef.current.scrollBy({
        left: direction === 'prev' ? -(trackWidth + gap) : trackWidth + gap,
        behavior: 'smooth',
      })
    }
  }, [])

  const { scrollerGapsClass, scrollerItemClass } = useMemo(
    () =>
      maxItemsConfig.reduce(
        (acc, config) => {
          const { maxItems, scrollerGap, scrollItemWidth } = config
          if (maxItems <= maxVisibleItems) {
            acc.scrollerGapsClass += ` ${scrollerGap}`
            acc.scrollerItemClass += ` ${scrollItemWidth}`
          }
          return acc
        },
        {
          scrollerGapsClass: '',
          scrollerItemClass: '',
        }
      ),
    [maxVisibleItems]
  )

  return (
    <div className="relative group/scroller">
      <NavButton
        direction="prev"
        onClick={onDirectionClick}
        disabled={scrollPosition === 0}
      />
      <NavButton
        direction="next"
        onClick={onDirectionClick}
        disabled={scrollPosition + trackWidth >= scrollWidth}
      />
      <div
        ref={trackRef}
        className={clsx(
          'relative flex snap-x snap-proximity flex-row overflow-x-auto no-scrollbar rtl:flex-row-reverse py-3 -my-3',
          scrollerGapsClass
        )}
      >
        {React.Children.map(children, (child, index) => (
          <ScrollerItem index={index} key={index} className={scrollerItemClass}>
            {React.cloneElement(child, {
              animationOrigin: getAnimationOrigin(index),
            })}
          </ScrollerItem>
        ))}
      </div>
    </div>
  )
}
