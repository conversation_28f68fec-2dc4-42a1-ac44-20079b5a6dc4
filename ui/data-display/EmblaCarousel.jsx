import * as React from 'react'
import clsx from 'clsx'
import useEmblaCarousel from 'embla-carousel-react'
import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))

const CarouselContext = React.createContext(null)

function useCarousel() {
  const context = React.useContext(CarouselContext)

  if (!context) {
    throw new Error('useCarousel must be used within a <Carousel />')
  }

  return context
}

const Carousel = React.forwardRef(
  (
    {
      orientation = 'horizontal',
      opts,
      setApi,
      plugins,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const [carouselRef, api] = useEmblaCarousel(
      {
        ...opts,
        axis: orientation === 'horizontal' ? 'x' : 'y',
      },
      plugins
    )
    const [canScrollPrev, setCanScrollPrev] = React.useState(false)
    const [canScrollNext, setCanScrollNext] = React.useState(false)

    const onSelect = React.useCallback(api => {
      if (!api) {
        return
      }

      setCanScrollPrev(api.canScrollPrev())
      setCanScrollNext(api.canScrollNext())
    }, [])

    const scrollPrev = React.useCallback(() => {
      api?.scrollPrev()
    }, [api])

    const scrollNext = React.useCallback(() => {
      api?.scrollNext()
    }, [api])

    React.useEffect(() => {
      if (!api || !setApi) {
        return
      }

      setApi(api)
    }, [api, setApi])

    React.useEffect(() => {
      if (!api) {
        return
      }

      onSelect(api)
      api.on('reInit', onSelect)
      api.on('select', onSelect)

      return () => {
        api?.off('select', onSelect)
      }
    }, [api, onSelect])

    return (
      <CarouselContext.Provider
        value={{
          carouselRef,
          api: api,
          opts,
          orientation:
            orientation || (opts?.axis === 'y' ? 'vertical' : 'horizontal'),
          scrollPrev,
          scrollNext,
          canScrollPrev,
          canScrollNext,
        }}
      >
        <div
          ref={ref}
          className={clsx('relative', className)}
          role="region"
          aria-roledescription="carousel"
          {...props}
        >
          {children}
        </div>
      </CarouselContext.Provider>
    )
  }
)
Carousel.displayName = 'Carousel'

const CarouselContent = React.forwardRef(({ className, ...props }, ref) => {
  const { carouselRef, orientation } = useCarousel()

  return (
    <div ref={carouselRef} className="overflow-hidden">
      <div
        ref={ref}
        className={clsx(
          'flex',
          orientation === 'horizontal' ? '-ml-4' : '-mt-4 flex-col',
          className
        )}
        {...props}
      />
    </div>
  )
})
CarouselContent.displayName = 'CarouselContent'

const CarouselItem = React.forwardRef(({ className, ...props }, ref) => {
  const { orientation } = useCarousel()

  return (
    <div
      ref={ref}
      role="group"
      aria-roledescription="slide"
      className={clsx(
        'min-w-0 shrink-0 grow-0 basis-full',
        orientation === 'horizontal' ? 'pl-4' : 'pt-4',
        className
      )}
      {...props}
    />
  )
})
CarouselItem.displayName = 'CarouselItem'

const CarouselPrevious = React.forwardRef(({ className, ...props }, ref) => {
  const { api, orientation, scrollPrev, canScrollPrev } = useCarousel()

  return (
    <Icon
      ref={ref}
      name="chevron-left"
      disabled={!canScrollPrev}
      onClick={() => {
        scrollPrev()
        api.plugins()?.autoplay.reset()
      }}
      className={clsx(
        'absolute hidden md:flex h-12 w-12 p-3 pl-2 rounded-full hover:bg-primary-100',
        orientation === 'horizontal'
          ? '-left-2 top-1/2 -translate-y-1/2'
          : '-top-16 left-1/2 -translate-x-1/2 rotate-90',
        className
      )}
      size="1.5em"
      {...props}
    />
  )
})
CarouselPrevious.displayName = 'CarouselPrevious'

const CarouselNext = React.forwardRef(({ className, ...props }, ref) => {
  const { api, orientation, scrollNext, canScrollNext } = useCarousel()

  return (
    <Icon
      ref={ref}
      name="chevron-right"
      disabled={!canScrollNext}
      onClick={() => {
        scrollNext()
        api.plugins()?.autoplay.reset()
      }}
      className={clsx(
        'absolute hidden md:flex h-12 w-12 p-3 pr-2 rounded-full hover:bg-primary-100',
        orientation === 'horizontal'
          ? '-right-2 top-1/2 -translate-y-1/2'
          : '-bottom-16 left-1/2 -translate-x-1/2 rotate-90',
        className
      )}
      size="1.5em"
      {...props}
    />
  )
})
CarouselNext.displayName = 'CarouselNext'

export {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
}
