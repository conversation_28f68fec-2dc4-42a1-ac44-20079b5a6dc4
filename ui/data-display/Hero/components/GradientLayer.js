import React from 'react'

import clsx from 'clsx'

export default function GradientLayer() {
  return (
    <>
      {/* Left gradient (linear) */}
      <div
        className={clsx(
          'hidden md:block',
          'absolute left-0 w-2/3 h-full',
          'bg-gradient-to-r from-primary-dark-800/85 via-primary-dark-800/85 via-30% to-transparent'
        )}
      />
      {/* Left gradient (radial) - NOTE: using primary-dark-800 HEX value (#0d171e)  */}
      <div className="hidden lg:block absolute inset-0 bg-[radial-gradient(ellipse_at_right,_transparent_70%,_#0d171e_100%)]" />
      {/* Bottom gradient (linear) */}
      <div
        className={clsx(
          'absolute bottom-0 w-full',
          'bg-gradient-to-t from-primary-dark-800 to-transparent',
          'h-full md:h-96 lg:h-96 xl:h-52'
        )}
      />
    </>
  )
}
