import React, { useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types'
import clsx from 'clsx'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'
import { isEmpty } from 'utils/arrays'

import dynamic from 'next/dynamic'

import useScrollPosition from 'hooks/useScrollPosition'
import useInView from 'hooks/useInView'

import BackgroundVideo from 'ui/data-display/BackgroundVideo'

const Badge = dynamic(() => import('ui/feedback/Badge'))
const Button = dynamic(() => import('ui/buttons/Button'))
const ButtonGroup = dynamic(() => import('ui/buttons/ButtonGroup'))
const GradientLayer = dynamic(() => import('./components/GradientLayer'))
const Image = dynamic(() => import('ui/data-display/Image'))
const Player = dynamic(() => import('ui/data-display/Player'))
const Text = dynamic(() => import('ui/typography/Text'))

import { getResponsiveSizes, useImageLoader } from 'ui/data-display/Image'

const HERO_IMAGE_QUALITY = 80

// Get styles to add fade-out for the hero image or video
function useMediaStyle(ref, { fadeOutPoint = 200, hidden } = {}) {
  const scrollPosition = useScrollPosition(ref)
  return hidden
    ? {
        opacity: 0,
      }
    : {
        transitionDuration: '800ms',
        opacity: scrollPosition > fadeOutPoint ? 0.2 : 1,
      }
}

export default function Hero({
  logo,
  image,
  mobileImage,
  title,
  titleTextSize,
  description,
  videoAccountId,
  videoProvider,
  videoId,
  videoPoster,
  videoDelay = 3000,
  metaData = [],
  tags = [],
  ctaIcon,
  ctaLabel,
  ctaUrl,
  ctaVideoIcon,
  ctaVideoId,
  ctaVideoLabel,
  ctaVideoOnOpen,
  ctaVideoOnClose,
  ctaVideoProvider,
}) {
  const imageLoader = useImageLoader(image)
  const mobileImageLoader = useImageLoader(mobileImage)
  const isMd = useMatchMedia(media.md)
  const isLg = useMatchMedia(media.lg)

  const _imageFile = isMd ? image : mobileImage || image
  const _imageLoader = isMd
    ? imageLoader
    : mobileImage
      ? mobileImageLoader
      : imageLoader

  const hasMetaData = !isEmpty(metaData)
  const hasTags = !isEmpty(tags)
  const hasVideo = videoAccountId && videoProvider && videoId

  const heroRef = useRef()
  const isInView = useInView(heroRef, { threshold: 0.5 })

  const [videoPlaying, setVideoPlaying] = useState(false)
  useEffect(() => {
    if (hasVideo && isInView) {
      setTimeout(() => setVideoPlaying(true), videoDelay)
    }
    return () => setVideoPlaying(false)
  }, [hasVideo, isInView, videoDelay])

  const videoStyle = useMediaStyle(heroRef, { hidden: !videoPlaying })
  const imageStyle = useMediaStyle(heroRef, { hidden: videoPlaying })

  return (
    <div
      className="relative w-full min-h-[380px] md:min-h-[600px] lg:min-h-[700px] overflow-hidden bg-primary-dark-800"
      ref={heroRef}
    >
      <Image
        className="absolute w-full h-full inset-0 object-cover object-top"
        file={_imageFile}
        alt={title}
        loader={_imageLoader}
        quality={HERO_IMAGE_QUALITY}
        priority={true}
        lazy={false}
        sizes={getResponsiveSizes('lg:1180px md:768px 384px')}
        style={imageStyle}
      />
      <BackgroundVideo
        accountId={videoAccountId}
        poster={videoPoster}
        provider={videoProvider}
        videoId={videoId}
        style={videoStyle}
      />
      <GradientLayer />
      <div
        className={clsx(
          'absolute inset-x-0 h-full px-8 py-8 md:py-24 md:pl-16 lg:pl-28 lg:pr-0 lg:py-24 md:max-w-[40rem]',
          'flex flex-col gap-6 md:gap-8 justify-end'
        )}
      >
        {(title || description || logo) && (
          <div className="flex flex-col items-center md:items-start gap-2 md:gap-4 px-4 md:px-0 group/content">
            {logo && (
              <Image
                className="max-h-14 md:max-h-16 lg:max-h-28 px-12 md:px-0 -mb-2 md:mb-4"
                file={logo}
                alt={title}
                objectFit="contain"
                objectPosition={isMd ? 'left' : 'center'}
                loader={imageLoader}
                sizes={getResponsiveSizes('lg:500 400px')}
              />
            )}
            {title && !logo && (
              <Text
                as="h2"
                textSize={titleTextSize || { xs: '2xl', md: '4xl' }}
                fontWeight="bold"
                className="leading-tight md:leading-normal"
                color="white"
              >
                {title}
              </Text>
            )}
            {description && isMd && (
              <Text
                as="h2"
                textSize={{ xs: 'md', md: 'xl' }}
                color="gray-300"
                className="line-clamp-2 md:line-clamp-2"
              >
                {description}
              </Text>
            )}
          </div>
        )}
        {(hasMetaData || hasTags) && isLg && (
          <div className="space-y-2">
            {hasMetaData && (
              <div className="text-white font-semibold text-lg">
                {metaData.join(' • ')}
              </div>
            )}
            {hasTags && (
              <div className="flex gap-1 dark">
                {tags.map((tag, key) => (
                  <Badge key={key} className="border" label={tag} />
                ))}
              </div>
            )}
          </div>
        )}
        <ButtonGroup align={isMd ? 'left' : 'center'} className="dark">
          {ctaLabel && ctaUrl && (
            <Button
              url={ctaUrl}
              icon={ctaIcon}
              iconClass="scale-75"
              variant="primary"
              className="rounded-full"
              size={isMd ? 'lg' : 'sm'}
              label={ctaLabel}
            />
          )}
          {ctaVideoProvider && ctaVideoId && (
            <Player
              provider={ctaVideoProvider}
              id={ctaVideoId}
              ctaSize={isMd ? 'lg' : 'sm'}
              ctaIcon={ctaVideoIcon}
              ctaLabel={ctaVideoLabel}
              ctaVariant="secondary"
              variant="lightboxButton"
              ctaClass="rounded-full"
              onLightboxOpen={ctaVideoOnOpen}
              onLightboxClose={ctaVideoOnClose}
            />
          )}
        </ButtonGroup>
      </div>
    </div>
  )
}

Hero.propTypes = {
  logo: PropTypes.object,
  image: PropTypes.object,
  title: PropTypes.string,
  description: PropTypes.string,
  primaryButtonLabel: PropTypes.string,
  primaryButtonUrl: PropTypes.string,
  secondaryButtonLabel: PropTypes.string,
  secondaryButtonUrl: PropTypes.string,
}
