import React from 'react'
import PropTypes from 'prop-types'

import { FormatDate } from 'utils/datetime'

export default function LabelDate({ className = '', date, format, label }) {
  return (
    <p
      className={`flex items-baseline space-x-2 leading-4 rtl:space-x-reverse ${className}`}
    >
      <span>{label}:</span>
      <span className="font-semibold">
        <FormatDate date={date} format={format} />
      </span>
    </p>
  )
}
LabelDate.propTypes = {
  className: PropTypes.string,
  date: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object,
    PropTypes.number,
  ]),
  format: PropTypes.string,
  label: PropTypes.node,
}
