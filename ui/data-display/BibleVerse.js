import React from 'react'
import PropTypes from 'prop-types'

export default function BibleVerse({ bible, passage, text }) {
  return (
    <div className="my-16 px-2 text-center md:px-8 xl:px-16">
      <div className="pb-6 font-serif italic text-lg md:text-xl">{text}</div>
      <div className="font-semi-bold text-lg">{passage}</div>
      {bible && <div className="text-gray-600 text-sm">{bible}</div>}
    </div>
  )
}

BibleVerse.propTypes = {
  bible: PropTypes.string,
  passage: PropTypes.string,
  text: PropTypes.string,
}
