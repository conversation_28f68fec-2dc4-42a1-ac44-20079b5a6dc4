import PropTypes from 'prop-types'
import React from 'react'

import { getTextColor } from 'ui/helpers/getColor'

export const LogoIcon = ({ className, fillColor, variant = 'dark' }) => {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 81.9 82"
    >
      <path
        className={
          fillColor
            ? getTextColor(fillColor)
            : variant === 'light'
              ? 'fill-white'
              : 'fill-gray-700'
        }
        fillRule="evenodd"
        d="M42.4 56.34c0-2.75 2.46-5.26 5.35-8.2l3.23-3.23c2.12-2.12 6.66-6.66 4.78-10.9-.14-.29.1-.43.29-.19 2.85 3.47 1.88 8.88-3.71 14.47l-4.58 4.58c-2.89 2.89-4.29 4.29-5.02 7.81-.05.24-.34.24-.34 0v-4.34ZM31.88 45.25c-3.28 3.28-4.1 7.09-1.45 9.36.24.19.1.43-.19.29-3.28-1.5-5.84-6.9-.24-12.49l11.19-11.19c2.89-2.89 4.29-4.24 5.02-7.77.05-.24.34-.24.34 0v4.29c0 2.75-2.46 5.26-5.35 8.2l-9.31 9.31Zm-5.93 4.44c-2.85-3.47-1.88-8.88 3.71-14.47l11.53-11.53c2.89-2.89 4.29-4.29 5.02-7.81.05-.24.34-.24.34 0v4.34c0 2.75-2.46 5.26-5.35 8.2L31.02 38.6c-2.17 2.12-6.66 6.66-4.78 10.9.14.29-.1.43-.29.19Zm-1.93-7.57c-1.01-3.33-.34-8.39 4.44-13.22l12.73-12.73c2.89-2.89 4.29-4.29 5.02-7.77.05-.24.34-.24.34 0v4.29c0 2.75-2.46 5.26-5.35 8.2l-11 10.95c-5.06 5.11-5.88 7.48-5.88 10.27.05.29-.19.29-.29 0Zm26.1-3.86c3.28-3.28 4.1-7.09 1.45-9.36-.24-.19-.1-.43.19-.29 3.28 1.5 5.84 6.9.24 12.49l-4.24 4.24c-2.89 2.89-4.29 4.24-5.02 7.77-.05.24-.34.24-.34 0v-4.29c0-2.75 2.46-5.26 5.35-8.2l2.36-2.36Zm7.86 3.14c1.01 3.33.34 8.39-4.44 13.22l-5.79 5.79-.48.48h-3.96c.96-1.69 2.6-3.33 4.44-5.16l4-4c5.06-5.06 5.88-7.48 5.88-10.27.05-.34.29-.34.34-.05ZM42.79 68.17c-.05.24-.34.24-.34 0v-4.29c0-.1.1-.19.19-.19h2.12c-1.01 1.3-1.59 2.56-1.98 4.49Zm9.41-.1c-5.55-.92-8.54 1.69-9.45 4.15-.1.24-.34.19-.34 0v-.77c0-2.75 2.46-5.26 5.35-8.2l4.82-4.82 12.83 2.22 6.8 6.8c6.03-7.14 9.7-16.4 9.7-26.48C81.9 18.33 63.57 0 40.95 0S0 18.33 0 40.95c0 10.08 3.67 19.34 9.7 26.48l6.8-6.8 14.57-2.51c6.75-1.16 8.44.34 8.44 2.56 0 .1-.1.19-.19.19h-4.2c-.1 0-.19.1-.19.19v2.51c0 .1.1.19.19.19h4.2c.1 0 .19.1.19.19v8.3c0 .19-.24.24-.34 0-.92-2.46-3.91-5.11-9.45-4.15 0 0-9.6 1.64-16.74 2.89 7.33 6.8 17.12 11 27.93 11s20.6-4.2 27.93-11c-5.5-1.01-11.05-1.98-16.64-2.94Z"
      />
    </svg>
  )
}

LogoIcon.propTypes = {
  className: PropTypes.string,
  fillColor: PropTypes.string,
  variant: PropTypes.oneOf(['light', 'dark']),
}
