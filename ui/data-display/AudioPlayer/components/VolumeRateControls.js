import React from 'react'
import PropTypes from 'prop-types'
import { ControlButton } from './ControlButton'
import Icon from 'ui/icons/Icon'
import { useTranslation } from 'next-i18next'

export function VolumeRateControls({
  rate,
  volume,
  setRate,
  setVolume,
  className = '',
  disabled,
}) {
  const { t } = useTranslation('media-library')

  return (
    <div className={`flex space-x-2 rtl:space-x-reverse ${className}`}>
      <ControlButton
        title={rate < 2 ? t('increaseRate') : t('resetRate')}
        onClick={() => {
          if (rate === 2) {
            setRate(1)
          } else {
            setRate(Number(rate) + 0.25)
          }
        }}
        disabled={disabled}
      >
        <span className="text-xs">{rate}&times;</span>
      </ControlButton>
      <ControlButton
        title={volume > 0 ? t('mute') : t('unmute')}
        onClick={() => {
          if (volume === 1) {
            setVolume(0)
          } else {
            setVolume(1)
          }
        }}
        disabled={disabled}
      >
        <Icon
          name={volume === 0 ? 'volume-xmark' : 'volume'}
          className={`h-4 w-4 ${volume === 0 ? 'translate-x-[1px]' : ''}`}
          size="1rem"
        />
      </ControlButton>
    </div>
  )
}

VolumeRateControls.propTypes = {
  className: PropTypes.string,
  volume: PropTypes.number,
  rate: PropTypes.number,
  setRate: PropTypes.func,
  setVolume: PropTypes.func,
  disabled: PropTypes.bool,
}
