import React, { useCallback } from 'react'
import PropTypes from 'prop-types'
import { SeekControl } from './SeekControl'
import { PlayControl } from './PlayControl'

export function Controls({
  play,
  pause,
  seekToOffset,
  state,
  className = '',
  liveStream,
}) {
  const disabled = ['INITIAL', 'ERROR'].includes(state)

  const seekTo = useCallback(
    offset => () => seekToOffset(offset),
    [seekToOffset]
  )

  return (
    <div
      className={`flex items-center justify-center space-x-4 rtl:space-x-reverse ${className}`}
    >
      {!liveStream && (
        <SeekControl
          onClick={seekTo(-15)}
          offset={15}
          disabled={disabled}
          flip
        />
      )}
      <PlayControl play={play} pause={pause} state={state} />
      {!liveStream && (
        <SeekControl onClick={seekTo(30)} disabled={disabled} offset={30} />
      )}
    </div>
  )
}

Controls.propTypes = {
  className: PropTypes.string,
  play: PropTypes.func.isRequired,
  pause: PropTypes.func.isRequired,
  seekToOffset: PropTypes.func.isRequired,
  liveStream: PropTypes.bool,
  state: PropTypes.oneOf([
    'INITIAL',
    'LOADSTARTED',
    'LOADEDMETADATA',
    'PLAYING',
    'PAUSED',
    'WAITING',
    'CANPLAY',
    'CANPLAYTHROUGH',
    'ENDED',
    'EMPTIED',
    'ERROR',
  ]).isRequired,
}
