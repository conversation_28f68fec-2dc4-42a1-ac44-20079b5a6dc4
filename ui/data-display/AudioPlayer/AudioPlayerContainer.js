import PropTypes from 'prop-types'

import clsx from 'clsx'

import { AudioPlayer } from './AudioPlayer'
import { AudioProvider } from 'components/AudioProvider'

export function AudioPlayerContainer({
  id,
  className = '',
  title,
  kicker,
  subtitle,
  sources,
  image,
  abstract,
  description,
  variant = 'lg',
  liveStream,
  onPlay,
}) {
  return (
    <AudioProvider sources={sources}>
      <AudioPlayer
        id={id}
        className={clsx(
          'rounded-lg bg-white p-4 text-gray-700 shadow dark:bg-primary-dark-700 dark:text-white',
          className
        )}
        title={title}
        kicker={kicker}
        subtitle={subtitle}
        image={image}
        abstract={abstract}
        description={description}
        variant={variant}
        liveStream={liveStream}
        onPlay={onPlay}
      />
    </AudioProvider>
  )
}

AudioPlayerContainer.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  sources: PropTypes.arrayOf(
    PropTypes.shape({
      src: PropTypes.string,
      type: PropTypes.string,
    })
  ),
  image: PropTypes.object,
  title: PropTypes.string,
  kicker: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  subtitle: PropTypes.string,
  abstract: PropTypes.string,
  description: PropTypes.object,
  liveStream: PropTypes.bool,
  variant: PropTypes.oneOf(['sm', 'md', 'lg']),
}
