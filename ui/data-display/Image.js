import PropTypes from 'prop-types'
import { useMemo } from 'react'

import NextImage from 'next/image'
import { getAspectRatioClass } from 'ui/helpers/getAspectRatioClass'
import { getImageUrl } from 'utils/images'

const DEFAULT_QUALITY = 75

/**
 * Next's Image loader for our CDN images
 * @param {number} defaultQuality
 * @returns
 */
export function useImageLoader(file) {
  return ({ src, width, quality }) =>
    getImageUrl(
      { ...file, name: src },
      `w:${width},q:${quality || DEFAULT_QUALITY}`
    )
}

export function useImageUrl(file, width = 800, quality = 80) {
  return useMemo(
    () => getImageUrl(file, `w:${width},q:${quality}`),
    [file, width, quality]
  )
}

const deviceSizes = {
  xs: '350px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
}

/**
 * Given a formatted string returns another one with media queries like `img` [sizes]
 * @param {string} sizes String with responsive sizes. Example: `'md:192px sm:128px 350px'`
 * @returns `string` - Output: `'(min-width: 768px) 192px, (min-width: 640px) 128px, 350px'`
 */
export function getResponsiveSizes(sizes = '') {
  return sizes
    .split(' ')
    .map(size => {
      const [mediaQuery, width] = size.split(':')
      return deviceSizes[mediaQuery]
        ? `(min-width: ${deviceSizes[mediaQuery]}) ${width}`
        : mediaQuery
    })
    .join(', ')
}

const objectFitStyles = {
  'contain': 'object-contain',
  'cover': 'object-cover',
  'fill': 'object-fill',
  'none': 'object-none',
  'scale-down': 'object-scale-down',
}

const objectPositionStyles = {
  'bottom': 'object-bottom',
  'center': 'object-center',
  'left': 'object-left',
  'left-bottom': 'object-left-bottom',
  'left-top': 'object-left-top',
  'right': 'object-right',
  'right-bottom': 'object-right-bottom',
  'right-top': 'object-right-top',
  'top': 'object-top',
}

// TODO: Add support for file.blurhash
export default function Image({
  alt,
  aspectRatio,
  className = '',
  file,
  height = 600,
  lazy = true,
  objectFit,
  objectPosition,
  priority,
  quality = 75,
  sizes = '',
  onLoadingComplete,
  src,
  width = 800,
  style,
}) {
  const imageLoader = useImageLoader(file)

  // sets objectFit to 'contain' for SVGs to avoid stretching, otherwise use the prop value
  objectFit = file?.mime === 'image/svg+xml' ? 'contain' : objectFit

  const classFit = objectFitStyles[objectFit] || objectFitStyles['cover'] || ''
  const classPosition =
    objectPositionStyles[objectPosition] || objectPositionStyles['center'] || ''

  const aspectRatioClass = getAspectRatioClass(aspectRatio, 'auto')

  return (
    <NextImage
      className={`${classFit} ${classPosition} ${aspectRatioClass} ${className}`}
      loader={file ? imageLoader : undefined}
      alt={alt || file?.originalFilename}
      src={file ? file.name : src}
      width={file?.width || width}
      height={file?.height || height}
      priority={priority}
      sizes={getResponsiveSizes(sizes)}
      quality={quality}
      loading={lazy ? 'lazy' : 'eager'}
      onLoadingComplete={onLoadingComplete}
      style={style}
    />
  )
}
Image.propTypes = {
  alt: PropTypes.string,
  aspectRatio: PropTypes.string,
  className: PropTypes.string,
  file: PropTypes.object,
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  lazy: PropTypes.bool,
  objectFit: PropTypes.oneOf([
    'fill',
    'contain',
    'cover',
    'none',
    'scale-down',
  ]),
  objectPosition: PropTypes.string,
  onLoadingComplete: PropTypes.func,
  priority: PropTypes.bool,
  quality: validateQuality,
  sizes: PropTypes.string,
  src: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
  url: PropTypes.string,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  style: PropTypes.object,
}

export function Img({ alt, file, src, srcWidth = 500, quality = 80, ...rest }) {
  const imgSrc = file ? getImageUrl(file, `w:${srcWidth},q:${quality}`) : src

  return <img alt={alt} src={imgSrc} {...rest} /> // eslint-disable-line @next/next/no-img-element
}
Img.propTypes = {
  alt: PropTypes.string,
  file: PropTypes.object,
  quality: validateQuality,
  src: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),
  srcWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}

function isValidQuality(quality) {
  return `${quality}`.match(/^[0-9][0-9]?$|^100$/)
}

function validateQuality(propValue, key, componentName) {
  if (propValue.quality && !isValidQuality(propValue.quality)) {
    return new Error(
      `Invalid value for prop 'quality' provided to '${componentName}'. Value should be integers from '0' to '100', and '${propValue.quality}' was provided here.`
    )
  }
  return null
}
