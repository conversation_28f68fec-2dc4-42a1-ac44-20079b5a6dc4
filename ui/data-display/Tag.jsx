import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Icon = dynamic(() => import('ui/icons/Icon'))

const variants = {
  'basic': {
    wrap: 'bg-gray-200 dark:bg-primary-dark-800 border-transparent text-gray-600 dark:text-gray-300 dark:border-gray-700',
    wrapHover: 'hover:bg-gray-300 dark:hover:bg-primary-dark-700',
    icon: 'bg-gray-400 text-white',
    iconHover: 'hover:bg-gray-500',
  },
  'filter': {
    wrap: 'bg-white hover:text-danger-600 hover:border-danger-600',
    wrapHover: '',
    icon: '',
    iconHover: '',
  },
  'light': {
    wrap: 'bg-gray-100 border-gray-300 text-gray-500',
    wrapHover: 'hover:bg-gray-200',
    icon: 'bg-gray-300 text-gray-100',
    iconHover: 'hover:bg-gray-400',
  },
  'primary': {
    wrap: 'bg-primary-500 border-transparent text-primary-50',
    wrapHover: 'hover:bg-primary-600',
    icon: 'bg-primary-200 text-primary-500',
    iconHover: 'hover:bg-primary-50',
  },
  'info': {
    wrap: 'bg-info-100 border-info-300 text-info-600',
    wrapHover: 'hover:bg-info-200',
    icon: 'bg-info-300 text-info-100',
    iconHover: 'hover:bg-info-400',
  },
  'success': {
    wrap: 'bg-success-500 border-transparent text-success-50',
    wrapHover: 'hover:bg-success-600',
    icon: 'bg-success-200 text-success-500',
    iconHover: 'hover:bg-success-50',
  },
  'success-light': {
    wrap: 'bg-success-100 border-success-300 text-success-600',
    wrapHover: 'hover:bg-success-200',
    icon: 'bg-success-300 text-success-100',
    iconHover: 'hover:bg-success-400',
  },
  'warn': {
    wrap: 'bg-warning-500 border-transparent text-warn-50',
    wrapHover: 'hover:bg-warning-600',
    icon: 'bg-warning-200 text-warn-500',
    iconHover: 'hover:bg-warning-50',
  },
  'warn-light': {
    wrap: 'bg-warning-100 border-warn-300 text-warn-600',
    wrapHover: 'hover:bg-warning-200',
    icon: 'bg-warning-300 text-warn-100',
    iconHover: 'hover:bg-warning-400',
  },
  'danger': {
    wrap: 'bg-danger-500 border-transparent text-danger-50',
    wrapHover: 'hover:bg-danger-600',
    icon: 'bg-danger-200 text-danger-500',
    iconHover: 'hover:bg-danger-50',
  },
  'danger-light': {
    wrap: 'bg-danger-100 border-danger-300 text-danger-600',
    wrapHover: 'hover:bg-danger-200',
    icon: 'bg-danger-300 text-danger-100',
    iconHover: 'hover:bg-danger-400',
  },
}

const alignOptions = {
  left: 'justify-start',
  center: 'justify-center',
  right: 'justify-end',
}

const sizesStyles = {
  xs: {
    wrap: 'py-0.5 text-sm min-h-[0.5rem]',
    text: 'text-xs',
  },
  sm: {
    wrap: 'py-1 px-1 text-sm min-h-[2rem]',
    text: 'text-xs',
  },
  md: {
    wrap: 'py-2 px-3 text-md min-h-[2.5rem]',
    text: 'text-sm',
  },
}

export default function Tag({
  avatar,
  disabled,
  deletable,
  icon,
  label,
  onClick,
  onDelete,
  size = 'md',
  variant = 'basic',
}) {
  const classes = variants[variant] ?? variants.basic
  const sizeStyles = sizesStyles[size] || sizesStyles.md
  const wrapClickableClass = disabled
    ? 'opacity-40'
    : onClick
      ? classes.wrapHover
      : ''
  const deleteClickableClass = disabled ? '' : classes.iconHover

  return (
    <Clickable
      className={`inline-flex items-center rounded border ${sizeStyles.wrap} ${classes.wrap} ${wrapClickableClass}`}
      disabled={disabled}
      onClick={onClick}
    >
      {avatar && (
        <div className="flex h-5 w-5 items-center justify-center">{avatar}</div>
      )}
      {icon && (
        <div className="-mr-2 flex w-6 items-center justify-center overflow-hidden rounded-full text-xs">
          <Icon name={icon} />
        </div>
      )}
      <span className={`whitespace-nowrap px-2 font-medium ${sizeStyles.text}`}>
        {label}
      </span>
      {deletable && (
        <Clickable
          className={`flex h-5 w-5 items-center justify-center overflow-hidden text-sm ${classes.icon} ${deleteClickableClass}`}
          disabled={disabled}
          onClick={onDelete}
        >
          <Icon name="times" />
        </Clickable>
      )}
    </Clickable>
  )
}
Tag.propTypes = {
  avatar: PropTypes.node,
  size: PropTypes.oneOf(['xs', 'sm', 'md']),
  deletable: PropTypes.bool,
  disabled: PropTypes.bool,
  icon: PropTypes.string,
  label: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  onDelete: PropTypes.func,
  variant: PropTypes.oneOf([
    'basic',
    'filter',
    'primary',
    'light',
    'info',
    'success',
    'success-light',
    'warn',
    'warn-light',
    'danger',
    'danger-light',
  ]),
}

/**
 *
 * @param {object} props
 * @param {object.children} props.children
 * @returns
 */
export function Tags({
  align = 'left',
  children,
  className = '',
  itemClass = '',
}) {
  const alignClass = alignOptions[align] || alignOptions.left
  return (
    <div
      className={`gap-2 flex flex-row flex-wrap items-center ${alignClass} ${className}`}
    >
      {React.Children.map(children, child =>
        child && itemClass ? <div className={itemClass}>{child}</div> : child
      )}
    </div>
  )
}
Tags.propTypes = {
  align: PropTypes.oneOf(['left', 'center', 'right']),
  className: PropTypes.string,
  children: PropTypes.node,
  itemClass: PropTypes.string,
}
