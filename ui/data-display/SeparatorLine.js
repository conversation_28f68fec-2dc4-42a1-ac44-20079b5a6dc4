import React from 'react'
import PropTypes from 'prop-types'

const variantsStyles = {
  primary: 'bg-primary-500',
  secondary: 'bg-secondary-500',
  gray: 'bg-gray-300',
}
export default function SeparatorLine({
  className = '',
  variant = 'secondary',
}) {
  const variantClass = variantsStyles[variant] || ''
  return <div className={`h-1 w-20 ${variantClass} ${className}`}></div>
}
SeparatorLine.propTypes = {
  className: PropTypes.string,
  variant: PropTypes.oneOf(['primary', 'secondary', 'gray']),
}
