import React, { useContext, useState, useCallback } from 'react'

const CarouselContext = React.createContext()

export default function CarouselProvider({
  children,
  automatic,
  duration,
  totalItems,
}) {
  const [isPaused, setIsPaused] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)

  const pauseCarousel = useCallback(() => {
    setIsPaused(true)
  }, [])

  const resumeCarousel = useCallback(() => {
    setIsPaused(false)
  }, [])

  return (
    <CarouselContext.Provider
      value={{
        automatic,
        duration,
        totalItems,
        isPaused,
        pauseCarousel,
        resumeCarousel,
        currentIndex,
        setCurrentIndex,
      }}
    >
      {children}
    </CarouselContext.Provider>
  )
}

export function useCarousel() {
  return useContext(CarouselContext)
}
