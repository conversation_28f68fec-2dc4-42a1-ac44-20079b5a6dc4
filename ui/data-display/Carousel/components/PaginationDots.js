import React from 'react'

import { useCarousel } from '../CarouselContext'

export default function PaginationDots() {
  const { currentIndex, totalItems } = useCarousel()
  const dotsArray = Array.from({ length: totalItems }, (_, index) => index)
  return (
    <div className="absolute bottom-3 dark:bottom-0 md:bottom-6 lg:bottom-8 dark:lg:bottom-4 inset-x-0 z-30 flex justify-center items-center gap-1">
      {dotsArray.map((item, key) => (
        <div
          key={key}
          className={`h-1 md:h-2 rounded-full transition-all duration-700 ease-in-out ${currentIndex === key ? 'w-4 md:w-6 bg-white/80' : 'w-1 md:w-2 bg-white/20'}`}
        />
      ))}
    </div>
  )
}
