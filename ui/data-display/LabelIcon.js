import React from 'react'
import PropTypes from 'prop-types'

import clsx from 'clsx'
import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))
const ConditionalLink = dynamic(() =>
  import('ui/navigation/Link').then(m => m.ConditionalLink)
)

export default function LabelIcon({
  className = '',
  icon,
  iconClass = '',
  label,
  labelClass = '',
  to,
  onClick,
  reverse,
  vertical,
}) {
  const isLink = to || onClick

  return (
    <ConditionalLink
      to={to}
      onClick={onClick}
      condition={isLink}
      className="w-fit"
    >
      <span
        className={`flex gap-2 items-center leading-tight ${
          vertical
            ? `flex-col ${reverse ? 'flex-col-reverse' : ''}`
            : `flex-row ${reverse ? 'flex-row-reverse' : ''}`
        } ${isLink ? 'cursor-pointer' : ''} ${className}`}
      >
        {icon && <Icon className={iconClass} name={icon} />}
        <span
          className={clsx(
            'text-gray-800 dark:text-primary-dark-100 leading-tight',
            isLink && 'hover:text-gray-700 dark:hover:text-gray-300',
            labelClass
          )}
        >
          {label}
        </span>
      </span>
    </ConditionalLink>
  )
}
LabelIcon.propTypes = {
  className: PropTypes.string,
  icon: PropTypes.string,
  iconClass: PropTypes.string,
  label: PropTypes.node.isRequired,
  labelClass: PropTypes.string,
  vertical: PropTypes.bool,
  reverse: PropTypes.bool,
}
