import PropTypes from 'prop-types'
import { Fragment } from 'react'

import clsx from 'clsx'
import dynamic from 'next/dynamic'

import Button from 'ui/buttons/Button'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'

import Image from '../Image'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function Avatar({
  size = 'sm',
  title,
  subtitle,
  icon = 'user',
  iconColor = 'primary-800',
  iconBackgroundColor = 'primary-100',
  description,
  className,
  url,
  callToAction,
  image,
}) {
  return (
    <div
      className={clsx(
        'flex items-stretch text-gray-800 dark:text-primary-dark-100',
        {
          'space-x-2': size === 'sm',
          'space-x-2 md:space-x-4': size === 'md' || size === 'lg',
          'space-x-2 md:space-x-4 lg:space-x-8': size === 'xl',
        },
        className
      )}
    >
      {image ? (
        <Image
          file={image}
          sizes="256px"
          className={clsx(
            'shrink-0 overflow-hidden rounded-full object-cover',
            {
              'h-12 w-12': size === 'sm',
              'h-14 w-14 md:h-16 md:w-16': size === 'md',
              'h-14 w-14 md:h-24 md:w-24': size === 'lg',
              'h-20 w-20 md:h-24 md:w-24 lg:h-40 lg:w-40': size === 'xl',
            }
          )}
        />
      ) : (
        <div
          className={clsx(
            'shrink-0 flex items-center justify-center rounded-full',
            {
              'h-12 w-12 p-2': size === 'sm',
              'h-14 w-14 p-2 md:h-16 md:w-16 md:p-6': size === 'md',
              'h-14 w-14 p-2 md:h-24 md:w-24 md:p-6': size === 'lg',
              'h-20 w-20 p-2 md:h-24 md:w-24 md:p-6 lg:h-40 lg:w-40 lg:p-8':
                size === 'xl',
            },
            getBackgroundColor(iconBackgroundColor)
          )}
        >
          <Icon
            name={icon}
            className={clsx('w-full h-full', getTextColor(iconColor))}
            size="100%"
          />
        </div>
      )}
      <div className="flex items-center">
        <div
          className={clsx('flex flex-col', {
            'space-y-0': size === 'sm' || size === 'md',
            'space-y-1 md:space-y-2': size === 'lg',
            'space-y-1 md:space-y-2 lg:space-y-3': size === 'xl',
          })}
        >
          {title && (
            <h4
              className={clsx('font-semibold', {
                'text-[0.9rem]/4': size === 'sm',
                'text-sm md:text-lg/6': size !== 'sm',
              })}
            >
              {title}
            </h4>
          )}
          <div>
            {subtitle && (
              <p
                className={clsx({
                  'text-sm': size === 'sm',
                  'text-sm md:text-base': size !== 'sm',
                })}
              >
                {subtitle}
              </p>
            )}

            <p
              className={clsx('font-normal', {
                'text-sm': size === 'sm',
                'text-sm md:text-base': size !== 'sm',
              })}
            >
              {typeof description === 'string'
                ? description.split(/\r?\n/).map((text, index) => (
                    <Fragment key={index}>
                      {index > 0 && <br />}
                      {text}
                    </Fragment>
                  ))
                : description}
            </p>
          </div>
          {url && callToAction && (
            <div>
              <Button
                url={url}
                label={callToAction}
                icon="arrow-right-long"
                variant="secondary"
                size={size === 'sm' ? 'xs' : 'sm'}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

Avatar.propTypes = {
  icon: PropTypes.string,
  iconColor: PropTypes.string,
  iconBackgroundColor: PropTypes.string,
  className: PropTypes.string,
  title: PropTypes.string,
  subtitle: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  description: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  url: PropTypes.string,
  callToAction: PropTypes.string,
  image: PropTypes.object,
  dynamicPerson: PropTypes.bool,
  person: PropTypes.object,
  personRole: PropTypes.object,
  personRoles: PropTypes.arrayOf(PropTypes.string),
  size: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
  ]),
}
