import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'

const Button = dynamic(() => import('ui/buttons/Button'))
const Link = dynamic(() => import('ui/navigation/Link'))
const Image = dynamic(() => import('ui/data-display/Image'))

const transformOrigin = {
  center: 'origin-center',
  left: 'origin-left',
  right: 'origin-right',
}

export default function Poster({
  enableAnimation,
  animationOrigin = 'center',
  animateImage = true,
  backgroundImage,
  callToActionLabel,
  callToActionIcon,
  className = '',
  description,
  enableCallToAction,
  enableTitle,
  enableDescription,
  image,
  title,
  url,
}) {
  const isLg = useMatchMedia(media.lg)

  return (
    <div
      className={`group/poster relative aspect-3/4 overflow-hidden rounded-lg bg-white dark:bg-primary-dark-700 shadow ${
        enableAnimation
          ? `transition-all duration-300 hover:scale-105 ${transformOrigin[animationOrigin]}`
          : ''
      } ${className}`}
    >
      <Link
        to={url}
        className={`block aspect-3/4 xhidden ${
          animateImage && enableAnimation
            ? 'group-hover/poster:scale-110 duration-300 transition-all'
            : ''
        }`}
      >
        {(enableTitle || enableDescription) && isLg && (
          <div className="-my-2 opacity-0 group-hover/poster:opacity-100 group-hover/poster:-m-0 flex transition-all duration-500 absolute z-10 inset-0 bg-gradient-to-t from-black to-transparent items-end justify-center">
            <div className="text-center text-white p-4 space-y-1 mb-6">
              {enableTitle && title && (
                <div className="font-semibold text-lg leading-tight">
                  {title}
                </div>
              )}
              {enableDescription && description && (
                <p className="line-clamp-3 scale-90">{description}</p>
              )}
              {enableCallToAction && callToActionLabel && (
                <div className="dark pt-2">
                  <Button
                    className="cursor-pointer"
                    icon={callToActionIcon}
                    label={callToActionLabel}
                    size="xs"
                    variant="hollow"
                  />
                </div>
              )}
            </div>
          </div>
        )}
        {backgroundImage && (
          <Image
            alt={title}
            aspectRatio="3/4"
            className="absolute scale-125 blur-xl"
            file={backgroundImage}
            sizes="200px sm:350px"
          />
        )}
        <div className="absolute top-1/2 w-full -translate-y-1/2">
          {image ? (
            <Image alt={title} file={image} sizes="200px sm:350px" />
          ) : (
            <div className="centered p-4 text-center font-semibold text-gray-400 text-lg md:p-10 md:text-2xl">
              <div className="overflow-hidden">{title}</div>
            </div>
          )}
        </div>
      </Link>
    </div>
  )
}
Poster.propTypes = {
  enableAnimation: PropTypes.bool,
  animateImage: PropTypes.bool,
  animationOrigin: PropTypes.oneOf(['center', 'left', 'right']),
  callToActionLabel: PropTypes.string,
  callToActionIcon: PropTypes.string,
  className: PropTypes.string,
  backgroundImage: PropTypes.object,
  description: PropTypes.string,
  enableCallToAction: PropTypes.bool,
  enableTitle: PropTypes.bool,
  enableDescription: PropTypes.bool,
  image: PropTypes.object,
  title: PropTypes.string,
  url: PropTypes.string,
}
