import React from 'react'
import PropTypes from 'prop-types'

import clsx from 'clsx'
import BackgroundImage from 'ui/data-display/BackgroundImage'
import { getBackgroundColor } from 'ui/helpers/getColor'

import BackgroundVideo from './BackgroundVideo'

const textClasses = {
  default: 'text-gray-700',
  dark: 'text-white dark:text-gray-700',
  simple: 'dark:text-white',
}

const bgClasses = {
  default: 'bg-white dark:bg-primary-dark-700',
  dark: 'bg-primary-700 dark:bg-white',
  simple: '',
}

export default function Box({
  children,
  className = 'p-6',
  innerClass = '',
  backgroundColor,
  bgImage,
  bgType = 'image',
  bgVideoAccountId,
  bgVideoId,
  bgVideoProvider,
  dark,
  id,
  simple,
}) {
  if (!children) return null

  // const [hasContent, setHasContent] = React.useState(true)
  // const innerDivRef = React.useRef()

  // React.useEffect(() => {
  //   if (hasContent && innerDivRef.current?.innerHTML === '') {
  //     setHasContent(false)
  //   }
  // }, [hasContent])

  // if (!children || !hasContent) return null

  const bgColorClass = backgroundColor
    ? getBackgroundColor(backgroundColor)
    : bgClasses[simple ? 'simple' : dark ? 'dark' : 'default']
  const textClass = textClasses[dark ? 'dark' : simple ? 'simple' : 'default']
  const styleClass = simple ? '' : 'rounded-lg shadow'

  return (
    <div
      className={`relative ${dark ? 'dark' : ''} ${styleClass} ${textClass} ${bgColorClass} ${className}`}
      id={id}
    >
      {bgType === 'video' && (
        <BackgroundVideo
          accountId={bgVideoAccountId}
          dark={dark}
          poster={bgImage}
          provider={bgVideoProvider}
          videoId={bgVideoId}
        />
      )}
      {bgType !== 'video' && bgImage && (
        <BackgroundImage
          className={`${simple ? '' : 'rounded-lg'}`}
          image={bgImage}
          dark={dark}
        />
      )}
      <div
        className={clsx('relative h-full', innerClass)}
        // ref={innerDivRef}
      >
        {children}
      </div>
    </div>
  )
}
Box.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  backgroundColor: PropTypes.string,
  bgImage: PropTypes.object,
  bgType: PropTypes.oneOf(['image', 'video']),
  bgVideoAccountId: PropTypes.string,
  bgVideoId: PropTypes.string,
  // Only supports Cloudflare at the moment
  bgVideoProvider: PropTypes.oneOf(['cloudflare']),
  dark: PropTypes.bool,
  id: PropTypes.string,
  innerClass: PropTypes.string,
  simple: PropTypes.bool,
}
