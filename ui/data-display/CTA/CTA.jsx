import React from 'react'

import clsx from 'clsx'

import But<PERSON> from 'ui/buttons/Button'

const variantClasses = {
  default: {
    wrapper: 'border-primary-500 bg-neutral-50',
    title: 'text-neutral-900',
    description: 'text-neutral-700',
  },
  primary: {
    wrapper: 'border-primary-900 bg-primary-900',
    title: 'text-white',
    description: 'text-white',
  },
  secondary: {
    wrapper: 'border-primary-500 bg-white',
    title: 'text-primary-900',
    description: 'text-primary-500',
  },
}

const ctaButtonVariantMap = {
  default: 'hollow',
  primary: 'alt-outline',
  secondary: 'default',
}

/**
 * Call to Action component
 * @param {string} id The id of the CTA
 * @param {string} className Additional classes for the CTA
 * @param {string} description Description of the CTA
 * @param {string} title Title of the CTA
 * @param {string} url URL for the CTA button
 * @param {string} ctaLabel Label for the CTA button
 * @param {'default' | 'primary' | 'secondary'} [variant='default'] The variant of the CTA
 * @returns {React.ReactElement} The CTA component
 */
export default function CTA({
  children,
  className,
  id,
  description,
  title,
  url,
  ctaLabel,
  variant = 'default',
}) {
  const ctaButtonVariant = ctaButtonVariantMap[variant]

  return (
    <div id={id} className={clsx('not-prose py-2', className)}>
      <div
        className={clsx(
          'flex flex-col gap-4 border-l-4 p-6 ps-8',
          variantClasses[variant].wrapper,
          className
        )}
      >
        {title && (
          <h4
            className={clsx(
              'text-3xl font-bold',
              variantClasses[variant].title
            )}
          >
            {title}
          </h4>
        )}
        <div className={clsx('', variantClasses[variant].description)}>
          {children || description}
        </div>

        <div>
          <Button href={url} variant={ctaButtonVariant} label={ctaLabel} />
        </div>
      </div>
    </div>
  )
}
