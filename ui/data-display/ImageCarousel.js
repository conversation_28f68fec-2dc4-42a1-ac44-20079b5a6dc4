import React, { useCallback, useState, useEffect } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import Image, {
  getResponsiveSizes,
  useImageLoader,
} from 'ui/data-display/Image'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function Carousel({
  automatic = true,
  className = '',
  duration = 5000,
  id,
  imagePosition = 'center center',
  items,
  quality,
}) {
  const [current, setCurrent] = useState(0)

  useEffect(() => {
    let interval

    if (automatic) {
      interval = setInterval(() => {
        setCurrent(current < items.length - 1 ? current + 1 : 0)
      }, duration)
    }
    return () => clearInterval(interval)
  }, [automatic, current, duration, items])

  const onPrev = useCallback(() => {
    setCurrent(current > 0 ? current - 1 : items.length - 1)
  }, [current, items])

  const onNext = useCallback(() => {
    setCurrent(current < items.length - 1 ? current + 1 : 0)
  }, [current, items])

  return (
    <div className={`relative ${className}`} id={id}>
      <div className="relative flex aspect-video flex-row">
        {items.map((item, i) => (
          <div
            className={`absolute inset-0 transition-opacity duration-300 ease-in-out ${
              current === i ? 'z-20 opacity-100' : 'z-10 opacity-0'
            }`}
            key={`carousel-item-${i}`}
          >
            <CarouselItem
              {...item}
              quality={quality}
              imagePosition={imagePosition}
              hasPriority={i === 0}
            />
          </div>
        ))}
      </div>
      {items.length > 1 && (
        <>
          <NavButton onClick={onPrev} icon="chevron-left" className="left-0" />
          <NavButton
            onClick={onNext}
            icon="chevron-right"
            className="right-0"
          />
        </>
      )}
    </div>
  )
}
Carousel.propTypes = {
  automatic: PropTypes.bool,
  className: PropTypes.string,
  duration: PropTypes.number,
  quality: PropTypes.number,
  id: PropTypes.string,
  items: PropTypes.arrayOf(
    PropTypes.shape({ alt: PropTypes.string, file: PropTypes.object })
  ),
  imagePosition: PropTypes.string,
}

const positions = {
  'top left': 'items-start justify-start',
  'top': 'items-start justify-center',
  'top right': 'items-start justify-end',
  'center left': 'items-center justify-start',
  'center': 'items-center justify-center',
  'center right': 'items-center justify-end',
  'bottom left': 'items-end justify-start',
  'bottom': 'items-end justify-center',
  'bottom right': 'items-end justify-end',
}

function CarouselItem({
  callToAction,
  description,
  hasPriority,
  image,
  imagePosition = 'center center',
  quality = 80,
  textPosition,
  title,
  url,
}) {
  const imageLoader = useImageLoader(image)

  return (
    <Link to={url} className="relative block h-full w-full select-none">
      <div className="h-full w-full overflow-hidden rounded-md bg-gray-200">
        {image && (
          <Image
            className="h-full w-full centered:rounded-md"
            file={image}
            alt={title}
            loader={imageLoader}
            quality={quality}
            priority={hasPriority}
            lazy={false}
            sizes={getResponsiveSizes('lg:1920px md:1280px 1024px')}
            objectFit="cover"
            position={imagePosition}
          />
        )}
      </div>
      {(title || description || callToAction) && (
        <div
          className={`absolute inset-0 flex p-16 lg:p-32 ${positions[textPosition]}`}
        >
          <div className="flex flex-col items-center space-y-2 rounded-xl text-white lg:space-y-8">
            <div className="flex flex-col items-center space-y-2 text-center lg:space-y-4">
              <h3 className="relative font-black leading-6 drop-shadow-xl text-3xl md:text-4xl lg:text-6xl">
                <span className="text-black text-opacity-50">{title}</span>
                <span
                  className="absolute inset-0 z-0 -translate-x-0.5 -translate-y-0.5 text-white"
                  aria-hidden
                >
                  {title}
                </span>
              </h3>
              {description && (
                <p className="relative font-semibold leading-4 drop-shadow-md md:leading-5 md:text-lg lg:leading-8 lg:text-3xl">
                  <span className="text-black text-opacity-50">
                    {description}
                  </span>
                  <span
                    className="absolute inset-0 z-0 -translate-x-px -translate-y-px text-white"
                    aria-hidden
                  >
                    {description}
                  </span>
                </p>
              )}
            </div>
            {callToAction && (
              <span className="cursor-pointer rounded-md bg-primary-700 px-3 py-2 font-bold uppercase shadow-lg">
                {callToAction}
              </span>
            )}
          </div>
        </div>
      )}
    </Link>
  )
}
CarouselItem.propTypes = {
  callToAction: PropTypes.string,
  description: PropTypes.string,
  hasPriority: PropTypes.bool,
  image: PropTypes.object,
  quality: PropTypes.number,
  textPosition: PropTypes.string,
  imagePosition: PropTypes.string,
  title: PropTypes.string,
  url: PropTypes.string,
}

function NavButton({ className = '', icon, onClick }) {
  return (
    <Clickable
      onClick={onClick}
      className={`absolute scale-75 md:scale-100 bottom-0 top-0 z-30 flex items-center justify-center p-0 lg:p-6 ${className}`}
    >
      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-white bg-opacity-0 p-4 text-white text-opacity-80 transition-all duration-300 ease-in-out text-2xl hover:bg-opacity-25 hover:text-opacity-90 lg:text-4xl xl:p-8">
        <Icon name={icon} className="drop-shadow-md" />
      </div>
    </Clickable>
  )
}
NavButton.propTypes = {
  className: PropTypes.string,
  icon: PropTypes.string,
  onClick: PropTypes.func,
}
