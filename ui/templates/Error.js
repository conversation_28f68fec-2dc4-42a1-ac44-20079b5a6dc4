/* eslint-disable @next/next/no-img-element */

import React from 'react'
import PropTypes from 'prop-types'

export default function ErrorTemplate({
  title = 'Error',
  message = 'Something went wrong',
}) {
  return (
    <div className="flex h-screen items-center justify-center">
      <div className="flex flex-col items-center justify-center gap-6 p-12 text-center md:gap-8 lg:gap-12">
        <img
          src="/images/hope-channel-logo-color.svg"
          alt="HopeChannel Logo"
          className="w-64 transition-all md:w-80 lg:w-96"
        />
        <div className="flex max-w-md flex-col gap-3 lg:max-w-lg">
          <h1 className="font-bold text-danger-600 transition-all text-2xl md:text-3xl lg:text-4xl">
            {title}
          </h1>
          {message && (
            <p className="font-semibold text-gray-700 text-lg">{message}</p>
          )}
        </div>
      </div>
    </div>
  )
}
ErrorTemplate.propTypes = {
  title: PropTypes.string,
  message: PropTypes.string,
}
