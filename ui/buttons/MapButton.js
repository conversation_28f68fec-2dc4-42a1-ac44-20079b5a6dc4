import React from 'react'
import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const HeaderButton = dynamic(() => import('./HeaderButton'))

export default function MapButton({ className = '', url }) {
  const { t } = useTranslation()
  if (!url) return null

  return (
    <HeaderButton
      label={t('network')}
      icon="globe"
      url={url}
      className={className}
    />
  )
}
MapButton.propTypes = {
  className: PropTypes.string,
  url: PropTypes.string,
}
