import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import Link from 'next/link'

import { useCloseNavOnClick } from 'ui/navigation/NavigationContext'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function HeaderButton({ className = '', icon, label, url }) {
  const handleClick = useCloseNavOnClick({ href: url })

  if (!url || !label) return null

  return (
    <Link
      href={url}
      passHref
      className={`flex flex-row items-center space-x-2 rtl:space-x-reverse ${className}`}
      onClick={handleClick}
      tabIndex={0}
    >
      <Icon
        name={icon}
        className="text-gray-700 dark:text-primary-dark-100 text-xl lg:text-2xl"
      />
      <span className="font-bold text-xl lg:hidden">{label}</span>
    </Link>
  )
}
HeaderButton.propTypes = {
  className: PropTypes.string,
  icon: PropTypes.string,
  label: PropTypes.string,
  url: PropTypes.string,
}
