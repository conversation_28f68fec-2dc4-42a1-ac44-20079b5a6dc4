import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

const variantsStyles = {
  'primary':
    'bg-primary-600 text-white border-transparent dark:bg-primary-dark-300 dark:text-primary-dark-700 dark:hover:text-primary-dark-800 hover:bg-primary-700 duration-200',
  'secondary':
    'bg-transparent text-primary-700 border-primary-700 dark:text-primary-dark-100 dark:hover:text-white dark:border-primary-dark-100 dark:hover:bg-transparent dark:hover:border-white hover:bg-primary-700 hover:text-white duration-200',
  'tertiary':
    'text-primary-700 dark:text-primary-dark-100 hover:text-primary-800 rounded-none leading-none border-transparent',
  'hollow':
    'text-primary-700 dark:text-white rounded border-primary-700 dark:border-white',
  'alt': 'bg-secondary-500 text-gray-800 border-secondary-500',
  'alt-outline':
    'border border-secondary-500 text-white bg-opacity-20 bg-black',
  'link':
    'bg-transparent text-gray-800 dark:text-primary-dark-100 border-transparent',
  'linkLight':
    'bg-transparent text-white dark:text-primary-dark-800 border-transparent',
  'disabled':
    'bg-gray-200 text-gray-500 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed border-transparent',
  'danger':
    'bg-danger-500 text-white border-transparent dark:bg-white dark:text-danger-700 hover:bg-danger-600 duration-200',
}

const sizesStyles = {
  xs: 'py-1 px-1 text-xs lg:px-2 min-h-[1.5rem]',
  sm: 'py-2 px-3 text-sm lg:px-3 min-h-[2rem]',
  md: 'py-2 px-3 text-md lg:px-4 min-h-[2.5rem]',
  lg: 'py-3 px-4 text-lg lg:px-6 min-h-[3rem]',
  xl: 'py-3 px-5 text-xl lg:px-8 min-h-[4rem]',
}

const iconSizesStyles = {
  xs: 'text-sm',
  sm: 'text-md',
  md: 'text-lg',
  lg: 'text-xl',
  xl: 'text-2xl ',
}

export default function Button({
  children,
  className = '',
  disabled,
  fullWidth,
  icon,
  iconClass = '',
  iconPosition = 'left',
  id,
  label,
  onClick,
  size = 'md',
  title,
  url,
  type = 'button',
  variant = 'primary',
}) {
  const variantClasses = disabled
    ? variantsStyles.disabled
    : `${variantsStyles[variant] || variantsStyles.primary}`

  const sizeClasses = sizesStyles[size] || sizesStyles.md
  const iconSizeClass = iconSizesStyles[size] || iconSizesStyles.md

  return (
    <Link
      as={url ? 'a' : 'button'}
      className={`whitespace-no-wrap inline-flex select-none items-center justify-center gap-2 rounded border-2 text-center font-bold uppercase leading-none focus:outline-none ${!disabled ? 'cursor-pointer' : ''} ${variantClasses} ${sizeClasses} ${
        fullWidth ? 'w-full' : ''
      } ${iconPosition === 'right' ? 'flex-row-reverse' : ''} ${className}`}
      disabled={disabled}
      id={id}
      to={disabled ? null : url}
      onClick={disabled ? null : onClick}
      title={title}
      type={url ? undefined : type}
    >
      {icon && <Icon className={`${iconSizeClass} ${iconClass}`} name={icon} />}
      {label && <span>{label ? label : children}</span>}
    </Link>
  )
}

Button.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool,
  id: PropTypes.string,
  icon: PropTypes.string,
  iconClass: PropTypes.string,
  iconPosition: PropTypes.oneOf(['left', 'right']),
  label: PropTypes.string,
  onClick: PropTypes.func,
  size: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  url: PropTypes.string,
  title: PropTypes.string,
  type: PropTypes.oneOf(['button', 'submit', 'cancel']),
  variant: PropTypes.oneOf([
    'alt',
    'alt-outline',
    'primary',
    'hollow',
    'link',
    'linkLight',
    'secondary',
    'tertiary',
    'tertiary-danger',
    'danger',
    'dang',
  ]),
}
