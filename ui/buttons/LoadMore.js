import PropTypes from 'prop-types'
import React from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const Button = dynamic(() => import('ui/buttons/Button'))

export default function LoadMore({
  label,
  onClick,
  loading,
  variant = 'primary',
}) {
  const { t } = useTranslation()
  return (
    <div className="flex items-center justify-center px-4 py-16">
      <Button
        disabled={loading}
        icon={loading ? 'spinner-third' : ''}
        iconClass="animate-spin"
        label={label || t('loadMore')}
        onClick={onClick}
        variant={variant}
      />
    </div>
  )
}
LoadMore.propTypes = {
  label: PropTypes.string,
  loading: PropTypes.bool,
  onClick: PropTypes.func,
  variant: PropTypes.oneOf([
    'alt',
    'primary',
    'hollow',
    'link',
    'linkLight',
    'secondary',
    'tertiary',
  ]),
}
