import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'
import { isArray } from 'utils/types'

const Button = dynamic(() => import('ui/buttons/Button'))

const alignOptions = {
  left: 'justify-start',
  center: 'justify-center',
  right: 'justify-end',
}

export default function ButtonGroup({
  align = 'left',
  children,
  className = '',
  items,
  ...commonProps
}) {
  const alignClass = alignOptions[align] || alignOptions.left

  return (
    <div
      className={`flex flex-wrap items-center gap-2 rtl:space-x-reverse ${alignClass} ${className}`}
    >
      {isArray(items)
        ? items.map((item, i) => (
            <Button key={`bg-${i}`} {...commonProps} {...item} />
          ))
        : children}
    </div>
  )
}
ButtonGroup.propTypes = {
  align: PropTypes.oneOf(['left', 'center', 'right']),
  children: PropTypes.node,
  className: PropTypes.string,
  items: PropTypes.array,
}
