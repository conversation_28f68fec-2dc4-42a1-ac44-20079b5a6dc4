import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { useCloseNavOnClick } from 'ui/navigation/NavigationContext'

const Button = dynamic(() => import('ui/buttons/Button'))

export default function CTAButton({ id, label, url }) {
  const handleLogoClick = useCloseNavOnClick({ href: url })

  if (!url || !label) return null
  return (
    <Button
      id={id}
      url={url}
      label={label}
      size="sm"
      onClick={handleLogoClick}
    />
  )
}
CTAButton.propTypes = {
  id: PropTypes.string,
  label: PropTypes.string,
  url: PropTypes.string,
}
