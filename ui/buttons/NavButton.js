import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Clickable = dynamic(() => import('ui/helpers/Clickable'))

export default function NavButton({ active, className = '', onClick }) {
  return (
    <Clickable className={className} onClick={onClick}>
      <div className="flex flex-row items-center justify-center w-5 h-5">
        <span
          className={`relative block w-full h-0.5 before:bg-primary-700 dark:before:bg-gray-300 before:w-full before:h-0.5 before:block before:absolute after:bg-primary-700 dark:after:bg-gray-300 after:w-full after:h-0.5 after:block after:absolute transition-all duration-200 ease-out before:transition-all before:duration-200 before:ease-out after:transition-all after:duration-200 after:ease-out  ${
            active
              ? 'bg-transparent before:rotate-45 after:-rotate-45 before:top-0 after:top-0'
              : 'bg-primary-700 dark:bg-gray-300 before:-top-1.5 after:top-1.5'
          }`}
        />
      </div>
    </Clickable>
  )
}
NavButton.propTypes = {
  active: PropTypes.bool,
  className: PropTypes.string,
  onClick: PropTypes.func,
}
