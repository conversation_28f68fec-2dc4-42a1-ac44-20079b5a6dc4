function parseJoiErrorType(type) {
  switch (type) {
    case 'any.required':
      return 'REQUIRED'
    case 'string.email':
      return 'INVALID_EMAIL'
    case 'string.min':
      return 'TOO_SHORT'
    case 'string.max':
      return 'TOO_LONG'
    case 'string.pattern.base':
      return 'INVALID_FORMAT'
    case 'string.uri':
      return 'INVALID_URL'
    case 'string.empty':
      return 'REQUIRED'
    case 'number.base':
      return 'INVALID_NUMBER'
    case 'number.min':
      return 'TOO_SMALL'
    case 'number.max':
      return 'TOO_BIG'
    case 'boolean.base':
      return 'INVALID_BOOLEAN'
    case 'date.base':
      return 'INVALID_DATE'
    case 'date.min':
      return 'TOO_EARLY'
    case 'date.max':
      return 'TOO_LATE'
    case 'array.includesRequiredUnknowns':
      return 'REQUIRED'
    case 'array.includes':
      return 'INVALID_ARRAY'
    case 'array.min':
      return 'TOO_SHORT'
    case 'array.max':
      return 'TOO_LONG'
    case 'array.length':
      return 'INVALID_ARRAY'
    case 'object.base':
      return 'INVALID_OBJECT'
    case 'object.unknown':
      return 'INVALID_OBJECT'
    case 'object.min':
      return 'TOO_SHORT'
    case 'object.max':
      return 'TOO_LONG'
    case 'object.length':
      return 'INVALID_OBJECT'
    case 'alternatives.match':
      return 'INVALID_FORMAT'
    // Custom error messages
    case 'unique':
      return 'DUPLICATE'
    default:
      return 'INVALID'
  }
}

function parseJoiError(error) {
  const { path, type } = error

  if (path && type) {
    return {
      [path.join('.')]: {
        message: `VALIDATION_ERROR_${parseJoiErrorType(type)}`,
      },
    }
  }

  return {}
}

export function parseAPIError(error) {
  if (!error) {
    return null
  }

  if (
    error.status === 400 &&
    error.code === 'VALIDATION_ERROR' &&
    Array.isArray(error.parsed)
  ) {
    const errors = error.parsed

    return errors.reduce((acc, error) => {
      return {
        ...acc,
        ...parseJoiError(error),
      }
    }, {})
  }

  return null
}
