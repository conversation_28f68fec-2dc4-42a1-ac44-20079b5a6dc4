import axios from 'axios'

// Default configuration for axios
axios.defaults.baseURL = process.env.NEXT_PUBLIC_API_URL
axios.defaults.headers.post['Content-Type'] = 'application/json'
axios.defaults.headers.common['ClientToken'] =
  process.env.NEXT_PUBLIC_API_CLIENT_TOKEN
axios.defaults.validateStatus = status => status >= 200 && status < 500

/**
 * Default fetch for "get" methods
 * @param {string} url
 * @returns `Promise`
 */
export async function getFetch(url, params = {}) {
  if (!url) return

  try {
    return await axios.get(url, { params }).then(res => res.data)
  } catch (error) {
    throw error.response
  }
}

/**
 * Default fetch for "post" methods
 * @param {string} url
 * @param {object} data
 * @param {object} config
 * @returns `Promise`
 */
export async function postFetch(url, data = {}, config = {}) {
  if (!url) return

  try {
    return await axios.post(url, data, config).then(res => res.data)
  } catch (error) {
    throw error.response
  }
}

/**
 * Default fetch for "patch" methods
 * @param {string} url
 * @param {object} data
 * @param {object} config
 * @returns `Promise`
 */
export async function patchFetch(url, data = {}, config = {}) {
  if (!url) return

  try {
    return await axios.patch(url, data, config).then(res => res.data)
  } catch (error) {
    throw error.response
  }
}

/**
 * Default fetch uploading files
 * @param {string} url
 * @param {object} data
 * @param {object} config
 * @returns `Promise`
 */
export async function fileUpload(url, data = {}, onProgress, config = {}) {
  if (!url) return

  var formData = new FormData()
  formData.append('file', data)

  if (typeof onProgress === 'function') {
    config.onUploadProgress = ({ loaded, total }) =>
      onProgress(Math.round((loaded * 100) / total), loaded, total)
  }

  try {
    return await axios
      .post(url, formData, {
        ...config,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      .then(res => res.data)
  } catch (error) {
    throw error.response
  }
}

/**
 * Default fetch for "patch" methods
 * @param {string} url
 * @param {object} config
 * @returns `Promise`
 */
export async function deleteFetch(url, config = {}) {
  if (!url) return

  try {
    return await axios.delete(url, config).then(res => res.data)
  } catch (error) {
    throw error.response
  }
}
