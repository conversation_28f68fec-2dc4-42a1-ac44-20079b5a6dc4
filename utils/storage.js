import { useCallback } from 'react'
import { useQuery, useQueryClient } from 'react-query'

/**
 * Save a value to local storage
 * @param {string} key
 * @param {any} value - value to be stored in local storage
 */
export function setLocalStorage(key, value) {
  // Can't always set local storage, so we need to catch errors
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error('Error saving to local storage', error) // eslint-disable-line no-console
    // TODO: Should we send something back to the function that called this?
  }
}

/**
 * Get a value from local storage
 * @param {string} key
 * @returns {any} - value from local storage
 */
export function getLocalStorage(key) {
  try {
    return JSON.parse(localStorage.getItem(key))
  } catch (error) {
    console.error('Error getting from local storage', error) // eslint-disable-line no-console
  }
}

/**
 * Provides a hook to use localStorage with react-query
 * @param {String} key The key to read/store the value from/under
 * @param {any} defaultValue The default value to use if the key is not set
 * @returns {[any, LocalStorageSetValue, LocalStorageRemove]} `[value, setValue, remove]`
 */
export function useLocalStorageQuery(key, defaultValue) {
  const queryClient = useQueryClient()

  const { data } = useQuery(
    ['storage', key, defaultValue],
    () => Promise.resolve(localStorage.getItem(key) || defaultValue),
    { enabled: !!key }
  )

  /**
   * @type {LocalStorageSetValue}
   */
  const saveValue = useCallback(
    newValue => {
      // If the value is undefined, remove the key from the storage
      if (newValue === undefined) {
        localStorage.removeItem(key)
      } else {
        // Otherwise set the value in the storage
        localStorage.setItem(key, newValue)
      }

      // Invalidate the query to refetch the data
      queryClient.invalidateQueries(['storage', key])
    },
    [key, queryClient]
  )

  /**
   * @type {LocalStorageRemove}
   */
  const remove = useCallback(() => {
    saveValue(undefined)
  }, [saveValue])

  return [data, saveValue, remove]
}
