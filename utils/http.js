export class APIError extends <PERSON>rror {
  constructor(message, status) {
    super(message)
    try {
      const parsedMessage = JSON.parse(message)
      if (Array.isArray(parsedMessage)) {
        this.code = 'VALIDATION_ERROR'
        this.parsed = parsedMessage
      } else {
        this.code = parsedMessage.code
        this.parsed = parsedMessage
      }
    } catch (error) {
      this.code = 'UNKNOWN_ERROR'
    } finally {
      this.name = 'APIError'
      this.status = status
    }
  }
}

export async function client(method, endpoint, params = {}, customConfig = {}) {
  const { baseURL = process.env.NEXT_PUBLIC_API_URL, ...customConfigOptions } =
    customConfig
  const headers = {
    ClientToken: process.env.NEXT_PUBLIC_API_CLIENT_TOKEN ?? '',
  }

  // Convert params object to query string
  const queryString =
    method === 'GET'
      ? Object.entries(params)
          .map(
            ([key, value]) =>
              `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
          )
          .join('&')
      : ''

  const url = `${baseURL}${endpoint}${queryString ? `?${queryString}` : ''}`

  const config = {
    method,
    ...customConfigOptions,
    headers: {
      ...headers,
      ...customConfigOptions.headers,
    },
  }

  if (method !== 'GET') {
    if (!('Content-Type' in (config.headers ?? {}))) {
      config.headers = {
        ...config.headers,
        'Content-Type': 'application/json',
      }
      config.body = JSON.stringify(params)
    } else {
      config.body = params
    }
  }

  const response = await fetch(url, config)

  if (response.ok) {
    // Only set the data when we have a 200 response
    if (response.status === 200) {
      return await response.json()
    }
    return Promise.resolve({})
  } else {
    const errorMessage = await response.text()
    return Promise.reject(new APIError(errorMessage, response.status))
  }
}

/**
 * Default fetch for "get" methods
 * @param {string} url
 * @returns `Promise`
 */
export async function getFetch(url, params = {}, config = {}) {
  if (!url) {
    throw new Error('URL is undefined')
  }

  const res = await client('GET', url, params, config)
  return res
}

/**
 * Default fetch for "post" methods
 * @param {string} url
 * @param {object} data
 * @param {object} config
 * @returns `Promise`
 */
export async function postFetch(url, data = {}, config = {}) {
  if (!url) {
    throw new Error('URL is undefined')
  }

  return await client('POST', url, data, config)
}

/**
 * Default fetch for "patch" methods
 * @param {string} url
 * @param {object} data
 * @param {object} config
 * @returns `Promise`
 */
export async function patchFetch(url, data = {}, config = {}) {
  if (!url) {
    throw new Error('URL is undefined')
  }

  return await client('PATCH', url, data, config)
}

/**
 * Default fetch for "delete" methods
 * @param {string} url
 * @param {object} data
 * @param {object} config
 * @returns `Promise`
 */
export async function deleteFetch(url, data = {}, config = {}) {
  if (!url) {
    throw new Error('URL is undefined')
  }

  return await client('DELETE', url, data, config)
}
