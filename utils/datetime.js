import isBeforeDate from 'date-fns/isBefore'
import { format as _formatDate } from 'date-fns/format'
import formatDistanceToNow from 'date-fns/formatDistanceToNow'
import substractDate from 'date-fns/sub'
import startOfWeek from 'date-fns/startOfWeek'
import endOfWeek from 'date-fns/endOfWeek'

import de from 'date-fns/locale/de'
import enUS from 'date-fns/locale/en-US'
import es from 'date-fns/locale/es'
import fr from 'date-fns/locale/fr'
import it from 'date-fns/locale/it'
import pl from 'date-fns/locale/pl'
import nb from 'date-fns/locale/nb'
import sv from 'date-fns/locale/sv'

import { useTranslation } from 'next-i18next'
import { padStart } from 'lodash'

import { removeUndefined } from 'utils/objects'
import { capitalize } from './strings'

const locales = {
  de,
  en: enUS,
  es,
  fr,
  it,
  pl,
  no: nb,
  nb,
  sv,
}

function getLocale(locale) {
  return locales[locale] || locales.en
}

export function useDatetimeLocale() {
  const { i18n } = useTranslation()
  const locale = getLocale(i18n.language)

  const customLocale = {
    ...locale,
    localize: {
      ...locale.localize,
      day: (dayIndex, options) => {
        const localisedDay = locale.localize.day(dayIndex, options)
        // Norway uses capitalised days of week (https://github.com/hope-media/hope-frontend/issues/449#issuecomment-1560537241)
        if (locale.code === 'nb') {
          return capitalize(localisedDay)
        }
        return localisedDay
      },
      month: (monthIndex, options) => {
        // Norway uses capitalised months (https://github.com/hope-media/hope-frontend/issues/449#issuecomment-1560537241)
        const localisedMonth = locale.localize.month(monthIndex, options)
        if (locale.code === 'nb') {
          return capitalize(localisedMonth)
        }
        return localisedMonth
      },
    },
  }

  return customLocale
}

export function formatDate(dateValue, format = 'Pp', options = {}) {
  if (!dateValue) return null
  const { locale, ...others } = { locale: enUS, ...removeUndefined(options) }

  const date = new Date(dateValue)
  return _formatDate(date, format, { locale, ...others })
}

function timeSince(dateValue, format, options = {}) {
  const { maxDays, addSuffix, locale } = {
    maxDays: 3,
    addSuffix: true,
    locale: enUS,
    ...removeUndefined(options),
  }

  const date = new Date(dateValue)
  const now = new Date()
  const limitDate = substractDate(now, { days: maxDays })

  return isBeforeDate(date, limitDate)
    ? formatDate(date, format, { locale })
    : formatDistanceToNow(date, { addSuffix, locale: getLocale(locale) })
}

function messageTime(dateValue, options) {
  const { locale } = { locale: enUS, ...removeUndefined(options) }

  const date = new Date(dateValue)
  const now = new Date()
  const yesterday = substractDate(now, { days: 1 })
  const oneWeekAgo = substractDate(now, { days: 7 })

  const format = isBeforeDate(date, oneWeekAgo)
    ? 'P'
    : isBeforeDate(date, yesterday)
      ? 'EEE'
      : 'p'

  return formatDate(date, format, { locale })
}

// JSX Components

export function TimeSince({ date, format, maxDays, addSuffix }) {
  const locale = useDatetimeLocale()

  return timeSince(date, format, { maxDays, locale, addSuffix })
}

export function MessageTime({ date }) {
  const locale = useDatetimeLocale()

  return messageTime(date, { locale })
}

export function FormatDate({ date, format, maxDays, addSuffix }) {
  const locale = useDatetimeLocale()

  switch (format) {
    case 'relative':
      return messageTime(date, { locale })

    case 'since':
      return timeSince(date, { locale, maxDays, addSuffix })

    default: {
      return formatDate(date, format, { locale })
    }
  }
}

/**
 * Converts hours into seconds
 * @param {number} h hours
 * @returns `number` seconds
 */
export function h2s(h) {
  return h * 60 * 60
}

/**
 * Converts minutes into seconds
 * @param {number} m minutes
 * @returns `number` seconds
 */
export function m2s(m) {
  return m * 60
}

/**
 * Converts hms time to seconds
 * @param {string} hms time with format `hh:mm:ss` or `mm:ss` or `ss`
 * @returns `number`
 */
export function hmsToSeconds(hms = '00:00:00') {
  if (!hms) return 0

  const a = hms.split(':').map(x => +x)

  if (a.length === 3) {
    return h2s(a[0]) + m2s(a[1]) + a[2]
  } else if (a.length === 2) {
    return m2s(a[0]) + a[1]
  } else if (a.length === 1) {
    return a[0]
  }
}

/**
 * Converts hms time to Vimeo Time
 * @param {string} hms time with format `hh:mm:ss` or `mm:ss` or `ss`
 * @returns `string`
 */
export function hmsToVimeoT(hms = '00:00:00') {
  if (!hms) return 0

  const a = hms.split(':').map(x => +x)

  if (a.length === 3) {
    return `${a[0]}h${a[1]}m${a[2]}s`
  } else if (a.length === 2) {
    return `${a[0]}m${a[1]}s`
  } else if (a.length === 1) {
    return `${a[0]}s`
  }
}

export const defaultTimeFormatOptions = {
  format: ['hours', 'minutes', 'seconds'],
}

export function timeFormat({ hours, minutes, seconds }) {
  return `${hours > 0 ? `${minutes}:` : ''}${minutes}:${padStart(
    seconds,
    2,
    '0'
  )}`
}

/**
 * Format the time from seconds to H:M:SS. Other formats support through custom formatter fn.
 * @param  {Number} seconds Seconds to format.
 * @param  {Function} formatter formatter function, such as `formatDuration` from `date-fns. Defaults to custom H:M:SS formatter
 * @param  {Object} options formatter function options, e.g. `options` supported by `formatDuration` from `date-fns`
 * @return {String} Formatted time.
 */
export function formatTime(seconds, formatter = timeFormat, options = {}) {
  seconds = Number(seconds)
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds % 3600) / 60)
  const s = Math.ceil((seconds % 3600) % 60)

  return formatter(
    { hours: h, minutes: m, seconds: s },
    { ...defaultTimeFormatOptions, ...options }
  )
}

export const firstDayOfWeek = function (date = new Date()) {
  return startOfWeek(date, { weekStartsOn: 0 })
}

export const lastDayOfWeek = function (date = new Date()) {
  return endOfWeek(date, { weekStartsOn: 0 })
}
