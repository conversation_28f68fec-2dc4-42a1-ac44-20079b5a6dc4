export const languageLocaleMap = {
  // Europe
  en: 'en_US',
  es: 'es_ES',
  pt: 'pt_BR',
  fr: 'fr_FR',
  de: 'de_DE',
  cs: 'cs_CZ',
  da: 'da_DK',
  is: 'is_IS',
  nb: 'nb_NO',
  no: 'nb_NO',
  fi: 'fi_FI',
  sv: 'fi_SE',
  it: 'it_IT',
  pl: 'pl_PL',
  ro: 'ro_RO',
  hu: 'hu_HU',
  bg: 'bg_BG',
  uk: 'uk_UA',
  ru: 'ru_RU',

  // Asia: Middle-east
  tr: 'tr_TR',
  he: 'he_IL',
  ar: 'ar_SA',
  hy: 'hy_AM',
  ky: 'ky_KG',
  kk: 'kk_KZ',
  uz: 'uz_UZ',
  fa: 'fa_IR',

  // Asia: Far-east
  zh: 'zh_CN',
  ja: 'ja_JP',
  ko: 'ko_',
  hi: 'hi_IN',
  ml: 'ml_IN',
  kn: 'kn_IN',
  te: 'te_IN',
  ta: 'ta_IN',
  si: 'si_LK',
  tl: 'tl_PH',
  id: 'id_ID',
  ms: 'ms_MY',
  th: 'th_TH',

  // Africa
  sw: 'sw_KE',
  om: 'om_ET',
  am: 'am_ET',

  fj: 'fj_FJ',
  mi: 'mi_NZ',
  sm: 'sm_WS',
  to: 'to_TO',
}

export function languageToLocale(lang = '') {
  return languageLocaleMap[lang || 'en']
}

export function toISO2Locale(locale = 'en', delimiter = '-') {
  return locale.split(delimiter)[0]
}
