import PropTypes from 'prop-types'
import React, { useContext } from 'react'

export const FeatureFlagContext = React.createContext(null)

export function FeatureFlagsProvider({ featureFlags = {}, children }) {
  return (
    <FeatureFlagContext.Provider value={featureFlags}>
      {children}
    </FeatureFlagContext.Provider>
  )
}

FeatureFlagsProvider.propTypes = {
  children: PropTypes.node.isRequired,
  featureFlags: PropTypes.shape({
    [PropTypes.string]: PropTypes.bool,
  }),
}

export function useFeatureFlagContext() {
  return useContext(FeatureFlagContext)
}
