import { usePageContext } from 'components/PageProvider'
import { useFeatureFlagEnabled } from './useFeatureFlagEnabled'

export function useSiteAuth() {
  const isUserLoginEnabled = useFeatureFlagEnabled('user-login')

  const { site } = usePageContext() ?? {}

  const defaultAuth = {
    enabled: false,
    registrationEnabled: true,
    requireRegistration: false,
    pages: {},
  }

  return isUserLoginEnabled ? site?.auth ?? defaultAuth : defaultAuth
}
