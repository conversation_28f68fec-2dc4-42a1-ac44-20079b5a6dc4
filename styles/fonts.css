/* source-sans-pro-200 - latin_latin-ext */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 200;
  src: url('./fonts/source-sans-pro-v21-latin_latin-ext-200.eot'); /* IE9 Compat Modes */
  src: url('./fonts/source-sans-pro-v21-latin_latin-ext-200.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v21-latin_latin-ext-200.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v21-latin_latin-ext-200.woff') format('woff'),
    /* Modern Browsers */
      url('./fonts/source-sans-pro-v21-latin_latin-ext-200.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v21-latin_latin-ext-200.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-200italic - latin_latin-ext */
@font-face {
  font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 200;
  src: url('./fonts/source-sans-pro-v21-latin_latin-ext-200italic.eot'); /* IE9 Compat Modes */
  src: url('./fonts/source-sans-pro-v21-latin_latin-ext-200italic.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */
      url('./fonts/source-sans-pro-v21-latin_latin-ext-200italic.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v21-latin_latin-ext-200italic.woff')
      format('woff'),
    /* Modern Browsers */
      url('./fonts/source-sans-pro-v21-latin_latin-ext-200italic.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v21-latin_latin-ext-200italic.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-regular - latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url('./fonts/source-sans-pro-v19-latin-ext-regular.eot'); /* IE9 Compat Modes */
  src: local(''),
    url('./fonts/source-sans-pro-v19-latin-ext-regular.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v19-latin-ext-regular.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-regular.woff') format('woff'),
    /* Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-regular.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v19-latin-ext-regular.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-italic - latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: url('./fonts/source-sans-pro-v19-latin-ext-italic.eot'); /* IE9 Compat Modes */
  src: local(''),
    url('./fonts/source-sans-pro-v19-latin-ext-italic.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v19-latin-ext-italic.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-italic.woff') format('woff'),
    /* Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-italic.ttf') format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v19-latin-ext-italic.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-600 - latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 600;
  src: url('./fonts/source-sans-pro-v19-latin-ext-600.eot'); /* IE9 Compat Modes */
  src: local(''),
    url('./fonts/source-sans-pro-v19-latin-ext-600.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v19-latin-ext-600.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-600.woff') format('woff'),
    /* Modern Browsers */ url('./fonts/source-sans-pro-v19-latin-ext-600.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v19-latin-ext-600.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-600italic - latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 600;
  src: url('./fonts/source-sans-pro-v19-latin-ext-600italic.eot'); /* IE9 Compat Modes */
  src: local(''),
    url('./fonts/source-sans-pro-v19-latin-ext-600italic.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v19-latin-ext-600italic.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-600italic.woff') format('woff'),
    /* Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-600italic.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v19-latin-ext-600italic.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-700 - latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 700;
  src: url('./fonts/source-sans-pro-v19-latin-ext-700.eot'); /* IE9 Compat Modes */
  src: local(''),
    url('./fonts/source-sans-pro-v19-latin-ext-700.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v19-latin-ext-700.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-700.woff') format('woff'),
    /* Modern Browsers */ url('./fonts/source-sans-pro-v19-latin-ext-700.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v19-latin-ext-700.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-700italic - latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 700;
  src: url('./fonts/source-sans-pro-v19-latin-ext-700italic.eot'); /* IE9 Compat Modes */
  src: local(''),
    url('./fonts/source-sans-pro-v19-latin-ext-700italic.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v19-latin-ext-700italic.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-700italic.woff') format('woff'),
    /* Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-700italic.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v19-latin-ext-700italic.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-900 - latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 900;
  src: url('./fonts/source-sans-pro-v19-latin-ext-900.eot'); /* IE9 Compat Modes */
  src: local(''),
    url('./fonts/source-sans-pro-v19-latin-ext-900.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v19-latin-ext-900.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-900.woff') format('woff'),
    /* Modern Browsers */ url('./fonts/source-sans-pro-v19-latin-ext-900.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v19-latin-ext-900.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
/* source-sans-pro-900italic - latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 900;
  src: url('./fonts/source-sans-pro-v19-latin-ext-900italic.eot'); /* IE9 Compat Modes */
  src: local(''),
    url('./fonts/source-sans-pro-v19-latin-ext-900italic.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('./fonts/source-sans-pro-v19-latin-ext-900italic.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-900italic.woff') format('woff'),
    /* Modern Browsers */
      url('./fonts/source-sans-pro-v19-latin-ext-900italic.ttf')
      format('truetype'),
    /* Safari, Android, iOS */
      url('./fonts/source-sans-pro-v19-latin-ext-900italic.svg#SourceSansPro')
      format('svg'); /* Legacy iOS */
}
