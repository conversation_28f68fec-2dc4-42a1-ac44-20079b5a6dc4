import { deleteFetch, getFetch, patchFetch, postFetch } from 'utils/http'

import { apiSessionToAdapterSession, apiUserToAdapterUser } from './utils'

const basePath = '/web-auth/auth'

/**
 * API Adapter for NextAuth
 * @param {NextApiRequest} req
 * @returns {Adapter}
 */
export function APIAdapter(req) {
  const clientConfig = {
    headers: {
      'Origin': req.headers?.host,
      'Cache-Control': 'no-cache',
    },
  }

  return {
    async createUser(data) {
      // eslint-disable-next-line no-useless-catch
      try {
        const user = await postFetch(`${basePath}/user`, data, clientConfig)
        return apiUserToAdapterUser(user)
      } catch (error) {
        throw error
      }
    },
    async getUser(id) {
      try {
        const user = await getFetch(
          `${basePath}/user/${id}`,
          undefined,
          clientConfig
        )
        if (!user) {
          return null
        }
        return apiUserToAdapterUser(user)
      } catch (error) {
        return null
      }
    },
    async getUserByEmail(email) {
      try {
        const user = await getFetch(
          `${basePath}/user/email`,
          { email },
          clientConfig
        )
        if (!user) {
          return null
        }
        return apiUserToAdapterUser(user)
      } catch (error) {
        return null
      }
    },
    async getUserByAccount(providerAndProviderAccountId) {
      try {
        const user = await getFetch(
          `${basePath}/user/account`,
          providerAndProviderAccountId,
          clientConfig
        )
        if (!user) {
          return null
        }
        return apiUserToAdapterUser(user)
      } catch (error) {
        return null
      }
    },
    async updateUser({ id, ...data }) {
      const updatedUser = await patchFetch(
        `${basePath}/user/${id}`,
        data,
        clientConfig
      )
      return apiUserToAdapterUser(updatedUser)
    },
    async deleteUser(id) {
      await deleteFetch(`${basePath}/user/${id}`, undefined, clientConfig)
    },
    async linkAccount(data) {
      await postFetch(`${basePath}/accounts`, data, clientConfig)
    },
    async unlinkAccount(providerOrProviderAccountId) {
      await deleteFetch(
        `${basePath}/accounts/${providerOrProviderAccountId}/unlink`,
        undefined,
        clientConfig
      )
    },
    async getSessionAndUser(sessionToken) {
      try {
        const sessionUser = await getFetch(
          `${basePath}/session/${sessionToken}`,
          undefined,
          clientConfig
        )

        if (!sessionUser) {
          return null
        }

        return {
          user: apiUserToAdapterUser(sessionUser.user),
          session: apiSessionToAdapterSession(sessionUser.session),
        }
      } catch (error) {
        return null
      }
    },
    async createSession(data) {
      const session = await postFetch(`${basePath}/session`, data, clientConfig)
      return apiSessionToAdapterSession(session)
    },
    async updateSession(data) {
      const session = await patchFetch(
        `${basePath}/session/${data.sessionToken}`,
        data,
        clientConfig
      )
      return apiSessionToAdapterSession(session)
    },
    async deleteSession(sessionToken) {
      await deleteFetch(
        `${basePath}/session/${sessionToken}`,
        undefined,
        clientConfig
      )
    },
    async createVerificationToken(data) {
      return await postFetch(
        `${basePath}/verification-token`,
        data,
        clientConfig
      )
    },
    async useVerificationToken(identifierToken) {
      try {
        const verificationToken = await deleteFetch(
          `${basePath}/verification-token`,
          identifierToken,
          clientConfig
        )
        if (!verificationToken) {
          return null
        }
        return verificationToken
      } catch (error) {
        return null
      }
    },
  }
}
