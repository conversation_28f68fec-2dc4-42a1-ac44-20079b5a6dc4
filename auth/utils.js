import { getImageUrl } from 'utils/images'

export function apiUserToAdapterUser(apiUser) {
  const {
    id,
    name,
    email,
    emailVerified,
    image,
    isProfileComplete,
    termsAccepted,
  } = apiUser

  return {
    id,
    name,
    email,
    emailVerified: new Date(emailVerified),
    image: getImageUrl(image),
    termsAccepted,
    isProfileComplete,
  }
}

export function apiAccountToAdapterAccount(apiAccount) {
  const { user, provider, providerAccountId, type } = apiAccount

  return {
    userId: user,
    provider,
    providerAccountId,
    type,
  }
}

export function apiSessionToAdapterSession(apiSession) {
  const { user, expires, sessionToken } = apiSession

  return {
    userId: user,
    expires: new Date(expires),
    sessionToken,
  }
}
