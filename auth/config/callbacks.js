export function callbacks(siteAuth) {
  return {
    /**
     * This is called when a user is signing in
     * @param {object} options
     * @param {object} options.user - The user object
     * @param {string} options.user.id - The user ID. If the user is new, this is the same as the email for account.type === 'email'
     * @param {string} options.user.name - The user name
     * @param {string} options.user.email - The user email
     * @param {object} options.account - The user account
     * @param {string} options.account.provider - The provider name
     * @param {string} options.account.type - The provider type
     * @param {string} options.account.providerAccountId - The provider account ID. This is the ID of the user on the provider for new users, or the ID of the user in the database for existing users
     * @param {object} options.credentials - The user credentials
     * @param {object} options.email - The user email. This is only available if using the email provider
     * @param {object} options.profile - The user profile. For OAuth providers, this is the profile data returned by the provider. The profile is only available when the user is new or on the first sign in
     * @returns {boolean | string} - Return `true` to allow sign in, `false` to deny access or `'/'` to redirect to a URL
     * @see https://next-auth.js.org/configuration/callbacks#sign-in
     */
    signIn: async ({ user, account }) => {
      const isNewUser =
        (account?.type === 'oauth' && account?.providerAccountId === user.id) ||
        (account?.type === 'email' && user.id === user.email)

      // If the user doesn't exists when registration is disabled, don't allow sign in
      if (!siteAuth?.registrationEnabled && isNewUser) {
        return false
      }

      // If the user is signing in with an OAuth account, allow sign in. Possible registration details will need to be requested from the user after sign in
      if (account?.type === 'oauth') {
        return true
      }

      // If the user doesn't exist and registration is required, redirect them to the registration page
      if (siteAuth?.requireRegistration && isNewUser) {
        const queryParams = new URLSearchParams(
          user.email
            ? {
                email: user.email,
              }
            : undefined
        )
        return `${
          siteAuth?.pages?.registerPageUrl || '/register'
        }?${queryParams.toString()}`
      }

      // Otherwise, allow sign in
      return true
    },
    /**
     * This is called every time a session is checked
     * @param {object} options
     * @param {object} options.session - The session object
     * @param {object} options.user - The user object
     * @returns {object} - Return the session object
     * @see https://next-auth.js.org/configuration/callbacks#session
     */
    session: async ({ session, user }) => {
      // Add user properties to session user object
      if (session?.user) {
        session.user.termsAccepted = user.termsAccepted
        session.user.isProfileComplete = user.isProfileComplete
        session.user.id = user.id
      }

      return session
    },
  }
}
