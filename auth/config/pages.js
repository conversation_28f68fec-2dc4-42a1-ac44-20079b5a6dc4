export function pages(siteAuth) {
  let pages = {}

  // Set custom pages if they exist in the site auth settings
  if (siteAuth) {
    if (siteAuth.pages?.loginPageUrl) {
      pages.signIn = siteAuth.pages.loginPageUrl
    }
    if (siteAuth.pages?.loginErrorPageUrl) {
      pages.error = siteAuth.pages.loginErrorPageUrl
    }
    if (siteAuth.pages?.verifyLoginPageUrl) {
      pages.verifyRequest = siteAuth.pages.verifyLoginPageUrl
    }
    if (siteAuth.pages?.newUserPageUrl) {
      pages.newUser = siteAuth.pages.newUserPageUrl
    }
  }

  return pages
}
