import React from 'react'

// This needs to be a relative import pointing directly to `node_modules`,
// otherwise the Storybook webpack alias will just cause a circular reference
// and not be able to resolve.
import NextImage from '../../node_modules/next/image'

/**
 * This mocks the normal NextJS Image component for use in Storybook. The regular image component
 * is difficult to mock otherwise, since we want to allow for unoptimized images from any source
 * in order to make writing Stories easy.
 *
 * @param {object} props all regular NextJS Image props
 * @returns A NextJS Image component as unoptimized and with a custom loader that simply returns the src
 */
const NextImageMock = props => (
  <NextImage {...props} unoptimized loader={({ src }) => src} />
)

export default NextImageMock
