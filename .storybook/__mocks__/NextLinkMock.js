import React from 'react'

const NextLinkMock = ({ children, onClick, passHref, ...rest }) => {
  const childrenWithProps = React.Children.map(children, child => {
    // Checking isValidElement is the safe way and avoids a typescript
    // error too.
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        ...rest,
        onClick: event => {
          event.preventDefault()
          onClick?.(event)
        },
      })
    }
    return child
  })

  return <>{childrenWithProps}</>
}

export default NextLinkMock
