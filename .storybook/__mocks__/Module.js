import React, { useEffect } from 'react'
import { useState } from 'react'

import SvgInline from '../../ui/data-display/SvgInline'

export default function Module({ fn, ...rest }) {
  const [module, setModule] = useState()

  useEffect(() => {
    async function dynamicImport() {
      const result = await fn()
      setModule(() => result.default ?? result)
    }
    dynamicImport()
  }, [])

  if (typeof module === 'string' && module.endsWith('.svg')) {
    return <SvgInline path={module} {...rest} />
  }

  if (typeof module === 'function') {
    return module(rest)
  }

  return module
}
