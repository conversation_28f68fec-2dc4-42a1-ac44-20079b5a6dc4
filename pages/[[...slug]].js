import PropTypes from 'prop-types'

import axios from 'axios'
import { getServerSession } from 'next-auth'
import { serverSideTranslations } from 'next-i18next/serverSideTranslations'
import dynamic from 'next/dynamic'

import AcceptTerms from 'components/AcceptTerms'
import CookieConsentProvider from 'components/CookieConsentProvider'
import GeoRedirectProvider from 'components/GeoRedirectProvider'
import PageFavicon from 'components/Favicon'
import PageMeta, { getPageMeta } from 'components/PageMeta'
import PageNotFound from 'components/PageNotFound'
import PageNotPermitted from 'components/PageNotPermitted'
import PagePassword from 'components/PagePassword'
import PageProvider from 'components/PageProvider'
import Content from 'components/content/Content'
import NavigationProvider from 'ui/navigation/NavigationContext'
import { isFeatureEnabled } from 'utils/featureFlags'
import { omit } from 'utils/objects'

import { authOptions } from './api/auth/[...nextauth]'
import { validateJsonString } from 'utils/json'
const CookieConsent = dynamic(() => import('ui/feedback/CookieConsent'))
const GeoRedirect = dynamic(() => import('ui/feedback/GeoRedirect'))
const ErrorTemplate = dynamic(() => import('ui/templates/Error'))
const MainTemplate = dynamic(() => import('ui/templates/Main'))

export default function Page({ page, noRender, session }) {
  if (noRender) return null // do no render HTML when server responds with XML, JSON or plain text
  if (!page || !page.site) return <ErrorTemplate />

  const {
    chromeless,
    dynamicResource,
    error,
    isRTL,
    language,
    notAuthorised,
    notFound,
    passwordProtected,
    passwordValid,
    resourceNotFound,
    site,
  } = page

  const acceptTermsShown = session?.user && !session?.user?.termsAccepted
  const isTermsPage = page.path === site.auth?.terms?.url

  const content =
    notFound || (dynamicResource && resourceNotFound) ? (
      <PageNotFound page={page} />
    ) : passwordProtected && !passwordValid ? (
      <PagePassword page={page} />
    ) : notAuthorised && error === 'NOT_PERMITTED' ? (
      <PageNotPermitted />
    ) : (
      <Content page={page} />
    )

  return (
    <NavigationProvider>
      <CookieConsentProvider settings={site?.cookieSettings}>
        <GeoRedirectProvider geoRedirect={site?.geoRedirect}>
          <div dir={isRTL ? 'rtl' : 'ltr'} lang={language}>
            <PageProvider page={page}>
              {/* <div>TODO: Add Chatwoot (https://www.chatwoot.com/hc/user-guide/articles/**********-how-to-install-live_chat-on-a-next-js-app)</div> */}
              <PageFavicon favicon={site?.favicon} />
              <PageMeta page={page} />
              {chromeless || (acceptTermsShown && isTermsPage) ? (
                content
              ) : (
                <MainTemplate page={page}>{content}</MainTemplate>
              )}{' '}
              {acceptTermsShown && !isTermsPage && (
                <AcceptTerms user={session.user} site={site} />
              )}
            </PageProvider>
            <GeoRedirect />
            <CookieConsent
              scripts={page.scripts}
              analytics={page.analytics}
              settings={site?.cookieSettings}
            />
          </div>
        </GeoRedirectProvider>
      </CookieConsentProvider>
    </NavigationProvider>
  )
}
Page.propTypes = {
  page: PropTypes.object,
  noRender: PropTypes.bool,
  session: PropTypes.object,
}

const defaultCacheControl = 'public, s-maxage=300, stale-while-revalidate=86400' // 5 minutes

export async function getServerSideProps({ req, res, params, query, locale }) {
  const {
    NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_API_CLIENT_TOKEN,
    NEXT_PUBLIC_API_CACHE_CONTROL,
  } = process.env

  // Start with a "notFound" page object
  let page = { notFound: true }

  // Fetch page from API, based in page.slug
  const headers = {
    Cookie: req.headers.cookie ?? '',
    ClientToken: NEXT_PUBLIC_API_CLIENT_TOKEN,
    Origin: req.headers.host,
    session: req.cookies[process.env.NEXTAUTH_SESSION_TOKEN_NAME] ?? null,
  }

  const bypassApiCache = Boolean(query['apicache-bypass'])
  const clearApiCache = Boolean(query['apicache-clear'])

  if (bypassApiCache) {
    // Bypass API cache
    headers['x-apicache-bypass'] = true
  }

  // Send client IP
  headers['X-Forwarded-For'] =
    req.headers['x-forwarded-for'] ||
    req.headers['X-Forwarded-For'] ||
    req.connection.remoteAddress

  try {
    const response = await axios.get(
      `${NEXT_PUBLIC_API_URL}/web/pages/${encodeURI(params.slug)}`,
      {
        headers,
        params: omit(query, 'slug'),
      }
    )
    if (response.data) {
      page = response.data
    }
  } catch (error) {
    console.log(error) // eslint-disable-line no-console
  }

  if (bypassApiCache || clearApiCache) {
    // Clear CDNs cache
    res.setHeader('Cache-Control', 'public, s-maxage=0, Pragma: no-cache')
    res.setHeader('CDN-Cache-Control', 'public, s-maxage=0')
    res.setHeader('Vercel-CDN-Cache-Control', 'public, s-maxage=0')
  } else {
    // Sets Cache-Control header when page has some settings for it, or uses the default
    res.setHeader(
      'Cache-Control',
      page.cacheControl || NEXT_PUBLIC_API_CACHE_CONTROL || defaultCacheControl
    )
  }

  if (page.notAuthorised) {
    const loginUrl = page.site?.auth?.pages?.loginPageUrl ?? '/'

    if (page.error === 'LOGIN_REQUIRED') {
      return {
        redirect: {
          destination: `${loginUrl}?callbackUrl=${encodeURIComponent(
            req.url
          )}&error=SessionRequired`,
          permanent: false,
        },
      }
    }
  }

  // Ensures response has a 404 status code when page or its dynamic resource are not found (and if page has a redirect, skip this)
  if (
    !page.redirect &&
    (page.notFound || (page.dynamicResource && page.resourceNotFound))
  ) {
    res.statusCode = 404
  }

  // Stop when site is not found
  if (!page.site) {
    return {
      notFound: true,
    }
  }

  // When there is a redirect for this page
  if (page.redirect) {
    res.setHeader('Cache-Control', 'no-cache')

    return {
      redirect: page.redirect,
    }
  }

  // When page is set to return XML, JSON or plain text
  if (
    page.xml?.enabled ||
    page.json?.enabled ||
    page.text?.enabled ||
    page.html?.enabled
  ) {
    const isXml = page.xml?.enabled
    const isJson = page.json?.enabled
    const isText = page.text?.enabled
    const isHtml = page.html?.enabled
    const { resource } = isXml ? page.xml : isJson ? page.json : {} // If enabled and has a resource, resource should be the output
    const outputString = resource
      ? page.resources[resource] // this page resource is expected to be a string in XML format
      : isHtml
        ? page.html.content
        : isText
          ? page.text.content
          : validateJsonString(page.json?.content) // this page content is expected to be a JSON content (in string format)

    // Output the string content if it is not empty or if it is a text content (empty text content should be rendered)
    if (outputString || (isText && outputString === '')) {
      const contentType = isXml
        ? 'text/xml'
        : isJson
          ? 'application/json'
          : isHtml
            ? 'text/html'
            : 'text/plain'
      res.setHeader('Content-Type', contentType)
      res.write(outputString)
      res.end()

      return {
        props: {
          noRender: true,
        },
      }
    }

    // If there is no XML, JSON, or string, return 404
    page.notFound = true
  }

  const { i18nNamespaces, site, language, chromeless } = page
  const pageLocale = language || site.language || locale || 'en'
  const meta = getPageMeta(page, pageLocale)
  const appSettings = site?.appSettings || {}

  const isUserLoginEnabled = isFeatureEnabled('user-login', page.featureFlags)

  const session =
    isUserLoginEnabled && page.site?.auth?.enabled
      ? await getServerSession(req, res, authOptions(req, page.site))
      : {}

  return {
    props: {
      session,
      page: {
        ...page,
        pageData: {
          ...meta,
          id: page.id || null,
          site,
          host: req.headers.host,
          url: req.url,
          language,
          chromeless: chromeless || false,
          absoluteUrl: `https://${req.headers.host}${page.path}`,
        },
      },
      appSettings,
      ...(await serverSideTranslations(pageLocale, [
        'common',
        'cookies',
        'media-library',
        'courses',
        'payments',
        ...(i18nNamespaces || []),
      ])),
    },
  }
}
