import { getServerSession } from 'next-auth'

import { APIError, client } from 'utils/http'

import { authOptions } from '../auth/[...nextauth]'

const basePath = '/courses'

/*
  Routes:
  --------------------------------------------------
  path: [ ':courseId', 'start' ],
  path: [ 'course-statuses', ':courseStatusId', 'submit-questionnaire' ],
  path: [ 'course-statuses', ':courseStatusId', 'switch-mode' ],
  path: [ 'chats' ],
  path: [ 'chats', ':chatId', 'messages' ],
  path: [ 'student', 'profile' ],
*/
export default async function courses(req, res) {
  const { path, ...query } = req.query
  const requestPath = path ?? []

  // check if user is authenticated
  if (
    requestPath.includes('start') ||
    requestPath.includes('submit-questionnaire') ||
    requestPath.includes('switch-mode') ||
    requestPath.includes('chats') ||
    requestPath.includes('messages') ||
    requestPath.includes('profile') ||
    (requestPath.includes('account') && requestPath.includes('delete')) ||
    requestPath.includes('count-unread-messages')
  ) {
    const session = await getServerSession(req, res, authOptions(req))
    if (!session) {
      return res.status(401).json({
        message: 'Login required.',
        code: 'LOGIN_REQUIRED',
      })
    }
  }

  try {
    const url = `${basePath}${requestPath.length ? `/${requestPath.join('/')}` : ''}`
    const response = await client(
      req.method ?? 'GET',
      url,
      req.method?.toUpperCase() === 'GET' ? query : req.body,
      {
        headers: {
          'Origin': req.headers?.host,
          'session':
            req.cookies[process.env.NEXTAUTH_SESSION_TOKEN_NAME] ?? null,
          'Cache-Control': 'no-cache',
        },
      }
    )
    res.status(200).json(response)
  } catch (error) {
    if (error instanceof APIError) {
      return res.status(error.status).json(error)
    }
    res.status(500).send({})
  }
}
