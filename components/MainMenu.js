import PropTypes from 'prop-types'
import { useMemo } from 'react'

import { useFeatureFlagEnabled } from 'hooks/useFeatureFlagEnabled'
import Navigation, {
  ItemLevel1,
  ItemLevel2,
  ItemLevel3,
} from 'ui/navigation/Navigation'

import { usePageContext } from './PageProvider'
import UserMenu from './UserMenu'

export default function MainMenu({ className = '', items = [] }) {
  const pageContext = usePageContext()

  const isUserLoginEnabled = useFeatureFlagEnabled('user-login')
  const authEnabled = useMemo(
    () => isUserLoginEnabled && pageContext?.site?.auth?.enabled,
    [isUserLoginEnabled, pageContext?.site?.auth?.enabled]
  )

  return (
    <Navigation className={className}>
      {items?.map((level1, i) => (
        <ItemLevel1
          label={level1.label}
          url={level1.url}
          variant={level1.variant}
          key={`${level1.url}-${i}`}
        >
          {level1.items?.map((level2, i) => (
            <ItemLevel2
              label={level2.label}
              url={level2.url}
              variant={level2.variant}
              key={`${level2.url}-${i}`}
            >
              {level2.items?.map((level3, i) => (
                <ItemLevel3
                  label={level3.label}
                  url={level3.url}
                  variant={level3.variant}
                  key={`${level3.url}-${i}`}
                />
              ))}
            </ItemLevel2>
          ))}
        </ItemLevel1>
      ))}

      {authEnabled && <UserMenu />}
    </Navigation>
  )
}
MainMenu.propTypes = {
  className: PropTypes.string,
  items: PropTypes.array,
}
