import dynamic from 'next/dynamic'

const Root = dynamic(() => import('./Root'))
const Section = dynamic(() => import('./Section'))
const Container = dynamic(() => import('./Container'))
const ContentPlaceholder = dynamic(() => import('./ContentPlaceholder'))
const Grid = dynamic(() => import('./Grid'))
const Slider = dynamic(() => import('./Slider'))
const Accordion = dynamic(() => import('./Accordion'))
const AccordionItem = dynamic(() =>
  import('./Accordion').then(m => m.AccordionItem)
)
const Header = dynamic(() => import('./Header'))
const SimpleText = dynamic(() => import('./SimpleText'))
const LabelIcon = dynamic(() => import('./LabelIcon'))
const ContactForm = dynamic(() => import('./ContactForm'))
const HtmlEmbed = dynamic(() => import('./HtmlEmbed'))
const MobileAppNotification = dynamic(() => import('./MobileAppNotification'))
const DemoReviewLink = dynamic(() => import('./DemoReviewLink'))
const HubSpotForm = dynamic(() => import('./HubSpotForm'))
const DisqusComments = dynamic(() => import('./DisqusComments'))
const ShareButton = dynamic(() => import('./ShareButton'))
const Map = dynamic(() => import('./Map'))
const Menu = dynamic(() => import('./Menu'))
const Button = dynamic(() => import('ui/buttons/Button'))
const Box = dynamic(() => import('./Box'))
const Card = dynamic(() => import('ui/data-display/Card'))
const Carousel = dynamic(() => import('ui/data-display/ImageCarousel'))
const QuoteCarousel = dynamic(() => import('ui/data-display/QuoteCarousel'))
const Image = dynamic(() => import('./Image'))
const ImageGallery = dynamic(() => import('./ImageGallery'))
const BibleVerse = dynamic(() => import('./BibleVerse'))
const Player = dynamic(() => import('./Player'))
const Breadcrumbs = dynamic(() => import('./Breadcrumbs'))
const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const RichText = dynamic(() => import('./RichText'))
const NewsletterSubscription = dynamic(() => import('./NewsletterSubscription'))
const NewsletterConfirmation = dynamic(() => import('./NewsletterConfirmation'))
const CookieBlockedContent = dynamic(
  () => import('ui/feedback/CookieBlockedContent')
)

const Form = dynamic(() => import('./Form/Form'))
const FormCheckbox = dynamic(() => import('./Form/FormCheckbox'))
const FormInput = dynamic(() => import('./Form/FormInput'))
const FormSelect = dynamic(() => import('./Form/FormSelect'))
const FormTextArea = dynamic(() => import('./Form/FormTextArea'))
const FormCaptcha = dynamic(() => import('./Form/FormCaptcha'))

const LoginError = dynamic(() => import('./auth/LoginError'))
const LoginForm = dynamic(() => import('./auth/LoginForm'))
const RegistrationForm = dynamic(() => import('./auth/RegistrationForm'))
const UserAccountOverview = dynamic(() => import('./auth/UserAccountOverview'))
const UserProfile = dynamic(() => import('./auth/UserProfile'))
const VerifyLogin = dynamic(() => import('./auth/VerifyLogin'))

const ChurchDetail = dynamic(() => import('./entities/ChurchDetail'))
const ChurchFinder = dynamic(() => import('./entities/ChurchFinder'))
const ChannelsMap = dynamic(() => import('./media-library/ChannelsMap'))
const CollectionList = dynamic(() => import('./media-library/CollectionList'))
const Collection = dynamic(() => import('./media-library/Collection'))
const CollectionTeaser = dynamic(
  () => import('./media-library/CollectionTeaser')
)
const ScheduleDay = dynamic(() => import('./media-library/ScheduleDay'))
const ScheduleLive = dynamic(() => import('./media-library/ScheduleLive'))
const ScheduleWeek = dynamic(() => import('./media-library/ScheduleWeek'))
const ShowList = dynamic(() => import('./media-library/ShowList'))
const ShowDetail = dynamic(() => import('./media-library/ShowDetail'))
const ShowImage = dynamic(() => import('./media-library/ShowImage'))
const ShowHosts = dynamic(() => import('./media-library/ShowHosts'))
const ShowParticipants = dynamic(
  () => import('./media-library/ShowParticipants')
)
const ShowLinks = dynamic(() => import('./media-library/ShowLinks'))
const ShowDocuments = dynamic(() => import('./media-library/ShowDocuments'))
const ShowPodcasts = dynamic(() => import('./media-library/ShowPodcasts'))
const ShowAudioPlaylist = dynamic(
  () => import('./media-library/ShowAudioPlaylist')
)
const ShowVideoCategories = dynamic(
  () => import('./media-library/ShowVideoCategories')
)
const ShowsFeedList = dynamic(() => import('./media-library/ShowsFeedList'))
const ShowMeta = dynamic(() => import('./media-library/ShowMeta'))
const RelatedShows = dynamic(() => import('./media-library/RelatedShows'))
const PersonDetail = dynamic(() => import('./media-library/PersonDetail'))
const EpisodesTeaser = dynamic(() => import('./media-library/EpisodesTeaser'))
const EpisodesList = dynamic(() => import('./media-library/EpisodesList'))
const EpisodeDetail = dynamic(() => import('./media-library/EpisodeDetail'))
const EpisodePlayer = dynamic(() => import('./media-library/EpisodePlayer'))
const EpisodeAudioPlayer = dynamic(
  () => import('./media-library/EpisodeAudioPlayer')
)
const EpisodeGuests = dynamic(() => import('./media-library/EpisodeGuests'))
const EpisodeHosts = dynamic(() => import('./media-library/EpisodeHosts'))
const EpisodeLinks = dynamic(() => import('./media-library/EpisodeLinks'))
const EpisodeDocuments = dynamic(
  () => import('./media-library/EpisodeDocuments')
)
const EpisodeDownloads = dynamic(
  () => import('./media-library/EpisodeDownloads')
)
const EpisodeMeta = dynamic(() => import('./media-library/EpisodeMeta'))
const RelatedEpisodes = dynamic(() => import('./media-library/RelatedEpisodes'))
const LivestreamPlayer = dynamic(
  () => import('./media-library/LivestreamPlayer')
)
const CategoryVideos = dynamic(() => import('./media-library/CategoryVideos'))
const EpisodeVideos = dynamic(() => import('./media-library/EpisodeVideos'))
const VideoDetail = dynamic(() => import('./media-library/VideoDetail'))
const VideoPlayer = dynamic(() => import('./media-library/VideoPlayer'))
const VideoCategories = dynamic(() => import('./media-library/VideoCategories'))
const VideoCategoryDetail = dynamic(
  () => import('./media-library/VideoCategoryDetail')
)
const VideoParent = dynamic(() => import('./media-library/VideoParent'))
const RelatedVideos = dynamic(() => import('./media-library/RelatedVideos'))
const OfflineVideos = dynamic(() => import('./media-library/OfflineVideos'))

const AudioPlayer = dynamic(() => import('./media-library/AudioPlayer'))
const AudioPlaylist = dynamic(() => import('./media-library/AudioPlaylist'))

const PWAInstallPrompt = dynamic(() => import('./pwa/PWAInstallPrompt'))
const PWAOfflineBanner = dynamic(() => import('./pwa/PWAOfflineBanner'))

const ArticleList = dynamic(() => import('./articles/List'))
const ArticleDetail = dynamic(() => import('./articles/Detail'))
const ArticleTeaser = dynamic(() => import('./articles/Teaser'))
const LatestArticles = dynamic(() => import('./articles/LatestArticles'))

const PublicationsList = dynamic(() => import('./publications/List'))
// const PublicationsSearch = dynamic(() => import('./publications/Search'))
const PublicationDetail = dynamic(() => import('./publications/Detail'))
// const PublicationDownloads = dynamic(() => import('./publications/Downloads'))
// const PublicationsReligionsMenu = dynamic(
//   () => import('./publications/ReligionsMenu')
// )
// const PublicationStatistics = dynamic(() => import('./publications/Statistics'))
// const PublishingHouses = dynamic(
//   () => import('./publications/PublishingHouses')
// )
// const PublishingHousesMap = dynamic(() => import('./publications/Map'))

const StandaloneDonation = dynamic(
  () => import('./payments/StandaloneDonation')
)

const CoursesCollection = dynamic(() => import('./courses/Collection'))
const CoursesEnrolledCourses = dynamic(
  () => import('./courses/EnrolledCourses')
)
const CourseDetail = dynamic(() => import('./courses/CourseDetail'))
const CourseHero = dynamic(() => import('./courses/CourseHero'))
const CourseImage = dynamic(() => import('./courses/CourseImage'))
const CourseActions = dynamic(() => import('./courses/CourseActions'))
const CourseStatus = dynamic(() => import('./courses/CourseStatus'))
const CourseMetaData = dynamic(() => import('./courses/CourseMetaData'))
const CourseTableOfContents = dynamic(
  () => import('./courses/CourseTableOfContents')
)
const CourseContent = dynamic(() => import('./courses/CourseContent'))
const CourseNavigation = dynamic(() => import('./courses/CourseNavigation'))
const CoursePagination = dynamic(() => import('./courses/CoursePagination'))
const CoursesMessages = dynamic(() => import('./courses/Messages'))
const CoursesStudentProfile = dynamic(() => import('./courses/StudentProfile'))
const CoursesDeleteStudentAccount = dynamic(
  () => import('./courses/DeleteStudentAccount')
)
const CoursesStudentNotifications = dynamic(
  () => import('./courses/StudentNotifications')
)
const CoursesStudentAdvisors = dynamic(
  () => import('./courses/StudentAdvisors')
)
const CourseRegistration = dynamic(() => import('./courses/CourseRegistration'))

function NoWrap({ children }) {
  return children
}

const blocks = {
  Root,
  Section,
  Container,
  ContentPlaceholder,
  Box,
  Grid,
  Slider,
  Card,
  Header,
  SimpleText,
  LabelIcon,
  Button,
  Breadcrumbs,
  LinkList,
  Map,
  Menu,
  ContactForm,
  HubSpotForm,
  DisqusComments,
  Image,
  ImageGallery,
  BibleVerse,
  Player,
  Accordion,
  AccordionItem,
  AccordionItemContent: NoWrap,
  CookieBlockedContent,
  Form,
  FormCheckbox,
  FormInput,
  FormSelect,
  FormTextArea,
  FormCaptcha,
  Carousel,
  QuoteCarousel,
  RichText,
  NewsletterSubscription,
  NewsletterConfirmation,
  HtmlEmbed,
  MobileAppNotification,
  DemoReviewLink,
  ShareButton,
  ArticleList,
  ArticleDetail,
  ArticleTeaser,
  LatestArticles,
  PublicationsList,
  // PublicationsSearch,
  PublicationDetail,
  // PublicationDownloads,
  // PublishingHouses,
  // PublishingHousesMap,
  // PublicationsReligionsMenu,
  // PublicationStatistics,
  ChurchDetail,
  ChurchFinder,
  ChannelsMap,
  CollectionList,
  Collection,
  CollectionTeaser,
  ScheduleDay,
  ScheduleLive,
  ScheduleWeek,
  ShowList,
  ShowDetail,
  ShowImage,
  ShowHosts,
  ShowParticipants,
  ShowLinks,
  ShowDocuments,
  ShowPodcasts,
  ShowAudioPlaylist,
  ShowVideoCategories,
  ShowsFeedList,
  ShowMeta,
  RelatedShows,
  PersonDetail,
  RelatedEpisodes,
  EpisodesTeaser,
  EpisodesList,
  EpisodeDetail,
  EpisodePlayer,
  EpisodeAudioPlayer,
  EpisodeGuests,
  EpisodeHosts,
  EpisodeLinks,
  EpisodeDocuments,
  EpisodeDownloads,
  EpisodeMeta,
  LivestreamPlayer,
  CategoryVideos,
  EpisodeVideos,
  VideoDetail,
  VideoCategories,
  VideoCategoryDetail,
  VideoPlayer,
  VideoParent,
  RelatedVideos,
  OfflineVideos,
  PWAInstallPrompt,
  PWAOfflineBanner,
  AudioPlayer,
  AudioPlaylist,
  CoursesCollection,
  CoursesEnrolledCourses,
  CourseDetail,
  CourseHero,
  CourseImage,
  CourseActions,
  CourseStatus,
  CourseMetaData,
  CourseTableOfContents,
  CourseContent,
  CourseNavigation,
  CoursePagination,
  CoursesMessages,
  CoursesStudentProfile,
  CoursesDeleteStudentAccount,
  CoursesStudentNotifications,
  CoursesStudentAdvisors,
  CourseRegistration,
  StandaloneDonation,
  LoginError,
  LoginForm,
  RegistrationForm,
  UserAccountOverview,
  UserProfile,
  VerifyLogin,
}

export default blocks
