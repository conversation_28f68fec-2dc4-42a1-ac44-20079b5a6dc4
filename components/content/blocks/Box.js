import React from 'react'

import UIBox from 'ui/data-display/Box'
import { getAspectRatioClass } from 'ui/helpers/getAspectRatioClass'
import useFlex from 'ui/helpers/useFlex'
import usePadding from 'ui/helpers/usePadding'
import useSpacing from 'ui/helpers/useSpacing'
import useWidth from 'ui/helpers/useWidth'

/**
 * Box component to wrap content.
 * @param {Object} props Component props
 * @param {Object} props.align Alignment of the content
 * @param {'auto'|'4/3'|'16/9'} props.aspectRatio Aspect ratio of the box
 * @param {React.ReactNode} props.children The children to render
 * @param {String} props.className Additional classes to add
 * @param {Object} props.direction Direction of the content
 * @param {String} props.id ID for the box
 * @param {Object} props.justify Justification of the content
 * @param {Object} props.padding Padding for the box
 * @param {Object} props.spacing Spacing for the box
 * @param {Object} props.width Width of the box
 * @returns {React.ReactNode} The Box component
 */
export default function Box({
  align = { xs: 'stretch' },
  aspectRatio,
  className = '',
  direction = { xs: 'y' },
  justify = { xs: 'start' },
  padding = { xs: { top: 'md' } },
  spacing = { xs: 'md' },
  width = { xs: 'full' },
  ...rest
}) {
  const aspectRatioClass = getAspectRatioClass(aspectRatio)
  const flexClasses = useFlex(direction, align, justify)
  const spacingClasses = useSpacing(spacing)
  const paddingClasses = usePadding(padding)
  const widthClasses = useWidth(width)

  return (
    <UIBox
      className={`${aspectRatioClass} ${paddingClasses} ${widthClasses} ${className}`}
      innerClass={`${flexClasses} ${spacingClasses}`}
      {...rest}
    />
  )
}
