import { Suspense } from 'react'

import dynamic from 'next/dynamic'
import { getAspectRatioClass } from 'ui/helpers/getAspectRatioClass'
import { getBackgroundColor } from 'ui/helpers/getColor'
import useFlex from 'ui/helpers/useFlex'
import usePadding from 'ui/helpers/usePadding'
import useSpacing from 'ui/helpers/useSpacing'

const BackgroundImage = dynamic(() => import('ui/data-display/BackgroundImage'))
const BackgroundVideo = dynamic(() => import('ui/data-display/BackgroundVideo'))

/**
 * Container component to wrap content. A container fills the full width of the screen and can have a background image or video.
 * @param {Object} props Component props
 * @param {Object} props.align Alignment of the content
 * @param {'auto'|'4/3'|'16/9'} props.aspectRatio Aspect ratio of the background image
 * @param {String} props.bgColor Background color of the container
 * @param {Object} props.bgImage Background image for the container
 * @param {String} props.bgImageAlign Alignment of the background image
 * @param {'image'|'video'} props.bgType Type of background (image or video)
 * @param {String} props.bgVideoAccountId Account ID for the video background
 * @param {String} props.bgVideoId Video ID for the video background
 * @param {'cloudflare'} props.bgVideoProvider Video provider for the video background
 * @param {React.ReactNode} props.children The children to render
 * @param {String} props.className Additional classes to add
 * @param {Boolean} props.dark  Indicates if the container is in dark mode
 * @param {String} props.id ID for the container
 * @param {Object} props.justify Justification of the content
 * @param {Boolean} props.noPadding Indicates if the container should have no padding
 * @param {Object} props.padding Padding for the container
 * @param {Object} props.spacing Spacing for the container
 * @returns {React.ReactNode} The Container component
 */
export default function Container({
  align = { xs: 'stretch' },
  aspectRatio,
  bgColor,
  bgImage,
  bgImageAlign,
  bgType = 'image',
  bgVideoAccountId,
  bgVideoId,
  bgVideoProvider,
  children,
  className = '',
  dark,
  direction,
  id,
  justify = { xs: 'start' },
  // TODO: legacy prop that should be migrated to use padding with value "zero"
  noPadding,
  padding,
  spacing,
}) {
  const aspectRatioClass = getAspectRatioClass(aspectRatio)
  const flexClasses = useFlex(direction ?? { xs: 'y' }, align, justify)
  const bgColorClass = getBackgroundColor(bgColor)
  const paddingClass = usePadding(padding, noPadding ? '' : 'py-8 xl:py-10')
  const spacingClass = useSpacing(spacing ?? { xs: 'lg' })
  const darkClass = dark ? 'dark' : ''

  return (
    <Suspense fallback={<div />}>
      <div
        className={`relative w-full !mx-auto max-w-full xl:max-w-screen-xl ${aspectRatioClass} ${bgColorClass} ${darkClass} ${className}`}
        id={id}
      >
        {bgType === 'video' && (
          <BackgroundVideo
            accountId={bgVideoAccountId}
            dark={dark}
            poster={bgImage}
            provider={bgVideoProvider}
            videoId={bgVideoId}
          />
        )}
        {bgType !== 'video' && bgImage && (
          <BackgroundImage
            image={bgImage}
            position={bgImageAlign}
            dark={dark}
            width={1920}
          />
        )}
        <div
          className={`relative ${flexClasses} px-6 xl:px-12 ${spacingClass} ${paddingClass}`}
        >
          {children}
        </div>
      </div>
    </Suspense>
  )
}
