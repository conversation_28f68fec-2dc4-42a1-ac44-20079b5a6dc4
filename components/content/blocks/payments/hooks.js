import axios from 'axios'
import { useEffect, useState } from 'react'
import { useMutation, useQuery } from 'react-query'

const API_URL = process.env.NEXT_PUBLIC_API_URL
const API_CLIENT_TOKEN = process.env.NEXT_PUBLIC_API_CLIENT_TOKEN

export function useCreatePaymentIntent(
  paymentMethodId,
  project,
  initialAmount
) {
  const { data, isLoading, error, isError, refetch } = useQuery(
    ['standaloneDonation'],
    () => {
      if (!paymentMethodId || typeof window === 'undefined') return {}
      return axios.post(
        `${API_URL}/payments/create-payment-intent`,
        { paymentMethodId, project, initialAmount },
        {
          headers: {
            ClientToken: API_CLIENT_TOKEN,
          },
        }
      )
    },
    { refetchOnWindowFocus: false, enabled: false }
  )

  return { data, isLoading, error, isError, refetch }
}

export function useUpdatePaymentIntentAmount({
  paymentMethodId,
  paymentIntentId,
  initialValue,
}) {
  //dangerous idea
  const [variables, setVariables] = useState(initialValue)

  const {
    mutate: updatePaymentIntentAmount,
    isLoading,
    isError,
    error,
  } = useMutation(
    amount => {
      if (!paymentMethodId || !paymentIntentId || amount < 100) {
        return
      }
      return axios.post(
        `${API_URL}/payments/update-payment-intent-amount`,
        { paymentMethodId, paymentIntentId, amount },
        {
          headers: {
            ClientToken: API_CLIENT_TOKEN,
          },
        }
      )
    },
    {
      onSuccess: variables => {
        if (variables?.data?.amount > 99) {
          setVariables(variables.data.amount)
        }
      },
    }
  )
  return { updatePaymentIntentAmount, isLoading, isError, error, variables }
}

export function useDebounce(value, delay) {
  // State and setters for debounced value
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(
    () => {
      // Update debounced value after delay
      const handler = setTimeout(() => {
        setDebouncedValue(value)
      }, delay)
      // Cancel the timeout if value changes (also on delay change or unmount)
      // This is how we prevent debounced value from updating if value is changed ...
      // .. within the delay period. Timeout gets cleared and restarted.
      return () => {
        clearTimeout(handler)
      }
    },
    [value, delay] // Only re-call effect if value or delay changes
  )
  return debouncedValue
}

// export function usePaymentIntent({ clientSecret }) {
//   const stripe = useStripe()

//   const { data, isLoading } = useQuery(
//     ['paymentIntent', clientSecret],
//     () => {
//       console.log(`stripe: ${stripe} and clientSecret: ${clientSecret}`)
//       if (stripe && clientSecret) {
//         return stripe.retrievePaymentIntent(clientSecret)
//       } else {
//         return
//       }
//     },
//     { refetchOnWindowFocus: false }
//   )

//   return { paymentIntent: data?.paymentIntent, isLoading }
// }
