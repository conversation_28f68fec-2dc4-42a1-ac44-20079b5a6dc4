import React, { useEffect, useState } from 'react'
import {
  PaymentElement,
  LinkAuthenticationElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js'
import Button from 'ui/buttons/Button'
import { useTranslation } from 'next-i18next'

import { useUpdatePaymentIntentAmount, useDebounce } from './hooks'
import CurrencyInput from 'ui/data-entry/CurrencyInput'
import Label from 'ui/data-entry/Label'

const paymentIntentStatuses = {
  succeeded: 'Payment succeeded!',
  processing: 'Your payment is processing.',
  requires_payment_method: '', // why is this status shown when
  error: 'Something went wrong.',
  unexpected: 'An unexpected error occurred.',
}

export default function CheckoutForm({
  paymentMethodId,
  clientSecret,
  redirectUrl,
  resetFunction,
  successMessage,
  initialAmount,
  currency,
}) {
  const { t } = useTranslation('payments')

  const elements = useElements()
  const stripe = useStripe()

  const [email, setEmail] = useState()
  const [message, setMessage] = useState('')
  const [wasSuccesful, setWasSuccesful] = useState(false)
  const [paymentIntent, setPaymentIntent] = useState()
  const [updatedAmount, setUpdatedAmount] = useState(initialAmount)

  const [value, setValue] = useState(initialAmount)
  const paymentIntentId = paymentIntent?.id
  const { updatePaymentIntentAmount, variables } = useUpdatePaymentIntentAmount(
    {
      paymentMethodId,
      paymentIntentId,
      initialAmount,
    }
  )

  useEffect(() => {
    // This only runs once, when stripe is loaded. Setting a query for this is not useful.
    // retrievePaymentIntent returns a promise from the stripe object, that we extract the payment intent from to use it on amount updates.
    // Note: both retrievePaymentIntent and confirmationPaymentIntent occur on this same stripe object, which takes its information from the Elements wrapped around this form.
    if (!stripe) {
      return
    }

    if (!clientSecret) {
      return
    }

    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
      setPaymentIntent(paymentIntent)
    })
  }, [stripe, clientSecret])

  // Effect for redirects to this page
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const redirect_status = urlParams.get('redirect_status')
    if (redirect_status === 'succeeded') setWasSuccesful(true)
  }, [])

  // Debounce search term so that it only gives us latest value ...
  // ... if searchTerm has not been updated within last 500ms.
  // The goal is to only have the API call fire when user stops typing ...
  // ... so that we aren't hitting our API rapidly.
  const debouncedValueUpdate = useDebounce(value, 500)

  // Effect for API call
  useEffect(
    () => {
      if (debouncedValueUpdate) {
        // here we send the update to the API
        updatePaymentIntentAmount(debouncedValueUpdate)
      } else {
        // setIsLoading(false)
      }
    },
    [debouncedValueUpdate, updatePaymentIntentAmount]
    // Only call effect if debounced search term changes
  )
  // Effect that sets the updated amount
  useEffect(() => {
    // When we receive the result of the update of amount...
    // ...we set it with setUpdatedAmount
    if (variables > 99) setUpdatedAmount(variables)
  }, [variables])

  const handleSubmit = async e => {
    e.preventDefault()

    if (!stripe || !elements) {
      return
    }

    // setIsLoading(true)

    const { paymentIntent: confirmationPaymentIntent, error } =
      await stripe.confirmPayment({
        elements,
        confirmParams: {
          receipt_email: email,

          return_url: redirectUrl.includes('https://localhost')
            ? redirectUrl.replace('https://', 'http://')
            : redirectUrl,
        },
        redirect: 'if_required',
      })
    const { status } = confirmationPaymentIntent || {}
    if (status === 'succeeded') {
      setWasSuccesful(true)
      setMessage(paymentIntentStatuses[confirmationPaymentIntent?.status])

      return
    } else {
      // This point will only be reached if there is an immediate error when
      // confirming the payment. Otherwise, your customer will be redirected to
      // your `return_url`. For some payment methods like iDEAL, your customer will
      // be redirected to an intermediate site first to authorize the payment, then
      // redirected to the `return_url`.
      if (error?.type === 'card_error' || error?.type === 'validation_error') {
        setMessage(error.message)
      } else {
        setMessage(t('unexpected'))
      }
    }
  }

  const paymentElementOptions = {
    layout: 'tabs',
  }

  const onValueChange = value => {
    setValue(value)
  }

  const onResetForm = () => {
    //we call the reset function passed from parent
    resetFunction()
  }

  return (
    <form id="payment-form" className="space-y-4">
      {wasSuccesful ? (
        <div className="flex flex-col space-y-8">
          <span>{t(successMessage || 'successMessage')}</span>
          <Button onClick={onResetForm} label={t('resetForm')} />
        </div>
      ) : (
        <div className="space-y-4">
          <CurrencyInput
            value={value}
            currency={currency}
            onChange={onValueChange}
            label={t('userAmount')}
            labelClass="text-[#30313C]" // match the color of stripe
            className="text-gray-800"
          />
          <LinkAuthenticationElement
            id="link-authentication-element"
            onChange={e => setEmail(e.target?.value)}
          />
          <PaymentElement
            className="pb-4"
            id="payment-element"
            options={paymentElementOptions}
          />
          <Button
            className="my-4"
            disabled={updatedAmount !== value || !stripe || !elements}
            id="submit"
            onClick={handleSubmit}
            label={t('paynow')}
          ></Button>

          {/* Show errors */}
          {message && (
            <Label
              className=" text-danger-500"
              text={t(message)}
              id="payment-message"
            />
          )}
        </div>
      )}
    </form>
  )
}

// Hook
