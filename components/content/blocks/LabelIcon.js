import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'
import useFontWeight from 'ui/helpers/useFontWeight'
import useTextSize from 'ui/helpers/useTextSize'
import useWidth from 'ui/helpers/useWidth'

const Icon = dynamic(() => import('ui/icons/Icon'))

export default function LabelIcon({
  id,
  className,
  label,
  icon,
  iconPosition,
  fontWeight,
  textSize,
  width,
}) {
  const fontWeightClass = useFontWeight(fontWeight)
  const positionClass = useIconPositionClass(iconPosition)
  const textSizeClass = useTextSize(textSize)
  const widthClass = useWidth(width, 'auto')

  return (
    <div
      className={`flex items-center justify-center ${textSizeClass} ${positionClass} ${widthClass} ${className}`}
      id={id}
    >
      <div>
        <Icon name={icon} />
      </div>
      <div className={` ${fontWeightClass}`}>{label}</div>
    </div>
  )
}
LabelIcon.propTypes = {
  className: PropTypes.string,
  label: PropTypes.string,
  icon: PropTypes.string,
  iconPosition: PropTypes.object,
  id: PropTypes.string,
  fontWeight: PropTypes.string,
  textSize: PropTypes.object,
  width: PropTypes.object,
}

function useIconPositionClass(position, defaultPosition = 'left') {
  if (!position) return position.xs[defaultPosition] || position.xs.full

  const classes = []

  for (const [breakpoint, value] of Object.entries(position)) {
    if (breakpointKeys.includes(breakpoint)) {
      const positionAtBreakpoint = positions[breakpoint]
      const vertical = ['top', 'bottom'].includes(value)
      const reverse = ['bottom', 'right'].includes(value)

      if (positionAtBreakpoint) {
        const classAtBreakpoint =
          positionAtBreakpoint?.[vertical ? 'vertical' : 'horizontal']
        const reverseClassAtBreakpoint = reverse
          ? positionAtBreakpoint[value]
          : ''

        classes.push(`${classAtBreakpoint} ${reverseClassAtBreakpoint}`)
      }
    }
  }

  return classes.join(' ')
}

const positions = {
  xs: {
    horizontal: 'flex-row gap-2',
    vertical: 'flex-col gap-2',
    right: 'flex-row-reverse',
    bottom: 'flex-col-reverse',
  },
  sm: {
    horizontal: 'sm:flex-row sm:gap-2',
    vertical: 'sm:flex-col sm:gap-2',
    right: 'sm:flex-row-reverse',
    bottom: 'sm:flex-col-reverse',
  },
  md: {
    horizontal: 'md:flex-row md:gap-2',
    vertical: 'md:flex-col md:gap-2',
    right: 'md:flex-row-reverse',
    bottom: 'md:flex-col-reverse',
  },
  lg: {
    horizontal: 'lg:flex-row lg:gap-2',
    vertical: 'lg:flex-col lg:gap-2',
    right: 'lg:flex-row-reverse',
    bottom: 'lg:flex-col-reverse',
  },
  xl: {
    horizontal: 'xl:flex-row xl:gap-2',
    vertical: 'xl:flex-col xl:gap-2',
    right: 'xl:flex-row-reverse',
    bottom: 'xl:flex-col-reverse',
  },
}

const breakpointKeys = Object.keys(positions)
