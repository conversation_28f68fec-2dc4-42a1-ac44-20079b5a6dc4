import React from 'react'
import PropTypes from 'prop-types'

import useWidth from 'ui/helpers/useWidth'
import RichText from 'ui/typography/RichText'

export default function RichTextBlock({ className = '', width, ...rest }) {
  const widthClasses = useWidth(width)

  return <RichText className={`${widthClasses} ${className} `} {...rest} />
}
RichTextBlock.propTypes = {
  className: PropTypes.string,
  width: PropTypes.object,
}
