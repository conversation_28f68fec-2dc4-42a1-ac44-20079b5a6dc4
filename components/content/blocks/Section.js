import { Suspense } from 'react'

import dynamic from 'next/dynamic'
import { getAspectRatioClass } from 'ui/helpers/getAspectRatioClass'
import { getBackgroundColor } from 'ui/helpers/getColor'
import useFlex from 'ui/helpers/useFlex'
import useSpacing from 'ui/helpers/useSpacing'
import usePadding from 'ui/helpers/usePadding'

const BackgroundImage = dynamic(() => import('ui/data-display/BackgroundImage'))
const BackgroundVideo = dynamic(() => import('ui/data-display/BackgroundVideo'))

/**
 * Section component to wrap content. A section fills the full width of the screen and can have a background image or video.
 * @param {Object} props Component props
 * @param {Object} props.align Alignment of the content
 * @param {'auto'|'4/3'|'16/9'} props.aspectRatio Aspect ratio of the background image
 * @param {String} props.bgColor Background color of the section
 * @param {Object} props.bgImage Background image for the section
 * @param {String} props.bgImageAlign Alignment of the background image
 * @param {'image'|'video'} props.bgType Type of background (image or video)
 * @param {String} props.bgVideoAccountId Account ID for the video background
 * @param {String} props.bgVideoId Video ID for the video background
 * @param {'cloudflare'} props.bgVideoProvider Video provider for the video background
 * @param {React.ReactNode} props.children The children to render
 * @param {String} props.className Additional classes to add
 * @param {Boolean} props.dark  Indicates if the section is in dark mode
 * @param {String} props.id ID for the section
 * @param {Object} props.justify Justification of the content
 * @param {Object} props.padding Padding for the section
 * @param {Object} props.spacing Spacing for the section

 * @returns {React.ReactNode} The Section component
 */
export default function Section({
  align,
  aspectRatio,
  bgColor,
  bgImage,
  bgImageAlign,
  bgType = 'image',
  bgVideoAccountId,
  bgVideoId,
  bgVideoProvider,
  children,
  className = '',
  dark,
  direction,
  id,
  justify,
  padding,
  spacing,
}) {
  const aspectRatioClass = getAspectRatioClass(aspectRatio)
  const flexClasses = useFlex(direction ?? { xs: 'y' }, align, justify)
  const bgColorClass = getBackgroundColor(
    bgColor ? bgColor : dark ? 'primary-dark-800' : '' // TODO: Add custom dark color support
  )
  const spacingClass = useSpacing(spacing)
  const paddingClass = usePadding(padding)

  const darkClass = dark ? 'dark' : ''

  return (
    <Suspense fallback={<div />}>
      <div
        className={`relative max-w-full ${aspectRatioClass} ${darkClass} ${bgColorClass} dark:bg-primary-dark-800 ${className}`}
        id={id}
      >
        {bgType === 'video' && (
          <BackgroundVideo
            accountId={bgVideoAccountId}
            dark={dark}
            poster={bgImage}
            provider={bgVideoProvider}
            videoId={bgVideoId}
          />
        )}
        {bgType !== 'video' && bgImage && (
          <BackgroundImage
            image={bgImage}
            position={bgImageAlign}
            dark={dark}
            width={1920}
          />
        )}
        <div
          data-node-type="Section-inner"
          className={`relative ${flexClasses} ${paddingClass} ${spacingClass}`}
        >
          {children}
        </div>
      </div>
    </Suspense>
  )
}
