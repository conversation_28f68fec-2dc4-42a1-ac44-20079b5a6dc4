import React, { useCallback, useEffect, useState } from 'react'

import dynamic from 'next/dynamic'
const Dropdown = dynamic(() => import('ui/buttons/Dropdown'))
const DropdownItem = dynamic(() =>
  import('ui/buttons/Dropdown').then(m => m.DropdownItem)
)
const DropdownDivider = dynamic(() =>
  import('ui/buttons/Dropdown').then(m => m.DropdownDivider)
)
import { isFunction } from 'utils/types'
import { useRouter } from 'next/router'
import { isEmpty } from 'lodash'

export default function PublicationsFilters({
  className = '',
  onFiltersChange,
  religions,
  categories,
  languages,
  filtersLabel,
  allLabel = 'All',
  religionFilterLabel = 'Religion',
  languageFilterLabel = 'Language',
  feltNeedFilterLabel = 'Felt-Need',
  doctrineFilterLabel = 'Doctrine',
  downloadLanguages = [],
  pageData,
}) {
  const router = useRouter()
  const { query } = router
  const currentLocale = pageData.language || 'en' // if nothing is selected then currentLocale is 'en'

  const [religion, setReligion] = useState()
  const [language, setLanguage] = useState()
  const [selectedCategories, setSelectedCategories] = useState()
  // The available languages are those that are added to the site, and have a download available.
  const availableDownloadLanguages = React.useMemo(() => {
    return languages.filter(lang =>
      downloadLanguages.some(
        downloadLang => downloadLang.locale === lang.locale
      )
    )
  }, [languages, downloadLanguages])
  // How do i set the language to the currentLocale if it is in the downloadLanguages array?
  useEffect(() => {
    if (isEmpty(availableDownloadLanguages)) {
      return
    }
    const currentLanguage = availableDownloadLanguages.find(
      item => item.locale === currentLocale
    )
    const fallbackLanguage = availableDownloadLanguages.find(
      item => item.locale === 'en'
    )

    if (currentLanguage) {
      setLanguage(currentLanguage.locale)
    } else {
      setLanguage(fallbackLanguage.locale)
    }
  }, [availableDownloadLanguages, currentLocale])

  // Sets selected filters when query changes
  useEffect(() => {
    const religion = query.filterReligion
    const language = query.filterLanguage
    const categories = query.filterCategories

    if (religion) {
      setReligion(religion)
    }

    if (language) {
      setLanguage(language)
    }

    if (categories) {
      setSelectedCategories(JSON.parse(categories))
    }
  }, [query])

  const selectedReligion = religions.find(item => item.name === religion)
  const selectedLanguage = availableDownloadLanguages
    ? availableDownloadLanguages.find(item => item.locale === language)
    : undefined

  const englishFallback = availableDownloadLanguages
    ? availableDownloadLanguages.find(item => item.locale === 'en')
    : undefined

  const handleReligionChange = useCallback(
    selectedReligion => {
      if (selectedReligion === religion) {
        selectedReligion = undefined
      }

      setReligion(selectedReligion)
      if (isFunction(onFiltersChange)) {
        onFiltersChange({
          religion: selectedReligion,
          language,
          categories: selectedCategories,
        })
      }
    },
    [religion, language, selectedCategories, onFiltersChange]
  )

  const handleLanguageChange = useCallback(
    language => {
      setLanguage(language)

      if (isFunction(onFiltersChange)) {
        onFiltersChange({ religion, language, categories: selectedCategories })
      }
    },
    [religion, selectedCategories, onFiltersChange]
  )

  // Sets selectd categories when category change
  const handleCategoriesChange = useCallback(
    (type, categoryName) => {
      const selectedTypeCategories = selectedCategories?.[type] || []

      const updatedTypeCategories =
        categoryName === 'all'
          ? undefined
          : selectedTypeCategories.includes(categoryName)
            ? selectedTypeCategories.filter(item => item !== categoryName)
            : [...selectedTypeCategories, categoryName]

      const newCategories = {
        ...selectedCategories,
        [type]: updatedTypeCategories,
      }

      setSelectedCategories(newCategories)

      if (isFunction(onFiltersChange)) {
        onFiltersChange({ religion, language, categories: newCategories })
      }
    },
    [language, religion, selectedCategories, onFiltersChange]
  )

  if (isEmpty(availableDownloadLanguages)) {
    return null
  }

  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      {
        <div className="flex flex-row flex-wrap items-center justify-center py-2 text-sm font-normal uppercase tracking-widest text-gray-600 md:justify-start xl:hidden">
          {filtersLabel || 'nofilters'}
        </div>
      }

      <div className="flex flex-row flex-wrap items-center justify-center gap-2 md:justify-start">
        <Dropdown
          label={
            <FilterLabel
              selected={selectedReligion?.title}
              allLabel={allLabel}
              label={religionFilterLabel}
            />
          }
        >
          {selectedReligion && (
            <>
              <DropdownItem
                label={allLabel}
                onClick={() => handleReligionChange('all')}
                selected={!religion}
              />
              <DropdownDivider />
            </>
          )}

          {religions.map(item => (
            <DropdownItem
              key={`religion-option-${item.name}`}
              label={item.title}
              onClick={() => handleReligionChange(item.name)}
              selected={item.name === religion}
            />
          ))}
        </Dropdown>

        {Object.entries(categories).map(([type, items]) => (
          <CategoryTypeFilter
            allLabel={allLabel}
            type={type}
            items={items}
            selectedCategories={selectedCategories}
            handleCategoriesChange={handleCategoriesChange}
            key={`category-type-${type}`}
            feltNeedFilterLabel={feltNeedFilterLabel}
            doctrineFilterLabel={doctrineFilterLabel}
          />
        ))}

        <Dropdown
          label={
            <FilterLabel
              selected={
                selectedLanguage?.locale === currentLocale
                  ? selectedLanguage?.nativeName
                  : selectedLanguage?.name?.[`${currentLocale}`] ||
                    selectedLanguage?.name?.['en'] ||
                    selectedLanguage?.name || // If a language doesnt have a name in the current locale, then it will default to english
                    // TODO: Remove this when all languages have translations
                    englishFallback?.name?.[`${currentLocale}`] ||
                    'English'
                // Replace fallback of 'English' with languages.find(obj => obj.current === true)?.nativeName when the filter is done with the current language by default
              }
              allLabel={allLabel}
              label={languageFilterLabel}
            />
          }
        >
          {availableDownloadLanguages.map(item => (
            <DropdownItem
              key={`language-${item.locale}`}
              label={
                item.locale === currentLocale
                  ? item.nativeName
                  : item.name?.[`${currentLocale}`] ||
                    item.name?.['en'] || // If a language doesnt have a name in the current locale, then it will default to english
                    item.name // TODO: Remove this when all languages have translations
              }
              onClick={() => handleLanguageChange(item.locale)}
              selected={item.locale === language}
            />
          ))}
        </Dropdown>
      </div>
    </div>
  )
}

function CategoryTypeFilter({
  allLabel = 'All',
  type,
  items,
  selectedCategories,
  handleCategoriesChange,
  feltNeedFilterLabel,
  doctrineFilterLabel,
}) {
  const selectedItems = items.filter(item =>
    selectedCategories?.[type]?.includes(item.name)
  )

  const hasSelectedItems = selectedItems?.length > 0

  return (
    <Dropdown
      label={
        <FilterLabel
          selected={selectedItems?.map(item => item.title).join(', ')}
          allLabel={allLabel}
          label={
            type === 'doctrine' ? doctrineFilterLabel : feltNeedFilterLabel
          }
        />
      }
      key={`category-type-${type}`}
    >
      {hasSelectedItems && (
        <>
          <DropdownItem
            label={allLabel}
            onClick={() => handleCategoriesChange(type, 'all')}
            selected={!selectedCategories?.[type]}
          />
          <DropdownDivider />
        </>
      )}

      {items?.map(item => (
        <DropdownItem
          key={`category-${type}-${item.name}`}
          label={item.title}
          onClick={() => handleCategoriesChange(type, item.name)}
          selected={selectedCategories?.[type]?.includes(item.name)}
        />
      ))}
    </Dropdown>
  )
}

function FilterLabel({ selected, allLabel, label }) {
  return (
    <span className="inline-flex max-w-[100px] truncate leading-6 md:max-w-xs">
      <span className="capitalize md:font-normal">{label}</span>
      <span className="hidden space-x-1 truncate md:inline">
        <span className="opacity-25">:</span>
        {selected ? (
          <span className="truncate">{selected}</span>
        ) : (
          <span>{allLabel}</span>
        )}
      </span>
    </span>
  )
}
