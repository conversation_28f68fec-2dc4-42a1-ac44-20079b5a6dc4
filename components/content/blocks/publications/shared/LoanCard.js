import React from 'react'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

import Tag, { Tags } from 'ui/data-display/Tag'
import Link from 'ui/navigation/Link'
const Image = dynamic(() => import('ui/data-display/Image'))

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'
import { isEmpty } from 'utils/arrays'
import { isArray } from 'utils/types'
import { getResponsiveSizes } from 'ui/data-display/Image'
import Button from 'ui/buttons/Button'
import ButtonGroup from 'ui/buttons/ButtonGroup'

export default function PublicationLibraryCard({ publication }) {
  const { author, categories, cover, title, url } = publication

  const { t } = useTranslation('publications')

  const isMd = useMatchMedia(media.md)

  const hasAuthor = !!author
  const hasCategories = isArray(categories) && !isEmpty(categories)

  return (
    <div className="relative flex flex-col md:flex-row gap-4 rounded-lg overflow-hidden bg-white shadow dark:bg-primary-dark-700 p-4">
      {/* COVER IMAGE */}
      <div className="overflow-hidden w-24 md:w-32 md:min-h-48">
        <Link to={url}>
          {cover?.file ? (
            <Image
              className="w-full rounded-lg"
              file={cover?.file}
              alt={title}
              imgClass="rounded-lg"
              sizes={getResponsiveSizes('lg:150px md:120px 120px')}
            />
          ) : (
            <div className="bg-gray-100 dark:bg-gray-500 rounded-lg aspect-2/3 shadow-md shadow-gray-200 dark:shadow-transparent flex items-center justify-center p-10">
              <span className="text-gray-400 dark text-center text-sm capitalize">
                {t('NO COVER IMAGE')}
              </span>
            </div>
          )}
        </Link>
      </div>

      {/* CONTENT */}
      <div className="w-full">
        <div className="flex justify-between flex-col h-full gap-8">
          {/* TOP */}
          <div className="space-y-4">
            <div>
              {(isMd || hasAuthor) && (
                <div className="text-gray-600 line-clamp-1 md:line-clamp-none text-sm dark:text-gray-400">
                  {author}
                </div>
              )}
              <Link className="font-semibold dark:text-gray-200" to={url}>
                {title}
              </Link>
            </div>
            {(isMd || hasCategories) && (
              <div>
                {hasCategories && (
                  <Tags>
                    {categories.map((category, key) => (
                      <Tag key={key} label={category.title} size="xs" />
                    ))}
                  </Tags>
                )}
              </div>
            )}
            {/* <div className="text-success-600 dark:text-success-400 text-sm">
              {t('Available')}
            </div> */}
            {/* <div className="text-warning-600 dark:text-warning-500 text-sm">
              {t('Expected availability')}:{' '}
              {publication.expectedAvailability || t('2025-08-17')}
            </div> */}
          </div>

          {/* BOTTOM */}
          <div className="space-y-4">
            <ButtonGroup align="right">
              {/* <Tooltip content="Add to wishlist" placement="top" size="sm">
                <Button
                  // label={t('Add to wishlist')}
                  tooltip={t('Add to your wishlist')}
                  size="sm"
                  variant="secondary"
                  icon="heart"
                />
              </Tooltip> */}
              <Button label={t('Loan Now')} size="sm" url={url} />
            </ButtonGroup>
          </div>
        </div>
      </div>
    </div>
  )
}
