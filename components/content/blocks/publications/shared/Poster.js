import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Poster = dynamic(() => import('ui/data-display/Poster'))

export default function PublicationPoster({
  publication,
  showTitle = true,
  showHover = true,
  className = '',
  // imageClass = '', // TODO: not used
}) {
  return (
    <Poster
      className={className}
      enableAnimation={showHover}
      enableTitle={showTitle}
      image={publication.cover?.file}
      title={publication.title}
      url={publication.url}
    />
  )
}
PublicationPoster.propTypes = {
  publication: PropTypes.object.isRequired,
  showDescription: PropTypes.bool,
  showHover: PropTypes.bool,
}
