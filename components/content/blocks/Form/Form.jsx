import PropTypes from 'prop-types'
import React, { useCallback, useState } from 'react'
import { useRef } from 'react'

import clsx from 'clsx'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import Button from 'ui/buttons/Button'
import UIForm from 'ui/data-entry/Form'
import Submit from 'ui/data-entry/Submit'
import useWidth from 'ui/helpers/useWidth'

import FormContextProvider from './FormContext'
import { useFormMutation } from './mutation'

const Alert = dynamic(() => import('ui/feedback/Alert'))

function getResetId() {
  return new Date().getTime().toString()
}

export default function Form({
  children,
  className,
  formTitle,
  contactEmails,
  id,
  pageData,
  submitErrorTitle,
  submitErrorMessage,
  submitLabel,
  sendAnotherLabel,
  submitSuccessTitle,
  submitSuccessMessage,
  variant,
  width,
  blockId,
}) {
  const resetId = useRef(id ?? getResetId())
  const [fieldsConfig, setFieldsConfig] = useState({})
  const { t, i18n } = useTranslation('form', { useSuspense: false })

  const { mutate, isLoading, isSuccess, error, reset } = useFormMutation(
    {
      onSuccess: () => {
        resetId.current = id ? `${id}-${getResetId()}` : getResetId()
      },
    },
    {
      pageId: pageData?.id,
      blockId,
    }
  )

  const widthClass = useWidth(width)

  const onSubmit = useCallback(
    data => {
      const formData = new FormData()

      formData.append('contactEmails', contactEmails)

      const fields = []

      Object.keys(data).forEach(fieldName => {
        if (!fieldsConfig[fieldName]) {
          return
        }

        const field = {
          ...fieldsConfig[fieldName],
          value: data[fieldName],
        }

        if (field.type === 'file') {
          formData.append(`files`, field.value) // Adds every file to the form data
        } else {
          fields.push(field)
        }
      })

      formData.append('fields', JSON.stringify(fields))

      formData.append('formTitle', formTitle)
      formData.append('formUrl', pageData.absoluteUrl)
      formData.append('language', i18n.language)

      // How to log the formData:
      // for (const [key, value] of formData.entries()) {
      //   console.log(key, value)
      // }

      mutate(formData)
    },
    [
      mutate,
      contactEmails,
      fieldsConfig,
      formTitle,
      i18n.language,
      pageData.absoluteUrl,
    ]
  )

  const registerField = useCallback(
    (name, config) => {
      fieldsConfig[name] = config

      setFieldsConfig(fieldsConfig)
    },
    [fieldsConfig]
  )

  if (!contactEmails) {
    return null
  }

  if (isSuccess) {
    return (
      <div className={`flex flex-col space-y-4 ${className}`}>
        <Alert
          title={submitSuccessTitle || t('sendSuccessTitle')}
          type="success"
          message={submitSuccessMessage || t('sendSuccessMessage')}
        />
        <Button
          className="self-end"
          variant="tertiary"
          size="sm"
          onClick={() => reset()}
          icon="arrow-right-long"
          label={sendAnotherLabel || t('sendAnother')}
        />
      </div>
    )
  }

  return (
    <UIForm
      variant={variant}
      onSubmit={onSubmit}
      className={clsx(widthClass, className)}
      id={resetId.current}
    >
      {error && (
        <Alert
          title={submitErrorTitle || t('sendErrorTitle')}
          message={
            error?.response?.data?.message ||
            submitErrorMessage ||
            t('sendErrorMessage')
          }
          type="danger"
        />
      )}
      <FormContextProvider registerField={registerField}>
        {children}
      </FormContextProvider>
      <Submit
        label={submitLabel || t('submit')}
        icon="arrow-right-long"
        loading={isLoading}
      />
    </UIForm>
  )
}
Form.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  contactEmails: PropTypes.string,
  formTitle: PropTypes.string,
  id: PropTypes.string,
  pageData: PropTypes.shape({
    absoluteUrl: PropTypes.string,
    id: PropTypes.string,
  }),
  submitErrorTitle: PropTypes.string,
  submitErrorMessage: PropTypes.string,
  submitLabel: PropTypes.string,
  sendAnotherLabel: PropTypes.string,
  submitSuccessTitle: PropTypes.string,
  submitSuccessMessage: PropTypes.string,
  variant: PropTypes.string,
  width: PropTypes.string,
  blockId: PropTypes.string,
}
