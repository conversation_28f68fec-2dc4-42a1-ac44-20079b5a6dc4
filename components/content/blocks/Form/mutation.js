import axios from 'axios'
import { useMutation } from 'react-query'

const API_URL = process.env.NEXT_PUBLIC_API_URL
const API_CLIENT_TOKEN = process.env.NEXT_PUBLIC_API_CLIENT_TOKEN

function sendFormData(formData, pageId, blockId) {
  return axios.post(`${API_URL}/web/feedback/form`, formData, {
    headers: {
      'ClientToken': API_CLIENT_TOKEN,
      'Content-Type': 'multipart/form-data',
      'Page-Id': pageId,
      'Block-Id': blockId,
    },
    validateStatus: status => status >= 200 && status < 400,
  })
}

export function useFormMutation(options, { pageId, blockId } = {}) {
  const sendFormDataWithIds = formData => {
    return sendFormData(formData, pageId, blockId)
  }
  return useMutation(sendFormDataWithIds, options)
}
