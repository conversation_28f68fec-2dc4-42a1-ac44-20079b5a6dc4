import PropTypes from 'prop-types'
import React from 'react'

import Select from 'ui/data-entry/Select'
import useWidth from 'ui/helpers/useWidth'

import { useRegister<PERSON>ield } from './FormContext'

export default function FormSelect({
  className,
  disabled,
  help,
  label,
  name,
  placeholder,
  options,
  required,
  width,
}) {
  const widthClass = useWidth(width, 'full')
  useRegisterField(name, {
    field: 'select',
    label,
    required,
    disabled,
    options,
  })

  return (
    <Select
      className={`${widthClass} ${className}`}
      disabled={disabled}
      help={help}
      label={label}
      name={name}
      required={required}
      options={options}
      placeholder={placeholder}
    />
  )
}
FormSelect.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  help: PropTypes.string,
  label: PropTypes.string,
  name: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.shape({ label: PropTypes.string, value: PropTypes.string })
  ),
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  width: PropTypes.object,
}
