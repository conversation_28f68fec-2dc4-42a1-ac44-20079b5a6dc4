import React from 'react'
import PropTypes from 'prop-types'

import { signIn } from 'next-auth/react'
import Button from 'ui/buttons/Button'

import useDemoReviewLink from './hooks/useDemoReviewLink'

export default function DemoReviewLink() {
  const [enabled, setEnabled] = React.useState(false)

  const { isLoading } = useDemoReviewLink(enabled)

  const onClick = React.useCallback(async () => {
    const response = await signIn('email', {
      redirect: false, // Prevent the sign in from redirecting.
      email: '<EMAIL>', // This is hardcoded, and should only ever be used for an account with this email, on any provider.
      callbackUrl: '/',
    })

    if (response.status === 200) {
      setEnabled(true)
    }
  }, [])

  return (
    <Button
      label="Log in to the demo account"
      onClick={onClick}
      className="max-w-fit"
      icon={isLoading ? 'spinner-third' : 'login'}
    />
  )
}
DemoReviewLink.propTypes = {
  code: PropTypes.string,
  blockId: PropTypes.string,
}
