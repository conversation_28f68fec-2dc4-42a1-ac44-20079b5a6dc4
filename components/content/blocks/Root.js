import useSpacing from 'ui/helpers/useSpacing'

/**
 * Root block component
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Block content
 * @param {string} props.spacing Block spacing
 * @param {string} props.id Block ID (required for page preview!)
 * @returns {React.ReactElement} Root block
 */
export default function Root({ children, spacing, id }) {
  const spacingClass = useSpacing(spacing)
  return (
    <div className={`flex flex-col ${spacingClass}`} id={id || 'ROOT'}>
      {children}
    </div>
  )
}
