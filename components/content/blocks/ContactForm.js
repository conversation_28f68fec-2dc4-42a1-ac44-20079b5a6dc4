import { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import axios from 'axios'
import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'
import { useFormContext } from 'react-hook-form'

import useWidth from 'ui/helpers/useWidth'

const API_URL = process.env.NEXT_PUBLIC_API_URL
const API_CLIENT_TOKEN = process.env.NEXT_PUBLIC_API_CLIENT_TOKEN

const Button = dynamic(() => import('ui/buttons/Button'))
const Captcha = dynamic(() => import('ui/data-entry/Captcha'))
const Checkbox = dynamic(() => import('ui/data-entry/Checkbox'))
const Form = dynamic(() => import('ui/data-entry/Form'))
const Input = dynamic(() => import('ui/data-entry/Input'))
const Submit = dynamic(() => import('ui/data-entry/Submit'))
const TextArea = dynamic(() => import('ui/data-entry/TextArea'))
const Alert = dynamic(() => import('ui/feedback/Alert'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function ContactForm({
  className,
  contactEmail,
  id,
  showAcceptTerms,
  showEmail,
  showName,
  showPhone,
  showSubject,
  termsUrl,
  width,
}) {
  const [formSent, setFormSent] = useState(false)
  const [showCaptcha, setShowCaptcha] = useState(false)
  const [error, setError] = useState(false)
  const { t } = useTranslation('contact-form', { useSuspense: false })

  const widthClass = useWidth(width)

  const onSubmit = useCallback(
    async data => {
      try {
        await axios.post(
          `${API_URL}/web/feedback/contact-form`,
          { contactEmail, ...data, showAcceptTerms },
          {
            headers: {
              ClientToken: API_CLIENT_TOKEN,
            },
          }
        )
        setFormSent(true)
      } catch (error) {
        setError(true)
        console.log(error) // eslint-disable-line no-console
      }
      setShowCaptcha(false)
    },
    [contactEmail, showAcceptTerms]
  )

  if (formSent) {
    return (
      <Alert
        title={t('sendSuccessTitle')}
        message={t('sendSuccessMessage')}
        type="success"
      />
    )
  }

  return (
    <Form
      onSubmit={onSubmit}
      validationMode="onChange"
      className={`${widthClass} ${className}`}
      id={id}
    >
      {error && (
        <Alert
          title={t('sendErrorTitle')}
          message={t('sendErrorMessage')}
          type="danger"
        />
      )}
      {showCaptcha ? (
        <div className="space-y-4">
          <Captcha
            name="captcha"
            label={t('captcha')}
            help={t('captchaHelp')}
            placeholder={t('captchaPlaceholder')}
            required
          />
          <Submit label={t('common:submit')} />
        </div>
      ) : (
        <div className="space-y-4">
          {showName && (
            <Input
              name="name"
              label={t('name')}
              placeholder={t('namePlaceholder')}
              required
            />
          )}
          {showEmail && (
            <Input
              name="email"
              label={t('email')}
              placeholder={t('emailPlaceholder')}
              type="email"
              required
            />
          )}
          {showPhone && (
            <Input name="phone" label={t('phone')} placeholder="01 2345 6789" />
          )}
          {showSubject && (
            <Input name="subject" label={t('subject')} readOnly />
          )}
          <TextArea
            name="message"
            label={t('message')}
            placeholder={t('messagePlaceholder')}
            required
          />
          {showAcceptTerms && (
            <div className="space-y-2">
              <Checkbox name="acceptTerms" label={t('acceptTerms')} required />
              {termsUrl && (
                <span className="pl-6">
                  <Link to={termsUrl} basic>
                    {t('readTerms')}
                  </Link>
                </span>
              )}
            </div>
          )}
          <ContinueButton onClick={() => setShowCaptcha(true)} />
        </div>
      )}
    </Form>
  )
}
ContactForm.propTypes = {
  className: PropTypes.string,
  contactEmail: PropTypes.string,
  id: PropTypes.string,
  showAcceptTerms: PropTypes.bool,
  showEmail: PropTypes.bool,
  showName: PropTypes.bool,
  showPhone: PropTypes.bool,
  showSubject: PropTypes.bool,
  termsUrl: PropTypes.string,
  width: PropTypes.object,
}

function ContinueButton({ onClick }) {
  const { t } = useTranslation('contact-form', { useSuspense: false })
  const { formState } = useFormContext()
  const { isValid } = formState

  return (
    <Button
      disabled={!isValid}
      label={t('common:continue')}
      onClick={onClick}
    />
  )
}
ContinueButton.propTypes = {
  onClick: PropTypes.func,
}
