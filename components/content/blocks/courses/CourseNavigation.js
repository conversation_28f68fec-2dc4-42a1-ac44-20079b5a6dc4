import PropTypes from 'prop-types'
import React, { useMemo } from 'react'

import dynamic from 'next/dynamic'

import { useMatchMedia } from 'hooks/useMatchMedia'
import Clickable from 'ui/helpers/Clickable'
import useToggle from 'ui/helpers/useToggle'
import { media } from 'utils/media'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Menu = dynamic(() => import('ui/navigation/Menu'))

export default function CourseNavigation({
  courseNavigation,
  className = '',
  displayMode,
}) {
  const { lessons, pageSettings } = courseNavigation || {}

  const showChildren = true

  const menuItems = useMemo(() => {
    return lessons?.map(lesson => ({
      label: lesson.title,
      url: lesson.url,
      active: lesson.isActive,
      children:
        showChildren ||
        !lesson.isLocked ||
        lesson.slides?.find(s => !s.isLocked)
          ? lesson.slides?.map(slide => {
              return {
                label: slide.title,
                url: slide.url,
                active: slide.isActive,
                icon: slide.isLocked
                  ? 'lock'
                  : slide.isRead
                    ? 'circle-check'
                    : 'circle',
                iconClass: slide.isLocked ? 'opacity-50' : '',
                marked: slide.isRead,
              }
            })
          : null,
    }))
  }, [lessons, showChildren])

  if (!courseNavigation || !lessons) return null

  return displayMode === 'fixed' ? (
    <Menu className={`w-full course-menu ${className}`} items={menuItems} />
  ) : (
    <FloatingNavigation
      className={className}
      menuItems={menuItems}
      pageSettings={pageSettings}
    />
  )
}
CourseNavigation.propTypes = {
  className: PropTypes.string,
  courseNavigation: PropTypes.object,
}

function FloatingNavigation({ className, menuItems, pageSettings }) {
  const [open, toggleOpen] = useToggle()
  const [isTransitioning, setIsTransitioning] = React.useState(false)

  const isLg = useMatchMedia(media.lg)
  const isSm = useMatchMedia(media.sm)

  if (!isLg) {
    menuItems = menuItems.map(lesson => {
      return {
        ...lesson,
        onClick: toggleOpen,
        children: lesson?.children?.map(slide => ({
          ...slide,
          onClick: toggleOpen,
        })),
      }
    })
  }

  const { chromeless, topMenu } = pageSettings || {}

  /*
    Component heights:
    ------------------------------
    Top menu: 32px, 2rem, top-8
    Header (small): 64px, 4rem, top-16
    Header (large): 96px, 6rem, top-24
    Top menu + header (small): 96px, 6rem, top-24
    Top menu + header (large): 128px, 8rem, top-32
  */

  const topPositionClass = chromeless
    ? 'top-0'
    : topMenu
      ? 'top-24 lg:top-32'
      : 'top-16 lg:top-24'

  return (
    <div
      className={`w-full view sm:w-96 mt-px bg-white shadow-xl fixed transition-all duration-500 z-max ${
        open ? 'right-0' : '-right-full sm:-right-96'
      } ${topPositionClass} ${className}`}
      style={{
        height: chromeless
          ? '100vh'
          : topMenu
            ? isLg
              ? 'calc(100vh - 8rem)'
              : 'calc(100vh - 6rem)'
            : isLg
              ? 'calc(100vh - 6rem)' // 6rem is the height of the header starting with large screens
              : 'calc(100vh - 4rem)', // 4rem is the height of the header for small screens
      }}
    >
      <div className="w-full h-full overflow-auto overflow-x-hidden no-scrollbar p-10">
        <div className="space-y-4">
          {!isSm && (
            <Clickable
              className={`w-10 h-10 flex items-center justify-center rounded-full bg-gray-200 hover:bg-gray-300 hover:scale-105 duration-300 ml-auto ${
                open ? '' : ''
              }`}
              onClick={toggleOpen}
            >
              <Icon name="times" className="text-2xl" />
            </Clickable>
          )}
          <Menu className="w-full course-menu" items={menuItems} />
        </div>
      </div>
      <Clickable
        className={`h-12 bg-primary-700 items-center justify-center absolute bottom-8 sm:top-8 cursor-pointer transition-all duration-500 rounded-full shadow-sm shadow-white ${
          open
            ? '-left-10 pl-2 rounded-r-none w-10 hidden sm:flex'
            : '-left-24 w-12 flex'
        }`}
        onClick={() => {
          setIsTransitioning(true)
          toggleOpen()
          setTimeout(() => setIsTransitioning(false), 300)
        }}
      >
        <Icon
          name={
            isTransitioning
              ? open
                ? 'list'
                : 'times'
              : open
                ? 'times'
                : 'list'
          }
          className="text-white text-2xl"
        />
      </Clickable>
    </div>
  )
}

function LessonItem({ lesson }) {
  const { title, slides } = lesson
  return (
    <div>
      <div>{title}</div>
      {slides && (
        <div className="pl-8">
          {slides.map(slide => (
            <SlideItem key={slide.id} slide={slide} />
          ))}
        </div>
      )}
    </div>
  )
}
LessonItem.propTypes = {
  lesson: PropTypes.object,
}

function SlideItem({ slide }) {
  const { title } = slide
  return <div>{title}</div>
}
SlideItem.propTypes = {
  slide: PropTypes.object,
}
