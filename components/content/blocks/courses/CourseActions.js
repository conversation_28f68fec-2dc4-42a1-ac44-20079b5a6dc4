/* eslint-disable no-console */
import PropTypes from 'prop-types'
import React, { useCallback, useState } from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'

const Button = dynamic(() => import('ui/buttons/Button'))
const ButtonGroup = dynamic(() => import('ui/buttons/ButtonGroup'))
import { Dialog, DialogContent } from 'ui/feedback/FloatingDialog'

import CourseModeSelect from './shared/CourseModeSelect'

import useBookmarkCourse from './api/useBookmarkCourse'
import useStartCourse from './api/useStartCourse'

export default function CourseActions({
  className = '',
  courseActions,
  coursesProviderCountries,
}) {
  if (!courseActions || !courseActions.actions?.length) return null
  const { courseId, courseSettings, providerId, actions, providerSettings } =
    courseActions

  const buttonClass = actions.length === 1 ? className : ''

  const buttons = actions.map(action => {
    switch (action.name) {
      case 'register':
        return (
          <LinkButton
            action={action}
            className={buttonClass}
            variant="alt"
            key={action.name}
          />
        )
      case 'start':
        return (
          <StartButton
            action={action}
            className={buttonClass}
            providerId={providerId}
            providerCountries={coursesProviderCountries}
            courseId={courseId}
            courseSettings={courseSettings}
            providerSettings={providerSettings}
            key={action.name}
          />
        )
      case 'continue':
        return (
          <LinkButton
            action={action}
            className={buttonClass}
            key={action.name}
            variant="alt"
          />
        )
      case 'view':
        return (
          <LinkButton
            action={action}
            className={buttonClass}
            key={action.name}
            variant="alt"
          />
        )
      case 'preview':
        return (
          <LinkButton
            action={action}
            className={buttonClass}
            key={action.name}
            variant="alt-outline"
          />
        )
      case 'save':
        return (
          <SaveButton
            action={action}
            className={buttonClass}
            courseId={courseId}
            key={action.name}
          />
        )
    }
  })

  return (
    <div>
      {buttons.length > 1 ? (
        <ButtonGroup className={`justify-center ${className}`}>
          {buttons}
        </ButtonGroup>
      ) : (
        buttons
      )}
    </div>
  )
}
CourseActions.propTypes = {
  className: PropTypes.string,
  courseActions: PropTypes.object,
  coursesProviderCountries: PropTypes.array,
}

function StartButton({
  courseId,
  courseSettings,
  providerId,
  providerCountries,
  action,
  className,
  providerSettings,
}) {
  const { t } = useTranslation('courses')
  const [showModal, setShowModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const { autonomousCourse, supervisedCourse } = courseSettings
  const { removeStartConfirmation } = providerSettings

  const { mutate: startCourse } = useStartCourse(courseId, providerId)

  const handleStart = useCallback(
    ({ mode, ...regionArgs }) => {
      setLoading(true)
      startCourse(
        { mode, ...regionArgs },
        {
          onSuccess: () => {
            // Short delay to allow the database to complete the write operation (the courseContent resource of the course page doesn't find the courseStatus immediately in some cases)
            setTimeout(() => {
              const timestamp = new Date().getTime()
              router.push(
                `${action.url}?apicache-bypass=${timestamp}`,
                undefined,
                {
                  unstable_skipClientCache: true,
                }
              )
            }, 700)
          },
        }
      )
    },
    [action.url, router, startCourse]
  )

  const handleButtonClick = () => {
    if (autonomousCourse && !supervisedCourse && removeStartConfirmation) {
      handleStart({ mode: 'autonomous' })
    } else {
      setShowModal(true)
    }
  }

  return (
    <>
      <Button
        label={t(`courseAction-${action.name}`)}
        onClick={handleButtonClick}
        className={className}
        icon={loading ? 'spinner-third' : ''}
        iconClass="animate-spin"
        variant="alt"
        size="lg"
      />
      <Dialog
        prefix={'title'}
        open={showModal}
        onOpenChange={status => setShowModal(status)}
      >
        <DialogContent
          title={
            autonomousCourse && supervisedCourse
              ? t('courseModeTitleOptions')
              : t('courseModeTitleSingle')
          }
          boxClass="max-w-xl"
        >
          <CourseModeSelect
            autonomous={autonomousCourse}
            supervised={supervisedCourse}
            onStart={handleStart}
            loading={loading}
            providerCountries={providerCountries}
          />
        </DialogContent>
      </Dialog>
    </>
  )
}

function LinkButton({ action, className, variant }) {
  const { t } = useTranslation('courses')
  const [loading, setLoading] = useState(false)
  const handleClick = useCallback(() => setLoading(true), [])
  return (
    <Button
      className={className}
      label={t(`courseAction-${action.name}`)}
      icon={loading ? 'spinner-third' : ''}
      iconClass="animate-spin"
      onClick={handleClick}
      url={action.url}
      variant={variant}
      size="lg"
    />
  )
}

function SaveButton({ action, className, courseId }) {
  const { t } = useTranslation('courses')

  const { mutate, isLoading } = useBookmarkCourse(courseId)

  const handleSave = () => {
    mutate(null, {
      onSuccess: () => {
        console.log('course started')
        console.log('redirect to', action.url)
      },
    })
  }

  return (
    <Button
      className={className}
      disabled={isLoading}
      onClick={handleSave}
      icon="heart"
      label={t(`courseAction-${action.name}`)}
      variant="link"
    />
  )
}
