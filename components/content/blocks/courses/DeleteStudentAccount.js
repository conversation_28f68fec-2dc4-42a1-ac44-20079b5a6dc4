import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'
import { signOut } from 'next-auth/react'

import { Dialog, DialogContent } from 'ui/feedback/FloatingDialog'
const Button = dynamic(() => import('ui/buttons/Button'))
const Header = dynamic(() => import('ui/data-display/Header'))
const Text = dynamic(() => import('ui/typography/Text'))

import { usePageContext } from 'components/PageProvider'

import useDeleteStudentAccount from './api/useDeleteStudentAccount'
import Icon from 'ui/icons/Icon'

export default function DeleteStudentAccount({
  keepWebUser,
  className = '',
  coursesStudentProfile,
}) {
  const { t } = useTranslation(['courses', 'common'])
  const [showModal, setShowModal] = useState(false)

  const { student } = coursesStudentProfile

  const pageContext = usePageContext()
  const [persistedLoading, setPersistedLoading] = useState(false)

  const { mutate } = useDeleteStudentAccount(
    student._id,
    pageContext?.cacheKey,
    keepWebUser
  )

  const handleDelete = useCallback(() => {
    setPersistedLoading(true)
    mutate(undefined, {
      onSuccess: () => {
        signOut({ callbackUrl: '/' })
      },
      onError: () => {
        setPersistedLoading(false)
      },
    })
  }, [mutate])

  return (
    <div className={`w-full space-y-8 ${className}`}>
      <Header title={t('deleteMyAccount')} />
      <Text>{t('deleteAccountDescription')}</Text>
      <Dialog open={showModal} onOpenChange={status => setShowModal(status)}>
        <Button label={t('deleteAccount')} onClick={() => setShowModal(true)} />
        <DialogContent
          boxClass="max-w-lg w-full"
          contentClass="mx-4 mb-4 flex flex-col items-center gap-4"
        >
          <Icon
            name="triangle-exclamation"
            className="text-danger-500 text-7xl"
          />
          <Text textSize={{ xs: 'xl', md: '2xl' }} align="center">
            {t('deleteAccountConfirmTitle')}
          </Text>
          <Text align="center">{t('deleteAccountConfirmDescription')}</Text>
          <Text
            className="py-4"
            textSize={{ xs: 'base', md: 'xl' }}
            fontWeight="medium"
            color="danger-500"
            align="center"
          >
            {t('deleteAccountConfirmIrreversible')}
          </Text>
          <div className="flex justify-end gap-4 pt-4">
            <Button
              label={t('common:cancel')}
              variant="link"
              onClick={() => setShowModal(false)}
            />
            <Button
              label={t('common:delete')}
              onClick={handleDelete}
              variant="danger"
              className={persistedLoading ? '' : ''}
              icon={persistedLoading ? 'spinner-third' : ''}
              iconClass="animate-spin"
              disabled={persistedLoading}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
DeleteStudentAccount.propTypes = {
  className: PropTypes.string,
  coursesStudentProfile: PropTypes.object,
  keepWebUser: PropTypes.bool,
}
