import PropTypes from 'prop-types'

import Content from 'components/content/Content'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import Alert from 'ui/feedback/Alert'
import Icon from 'ui/icons/Icon'

import CoursePagination from './CoursePagination'
import Questionnaire from './questionnaire/Questionnaire'
import PageNotFound from 'components/PageNotFound'

const Tag = dynamic(() => import('ui/data-display/Tag'))
const SeparatorLine = dynamic(() => import('ui/data-display/SeparatorLine'))

export default function CourseContent({
  courseContent,
  coursePagination,
  coursesProviderCountries,
  showTitle,
  showContent,
  showPagination,
  completed, // TRUE when last questionnaire that doesn't require review was submitted
}) {
  const { t } = useTranslation('courses')

  if (!courseContent) return null

  // Return 404 error if slide content is not found
  if (courseContent.notFound) {
    return <PageNotFound />
  }

  const {
    title,
    type,
    content,
    questionnaire,
    courseStatus,
    courseSettings,
    questionnaireStatus,
    isLocked,
    courseCompleted, // TRUE when last slide was read
  } = courseContent

  if (isLocked)
    return (
      <div className="flex items-center justify-center w-full">
        <Alert type="warning" className="w-full">
          <div className="text-center space-y-4 my-10 mx-4">
            <Icon name="lock" className="text-4xl" />
            <div className="text-2xl font-semibold">{t('contentLocked')}</div>
          </div>
        </Alert>
      </div>
    )

  return (
    <div className="w-full space-y-10">
      {(completed || courseCompleted) && (
        <Alert className="text-center" type="success">
          <div className="space-y-8 py-5">
            <h4 className="font-bold text-3xl text-success-700">
              {t('courseCompletedTitle')}
            </h4>
            <div>{t('courseCompletedDescription')}</div>
          </div>
        </Alert>
      )}
      {!courseStatus && <Tag label={t('preview')} />}
      {showTitle && (
        <div className="space-y-6">
          <h1 className="font-bold text-4xl">{title}</h1>
          <SeparatorLine />
        </div>
      )}
      {showContent && (
        <>
          {type === 'questionnaire' ? (
            <Questionnaire
              courseSettings={courseSettings}
              courseStatus={courseStatus}
              providerCountries={coursesProviderCountries}
              questionnaire={questionnaire}
              questionnaireStatus={questionnaireStatus}
            />
          ) : (
            <Content page={{ id: 'root', content }} />
          )}
        </>
      )}
      {showPagination && (
        <CoursePagination
          className="pt-2 md:pt-16"
          coursePagination={coursePagination}
          courseStatus={courseStatus}
        />
      )}
    </div>
  )
}
CourseContent.propTypes = {
  courseContent: PropTypes.object,
}
