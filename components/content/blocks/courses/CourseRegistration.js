import React, { useCallback, useState } from 'react'

import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { useTranslation } from 'next-i18next'

const Alert = dynamic(() => import('ui/feedback/Alert'))
const Button = dynamic(() => import('ui/buttons/Button'))
const Heading = dynamic(() => import('ui/typography/Heading'))
const Form = dynamic(() => import('ui/data-entry/Form'))
const Input = dynamic(() => import('ui/data-entry/Input'))
const Submit = dynamic(() => import('ui/data-entry/Submit'))
const Text = dynamic(() => import('ui/typography/Text'))

import AcceptTermsCheckbox from '../auth/shared/AcceptTermsCheckbox'
import useRegisterCorrespondenceCourse from './api/useRegisterCorrespondenceCourse'
import { useSiteAuth } from 'hooks/useSiteAuth'

export default function CourseRegistration({
  courseRegistration,
  className = '',
}) {
  const { t } = useTranslation(['common', 'courses'])
  const { courseId, correspondenceCourse, email } = courseRegistration || {}
  const [registrationSuccess, setRegistrationSuccess] = useState(false)
  const { terms } = useSiteAuth()

  const {
    mutate,
    isLoading: isSubmitting,
    isError,
  } = useRegisterCorrespondenceCourse(courseId, email)

  const onSubmit = useCallback(
    formValues => {
      mutate(formValues, {
        onSuccess: () => {
          setRegistrationSuccess(true)
        },
      })
    },
    [mutate]
  )

  if (!courseId || !correspondenceCourse || !email) return null

  return (
    <div className={`w-full space-y-12 ${className}`}>
      {isError && (
        <Alert
          title={t('courses:registrationErrorTitle')}
          message={t('courses:registrationErrorMessage')}
          type="danger"
        />
      )}
      <div className="space-y-4">
        <Heading
          as="h2"
          align="center"
          fontWeight="semibold"
          title={t('courses:registerNow')}
          textSize={{ xs: 'xl', md: '3xl' }}
        />
        <Text
          align="center"
          text={t('courses:registerNowDescription')}
          textSize={{ xs: 'md', md: 'lg' }}
        />
      </div>
      {registrationSuccess ? (
        <div className="space-y-4">
          <Alert type="success" title={t('courses:registrationSuccessTitle')}>
            {t('courses:registrationSuccessMessage')}
          </Alert>
          <Button
            onClick={() => setRegistrationSuccess(false)}
            label={t('courses:makeFurtherRegistration')}
            variant="tertiary"
          />
        </div>
      ) : (
        <Form onSubmit={onSubmit}>
          <div className="space-y-2">
            <div className="flex flex-col md:flex-row gap-8 md:gap-4">
              <Input
                className="flex-1"
                label={t('firstName')}
                name="firstName"
                required
              />
              <Input
                className="flex-1"
                label={t('lastName')}
                name="lastName"
                required
              />
            </div>
            <Input label={t('street')} name="street" required />
            <div className="flex flex-col md:flex-row gap-8 md:gap-4">
              <Input
                className="w-full md:w-1/3"
                label={t('zip')}
                name="zip"
                required
              />
              <Input
                className="w-full md:w-2/3"
                label={t('city')}
                name="city"
                required
              />
            </div>
            <Input label={t('country')} name="country" required />
            <div className="pt-4">
              <AcceptTermsCheckbox terms={terms} />
            </div>
            <div className="pt-4">
              <Submit label={t('submit')} loading={isSubmitting} />
            </div>
          </div>
        </Form>
      )}
    </div>
  )
}
CourseRegistration.propTypes = {
  courseRegistration: PropTypes.object,
  className: PropTypes.string,
}
