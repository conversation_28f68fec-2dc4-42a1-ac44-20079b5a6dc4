import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

import { isEmpty } from 'utils/arrays'

const Empty = dynamic(() => import('ui/data-display/Empty'))
const Text = dynamic(() => import('ui/typography/Text'))

const AdvisorBenefits = dynamic(() => import('./shared/AdvisorBenefits'))
const AdvisorCard = dynamic(() => import('./advisors/AdvisorCard'))

export default function StudentAdvisors({
  className = '',
  coursesStudentAdvisors,
  showAddress,
  showEmail,
  showMessagesLink,
}) {
  const { t } = useTranslation('courses')

  if (isEmpty(coursesStudentAdvisors)) {
    return (
      <Empty title={t('noAdvisorTitle')} icon="user">
        <div className="pt-6 space-y-8">
          <Text align="center">
            {t('requestionSupervisionDescriptionBenefits')}
          </Text>
          <AdvisorBenefits className="border bg-gray-100 p-4 md:p-6 rounded-lg" />
          <Text align="center" className="px-0 md:px-20">
            {t('getAnAdvisorDescription')}
          </Text>
        </div>
      </Empty>
    )
  }

  return (
    <div className={`w-full divide-y ${className}`}>
      {coursesStudentAdvisors.map(advisor => (
        <AdvisorCard
          className="py-10"
          key={advisor?._id}
          advisor={advisor}
          showAddress={showAddress}
          showEmail={showEmail}
          showMessagesLink={showMessagesLink}
        />
      ))}
    </div>
  )
}
StudentAdvisors.propTypes = {
  coursesStudentAdvisors: PropTypes.array,
  className: PropTypes.string,
}
