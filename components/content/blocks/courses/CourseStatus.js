import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const CourseMode = dynamic(() => import('./shared/CourseMode'))
const CourseProgressBar = dynamic(() => import('./shared/CourseProgressBar'))

export default function CourseStatus({
  courseStatus,
  coursesProviderCountries,
  className = '',
}) {
  if (!courseStatus) return null
  return (
    <div
      className={`w-full flex flex-col md:flex-row justify-between items-center gap-4 border rounded-lg p-4 md:p-8 bg-gray-100 ${className}`}
    >
      <CourseProgressBar
        className="w-full md:max-w-md"
        courseStatus={courseStatus}
      />
      <CourseMode
        courseStatus={courseStatus}
        course={courseStatus.course}
        providerCountries={coursesProviderCountries}
      />
    </div>
  )
}
CourseStatus.propTypes = {
  courseStatus: PropTypes.object,
  coursesProviderCountries: PropTypes.array,
}
