import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const CardContainer = dynamic(() => import('ui/data-display/CardContainer'))
const Link = dynamic(() => import('ui/navigation/Link'))

const CourseCard = dynamic(() => import('./shared/CourseCard'))
const CoursePoster = dynamic(() => import('./shared/CoursePoster'))

export default function CoursesCollection({
  coursesCollection,
  hideTitle,
  layout = 'card',
  enableTitleInPosters,
  enableAbstractInPosters,
  enableAbstractInCards,
  enableCallToActionInPosters,
  callToActionLabel,
}) {
  const { t } = useTranslation('courses')

  if (!coursesCollection) return null

  const { courses, title, url } = coursesCollection

  return (
    <CardContainer
      title={hideTitle ? null : title}
      extra={
        url && (
          <Link className="font-semibold uppercase" to={url} basic>
            {t('common:viewAll')}
          </Link>
        )
      }
    >
      {courses?.map(course =>
        layout === 'poster' ? (
          <CoursePoster
            key={course._id}
            course={course}
            enableTitle={enableTitleInPosters}
            enableAbstract={enableAbstractInPosters}
            enableCallToAction={enableCallToActionInPosters}
            callToActionLabel={callToActionLabel}
          />
        ) : (
          <CourseCard
            key={course._id}
            course={course}
            showDescription={enableAbstractInCards}
          />
        )
      )}
    </CardContainer>
  )
}
CoursesCollection.propTypes = {
  coursesCollection: PropTypes.object,
  hideTitle: PropTypes.bool,
  layout: PropTypes.string,
}
