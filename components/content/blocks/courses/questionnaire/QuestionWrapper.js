import PropTypes from 'prop-types'
import React from 'react'

export default function QuestionWrapper({
  question,
  questionNumber,
  showRequired,
  children,
}) {
  const { text, config } = question
  const { required } = config

  const questionClass = 'font-bold text-lg leading-tight'

  return (
    <div className="space-x-2 flex">
      {questionNumber && <div className={questionClass}>{questionNumber}.</div>}
      <div className="space-y-6 w-full">
        <div className="font-bold text-lg leading-tight">
          {text}{' '}
          {showRequired && required ? (
            <span className="text-danger-500">*</span>
          ) : (
            ''
          )}
        </div>
        {children}
      </div>
    </div>
  )
}

QuestionWrapper.propTypes = {
  children: PropTypes.node,
  question: PropTypes.object.isRequired,
  questionNumber: PropTypes.number,
  showRequired: PropTypes.bool,
}
