import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'

const Answer = dynamic(() => import('../Answer'))
const AnswerNotes = dynamic(() => import('../AnswerNotes'))
const QuestionWrapper = dynamic(() => import('../QuestionWrapper'))
const TextArea = dynamic(() => import('ui/data-entry/TextArea'))

export default function MultipleOpenAnswers({
  name,
  question,
  questionNumber,
  questionnaireStatus,
  preview,
}) {
  const isSubmitted = !!questionnaireStatus?.submittedAt

  // Don't render if there are no text boxes
  if (!question?.config?.textBoxes?.filter(tb => tb.enabled).length) return null

  return (
    <QuestionWrapper
      question={question}
      questionNumber={questionNumber}
      showRequired={!isSubmitted}
    >
      {preview || !isSubmitted ? (
        <QuestionFields name={name} question={question} disabled={preview} />
      ) : (
        <QuestionAnswers
          question={question}
          questionnaireStatus={questionnaireStatus}
        />
      )}
    </QuestionWrapper>
  )
}

MultipleOpenAnswers.propTypes = {
  question: PropTypes.object,
  questionnaireStatus: PropTypes.object,
  questionNumber: PropTypes.number,
  name: PropTypes.string,
}

function QuestionFields({ question, name, disabled }) {
  return (
    <div className="space-y-8">
      {question.config.textBoxes
        .filter(textBox => textBox.enabled)
        .map((textBox, key) => {
          const { config, text } = textBox
          const { required, rows } = config

          return (
            <div className="space-y-4" key={key}>
              <p className="font-semibold">
                {text}{' '}
                {required ? (
                  <span className="text-danger-500 font-bold text-lg">*</span>
                ) : (
                  ''
                )}
              </p>
              <TextArea
                name={`${name}.${textBox.id}`}
                rows={rows}
                disabled={disabled}
                required={required}
              />
            </div>
          )
        })}
    </div>
  )
}

function QuestionAnswers({ question, questionnaireStatus }) {
  return (
    <div className="space-y-8">
      {question.config.textBoxes
        .filter(textBox => textBox.enabled)
        .map((textBox, key) => {
          const { answers, advisorNotes, submittedAt, correctedAt } =
            questionnaireStatus

          const answer = answers?.[question._id]?.[textBox.id]
          const _advisorNotes = advisorNotes?.[question._id]?.[textBox.id]
          const automaticFeedback = textBox.config?.feedback

          return (
            <div className="space-y-4" key={key}>
              <p className="font-semibold">{textBox.text}</p>
              <div className="space-y-5">
                <Answer answer={answer} />
                {submittedAt && (
                  <div className="space-y-4">
                    <AnswerNotes
                      content={automaticFeedback}
                      type="automatic"
                      variant="info"
                    />
                    {correctedAt && (
                      <AnswerNotes
                        // advisor={questionnaireStatus.advisor}
                        text={_advisorNotes}
                        type="advisor"
                        variant="correct"
                      />
                    )}
                  </div>
                )}
              </div>
            </div>
          )
        })}
    </div>
  )
}
