import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'

const Answer = dynamic(() => import('../Answer'))
const AnswerNotes = dynamic(() => import('../AnswerNotes'))
const QuestionWrapper = dynamic(() => import('../QuestionWrapper'))
const TextArea = dynamic(() => import('ui/data-entry/TextArea'))

export default function OpenAnswer({
  name,
  question,
  questionNumber,
  questionnaireStatus,
  preview,
}) {
  const isSubmitted = !!questionnaireStatus?.submittedAt
  return (
    <QuestionWrapper
      question={question}
      questionNumber={questionNumber}
      showRequired={!isSubmitted}
    >
      {preview || !isSubmitted ? (
        <QuestionField name={name} question={question} disabled={preview} />
      ) : (
        <QuestionAnswer
          question={question}
          questionnaireStatus={questionnaireStatus}
        />
      )}
    </QuestionWrapper>
  )
}

OpenAnswer.propTypes = {
  question: PropTypes.object,
  questionnaireStatus: PropTypes.object,
  questionNumber: PropTypes.number,
  name: PropTypes.string,
}

function QuestionField({ question, name, disabled }) {
  const { config } = question
  const { required, rows } = config

  return (
    <TextArea name={name} rows={rows} disabled={disabled} required={required} />
  )
}

function QuestionAnswer({ question, questionnaireStatus }) {
  if (!questionnaireStatus) return null

  const { answers, advisorNotes, correctedAt } = questionnaireStatus

  const answer = answers?.[question._id]
  const _advisorNotes = advisorNotes?.[question._id]
  const automaticFeedback = question.config?.feedback

  return (
    <div className="space-y-5">
      <Answer answer={answer} />
      <div className="space-y-4">
        <AnswerNotes
          content={automaticFeedback}
          type="automatic"
          variant="info"
        />
        {correctedAt && (
          <AnswerNotes
            // advisor={questionnaireStatus.advisor}
            text={_advisorNotes}
            type="advisor"
            variant="correct"
          />
        )}
      </div>
    </div>
  )
}
