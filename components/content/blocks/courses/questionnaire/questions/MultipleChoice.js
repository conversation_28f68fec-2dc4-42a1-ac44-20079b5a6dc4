import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'

const AnswerNotes = dynamic(() => import('../AnswerNotes'))
const Checkbox = dynamic(() => import('ui/data-entry/Checkbox'))
const Option = dynamic(() => import('../Option'))
const QuestionWrapper = dynamic(() => import('../QuestionWrapper'))

export default function MultipleChoice({
  name,
  question,
  questionNumber,
  questionnaireStatus,
  preview,
}) {
  const isSubmitted = !!questionnaireStatus?.submittedAt

  // Don't render if there are no options
  if (!question?.config?.options?.filter(o => o.enabled).length) return null

  return (
    <QuestionWrapper
      question={question}
      questionNumber={questionNumber}
      showRequired={!isSubmitted}
    >
      {preview || !isSubmitted ? (
        <QuestionFields name={name} question={question} disabled={preview} />
      ) : (
        <QuestionAnswer
          question={question}
          questionnaireStatus={questionnaireStatus}
        />
      )}
    </QuestionWrapper>
  )
}

MultipleChoice.propTypes = {
  question: PropTypes.object,
  questionnaireStatus: PropTypes.object,
  questionNumber: PropTypes.number,
  name: PropTypes.string,
}

function QuestionFields({ question, name, disabled }) {
  const { config } = question

  const layout = config?.layout || 'vertical'
  const variant = config?.variant || 'chip'

  return (
    <div
      className={`flex ${
        layout === 'horizontal' ? 'flex-row gap-3' : 'flex-col gap-2'
      }`}
    >
      {config.options
        .filter(option => option.enabled)
        .map((option, key) => {
          const { label } = option
          return (
            <Checkbox
              key={key}
              label={label}
              // label={`${label} - ${option.id}`}
              name={`${name}.${option.id}`}
              disabled={disabled}
              variant={variant}
            />
          )
        })}
    </div>
  )
}

function QuestionAnswer({ question, questionnaireStatus }) {
  if (!questionnaireStatus) return null

  const { answers, advisorNotes, correctedAt } = questionnaireStatus

  return (
    <div className="space-y-4">
      {question.config.options
        .filter(option => option.enabled)
        .map((option, key) => {
          // The options the student selected
          const isSelected = !!answers?.[question._id]?.[option.id]

          // The notes/reply to the given answer
          const _advisorNotes = advisorNotes?.[question._id]?.[option.id]
          const automaticNeutralAnswerFeedback = option.neutralAnswerFeedback
          const automaticWrongAnswerFeedback = option.wrongAnswerFeedback

          const isNeutralAnswer = option.solution === 'neutral'
          const isCorrectAnswer =
            (option.solution === 'correct' && isSelected) ||
            (option.solution === 'incorrect' && !isSelected)

          return (
            <div className="space-y-3" key={key}>
              <Option option={option} selected={isSelected} type="checkbox" />
              <div className="space-y-4">
                {isNeutralAnswer && (
                  <AnswerNotes
                    content={automaticNeutralAnswerFeedback}
                    type="automatic"
                    variant="info"
                  />
                )}
                {!isNeutralAnswer && !isCorrectAnswer && (
                  <AnswerNotes
                    content={automaticWrongAnswerFeedback}
                    type="automatic"
                    variant="wrong"
                  />
                )}
                {correctedAt && (
                  <AnswerNotes
                    // advisor={questionnaireStatus.advisor}
                    text={_advisorNotes}
                    type="advisor"
                    variant={
                      isNeutralAnswer
                        ? 'correct'
                        : isCorrectAnswer
                        ? 'correct'
                        : 'wrong'
                    }
                  />
                )}
              </div>
            </div>
          )
        })}
    </div>
  )
}
