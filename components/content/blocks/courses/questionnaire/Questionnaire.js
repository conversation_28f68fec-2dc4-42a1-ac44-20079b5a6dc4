import PropTypes from 'prop-types'
import React, { useCallback, useEffect, useMemo, useState } from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router.js'
import { useFormContext } from 'react-hook-form'
import useGoToTop from 'ui/helpers/useGoToTop.js'

import useSubmitQuestionnaire from '../api/useSubmitQuestionnaire.js'
import Icon from 'ui/icons/Icon.js'
import { formatDate, useDatetimeLocale } from 'utils/datetime.js'

const Alert = dynamic(() => import('ui/feedback/Alert.js'))
const Button = dynamic(() => import('ui/buttons/Button.js'))
const Form = dynamic(() => import('ui/data-entry/Form'))
const MultipleChoice = dynamic(() => import('./questions/MultipleChoice.js'))
const MultipleOpenAnswers = dynamic(
  () => import('./questions/MultipleOpenAnswers.js')
)
const OpenAnswer = dynamic(() => import('./questions/OpenAnswer.js'))
const RatingScale = dynamic(() => import('./questions/RatingScale.js'))
const RequestSupervision = dynamic(() => import('./RequestSupervision.js'))
const RichText = dynamic(() => import('./questions/RichText.js'))
const SectionTitle = dynamic(() => import('./questions/SectionTitle.js'))
const SingleChoice = dynamic(() => import('./questions/SingleChoice.js'))
const Submit = dynamic(() => import('ui/data-entry/Submit'))

const questionComponents = {
  openAnswer: OpenAnswer,
  multipleOpenAnswers: MultipleOpenAnswers,
  scale: RatingScale,
  multipleChoice: MultipleChoice,
  singleChoice: SingleChoice,
  sectionTitle: SectionTitle,
  richText: RichText,
  // fillInTheBlank: null,
}

const noQuestionNumbers = ['richText', 'sectionTitle']

export default function Questionnaire({
  courseSettings,
  courseStatus,
  providerCountries,
  questionnaire,
  questionnaireStatus,
}) {
  const [submitting, setSubmitting] = useState()
  const goToTop = useGoToTop()
  const router = useRouter()

  const submitMutation = useSubmitQuestionnaire(
    courseStatus,
    questionnaire,
    'submit'
  )
  const saveMutation = useSubmitQuestionnaire(
    courseStatus,
    questionnaire,
    'save'
  )

  useEffect(() => {
    setSubmitting(false)
  }, [router.asPath])

  const onSubmit = useCallback(
    data => {
      const { selectedRegion, ...formData } = data

      if (formData.requestSupervision && providerCountries?.length) {
        data = {
          ...formData,
          country: selectedRegion?.country?._id,
          regionId: selectedRegion?.regionId,
          zip: selectedRegion?.zip,
          city: selectedRegion?.city,
        }
      }

      // Add region data if supervision was requested and providerCountries are available
      let regionData = {}
      if (formData.requestSupervision && providerCountries?.length) {
        regionData = {
          countryId: selectedRegion?.country?._id,
          regionId: selectedRegion?.regionId,
          zip: selectedRegion?.zip,
          city: selectedRegion?.city,
        }
      }

      setSubmitting(true)
      saveMutation.reset()
      submitMutation.mutate(
        {
          action: 'submit',
          questionnaire: questionnaire._id,
          ...formData,
          ...regionData,
        },
        {
          onSuccess: ({ courseCompleted }) => {
            // TODO: invalidate query for course navigation / course pagination
            // setSubmitting(false)
            router.push({
              pathname: router.asPath.split('?')[0],
              query: {
                s: router.query.s,
                submitted: true,
                ...(courseCompleted && { completed: true }),
              },
            })
          },
          onError: () => {
            setSubmitting(false)
            goToTop('smooth')
          },
        }
      )
    },
    [
      goToTop,
      providerCountries?.length,
      questionnaire._id,
      router,
      saveMutation,
      submitMutation,
    ]
  )

  const onSave = useCallback(
    formData => {
      submitMutation.reset()
      saveMutation.mutate(
        {
          action: 'save',
          questionnaire: questionnaire._id,
          ...formData,
        },
        {
          onSuccess: () => {},
          onError: () => {},
        }
      )
    },
    [questionnaire._id, saveMutation, submitMutation]
  )

  let questionNumber = 0

  return (
    <div className="space-y-12">
      <FormFeedback
        courseStatus={courseStatus}
        questionnaire={questionnaire}
        questionnaireStatus={questionnaireStatus}
        submitMutation={submitMutation}
      />
      <Form
        id={`questionnaire-${questionnaire._id}`}
        className={'w-full'}
        data={questionnaireStatus}
        onSubmit={onSubmit}
        disabled={submitting || saveMutation.isLoading}
        resetOnSubmit={false}
      >
        <div className="space-y-16">
          <div className="space-y-8 md:space-y-20">
            {questionnaire?.questions
              ?.filter(question => question.enabled)
              ?.filter(question => !!questionComponents[question.type])
              .map((question, key) => {
                const Question = questionComponents[question.type]

                const { questionNumbering } = questionnaire

                // Don't increase question number for certain question types
                if (
                  questionNumbering &&
                  !noQuestionNumbers.includes(question.type)
                ) {
                  questionNumber++
                }

                return (
                  <Question
                    key={key}
                    question={question}
                    questionNumber={questionNumbering ? questionNumber : null}
                    questionnaireStatus={questionnaireStatus}
                    name={`answers.${question._id}`}
                  />
                )
              })}
          </div>
          {courseStatus && !questionnaireStatus?.submittedAt && (
            <div className="space-y-12">
              {courseStatus?.mode === 'autonomous' &&
                courseSettings?.supervisedCourse && (
                  <RequestSupervision
                    name="requestSupervision"
                    providerCountries={providerCountries}
                  />
                )}
              <div className="flex flex-col md:flex-row items-center gap-4 md:gap-2">
                <SubmitQuestionnaireButton
                  disabled={submitting || saveMutation.isLoading}
                  submitting={submitting}
                />
                <SaveQuestionnaireButton
                  onSave={onSave}
                  disabled={submitting || saveMutation.isLoading}
                  saving={saveMutation.isLoading}
                  error={saveMutation.error?.data}
                />
              </div>
            </div>
          )}
        </div>
      </Form>
    </div>
  )
}

Questionnaire.propTypes = {
  courseSettings: PropTypes.object,
  courseStatus: PropTypes.object,
  providerCountries: PropTypes.array,
  questionnaire: PropTypes.object,
  questionnaireStatus: PropTypes.object,
}

function SubmitQuestionnaireButton({ disabled, submitting }) {
  const { t } = useTranslation('courses')
  const { formState, watch } = useFormContext()
  const { isValid } = formState

  const selectedRegion = watch('selectedRegion')
  const requestSupervision = watch('requestSupervision')

  const validRegion = useMemo(() => {
    if (!requestSupervision) return true
    return selectedRegion
      ? selectedRegion?.country
        ? selectedRegion.country.regionType === 'region'
          ? selectedRegion.regionId
          : selectedRegion.zip && selectedRegion.city
        : false
      : false
  }, [selectedRegion, requestSupervision])

  return (
    <Submit
      label={t('submitAnswers')}
      disabled={!validRegion || !isValid || submitting || disabled}
      icon={submitting ? 'spinner-third' : ''}
      iconClass="animate-spin"
    />
  )
}

function SaveQuestionnaireButton({ onSave, saving, disabled, error }) {
  const { t } = useTranslation('courses')
  const { getValues } = useFormContext()

  const handleSave = useCallback(() => {
    if (!saving && !disabled) {
      const data = getValues()
      onSave(data)
    }
  }, [disabled, getValues, onSave, saving])

  return (
    <Button
      label={t('saveAnswers')}
      variant="tertiary"
      onClick={handleSave}
      icon={saving ? 'spinner-third' : error ? 'triangle-exclamation' : null}
      iconClass={saving ? 'animate-spin' : error ? 'text-danger-600' : ''}
      disabled={disabled}
    />
  )
}

function FormFeedback({ questionnaireStatus, questionnaire, submitMutation }) {
  const { t } = useTranslation('courses')
  const locale = useDatetimeLocale()
  const router = useRouter()

  const error = submitMutation.error
  const showConfirmationMessage = questionnaire?.showConfirmationMessage ?? true // Set default value to true if not provided
  const submitConfirmation =
    questionnaireStatus && router.query.submitted === 'true'
  const questionnaireSubmitted = !!questionnaireStatus?.submittedAt
  const questionnaireCorrected = !!questionnaireStatus?.correctedAt
  const pendingReview =
    questionnaireStatus?.reviewRequested && // submitted in supervised mode
    questionnaireSubmitted &&
    !questionnaireCorrected
  const _questionnaireRequiresReview = questionnaire?.requiresReview

  // Show error message
  if (error) {
    return (
      <Alert className="text-center" type="danger">
        {error?.data ? error.data : t('questionnaireSubmittedError')}
      </Alert>
    )
  }

  // Don't show message if questionnaire is not submitted or confirmation message is disabled
  if (!questionnaireSubmitted || !showConfirmationMessage) return null

  return (
    <Alert className="text-center" type="success">
      <div className="space-y-4">
        {submitConfirmation && (
          <Icon name="circle-check" className="text-6xl text-success-500" />
        )}
        <div>
          {submitConfirmation
            ? t('questionnaireSubmittedSuccess') // You answers have been successfully submitted
            : t('questionnaireSubmittedOn', {
                // You have submitted the questionnaire on
                date: formatDate(questionnaireStatus.submittedAt, 'PPP', {
                  locale,
                }),
              })}
        </div>
        {questionnaireCorrected ? (
          <div>
            {t('questionnaireReviewedDescription')}
            {/* Your answers have been reviewed. Check out the advisor notes below. */}
          </div>
        ) : null}
        {pendingReview && (
          <div>
            <div>
              {
                submitConfirmation
                  ? t('questionnairePendingReviewSuccess') // Our team will review your answers.
                  : t('questionnairePendingReview') // Our team is currently reviewing your answers.
              }
            </div>
            {_questionnaireRequiresReview ? (
              <div className="space-y-4">
                <div>{t('questionnaireWaitingForReview')}</div>
                <div>{t('questionnaireWaitingForReviewThankYou')}</div>
                {/*
                    You'll be able to continue the course once your answers have been reviewed.
                    Thank you for your patience.
                  */}
              </div>
            ) : (
              <div>
                {t('questionnairePendingReviewNotified')}
                {/* You'll be notified once your answers have been reviewed. */}
              </div>
            )}
          </div>
        )}
      </div>
    </Alert>
  )
}
