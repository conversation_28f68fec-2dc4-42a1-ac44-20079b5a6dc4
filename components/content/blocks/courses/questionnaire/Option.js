import PropTypes from 'prop-types'
import React from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Tooltip = dynamic(() => import('ui/feedback/Tooltip'))

const icons = {
  checkbox: {
    selected: 'square-xmark',
    notSelected: 'square',
  },
  radio: {
    selected: 'circle-dot',
    notSelected: 'circle',
  },
}

export default function Option({ option, selected, type = 'checkbox' }) {
  const { t } = useTranslation('courses')
  const { label, solution } = option

  const neutralAnswer = solution === 'neutral'

  let correctAnswer = false

  // Checkbox settings
  if (type === 'checkbox') {
    correctAnswer =
      (solution === 'correct' && selected) ||
      (solution === 'incorrect' && !selected)
  }
  // Radio button settings
  else {
    correctAnswer = solution === 'correct' && selected
  }

  return (
    <div
      className={`px-5 py-4 rounded-lg flex space-x-2 justify-between items-center font-semibold border hover:border-gray-300 ${
        selected
          ? 'xx-bg-secondary-300 xx-border-secondary-400 xx-bg-opacity-50'
          : ''
      } ${solution === 'correct' ? 'border-success-300 hover:border-success-400 bg-success-50' : ''}`}
    >
      <div className="flex space-x-2">
        <Icon
          name={selected ? icons[type].selected : icons[type].notSelected}
          className={
            selected
              ? neutralAnswer
                ? 'text-secondary-500'
                : correctAnswer
                  ? 'text-success-600'
                  : 'text-danger-600'
              : (selected && !correctAnswer) ||
                  (type === 'checkbox' && !selected && solution === 'correct')
                ? 'text-danger-600'
                : 'text-gray-300'
          }
          size={24}
        />
        <div>{label}</div>
      </div>
      {!neutralAnswer &&
        ((type === 'checkbox' && selected) ||
          (type === 'checkbox' && !selected && !correctAnswer) ||
          (type === 'radio' && selected)) && (
          <Tooltip
            className="cursor-pointer"
            content={
              correctAnswer ? t('yourAnswerCorrect') : t('yourAnswerWrong')
            }
            placement="top"
            size="sm"
          >
            <Icon
              name={correctAnswer ? 'check' : 'times'}
              className={correctAnswer ? 'text-success-600' : 'text-danger-600'}
              size={20}
            />
          </Tooltip>
        )}
      {!neutralAnswer &&
        solution === 'correct' &&
        type === 'radio' &&
        !selected && (
          <div className="flex items-center gap-2 font-medium">
            <span className="text-xl pt-1">👈</span>{' '}
            <span className="mt-1">{t('correctAnswer')}</span>
          </div>
        )}
    </div>
  )
}

Option.propTypes = {
  option: PropTypes.object,
  selected: PropTypes.bool,
  type: PropTypes.oneOf(['checkbox', 'radio']),
}
