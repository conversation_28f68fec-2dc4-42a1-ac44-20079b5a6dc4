import PropTypes from 'prop-types'
import React, { useCallback, useState } from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import RegionPicker from '../shared/RegionPicker'
import { useFormContext } from 'react-hook-form'

const Checkbox = dynamic(() => import('ui/data-entry/Checkbox'))
const Text = dynamic(() => import('ui/typography/Text'))

export default function RequestSupervision({ name, providerCountries }) {
  const { t } = useTranslation('courses')
  const [showRegionPicker, setShowRegionPicker] = useState(false)
  const { setValue } = useFormContext()

  const handleCheckboxChange = useCallback(
    checked => {
      if (providerCountries?.length) {
        setShowRegionPicker(checked)
        if (!checked) {
          setValue('selectedRegion', undefined)
        }
      }
    },
    [setValue, providerCountries]
  )

  const handleRegionChange = useCallback(
    ({ country, regionId, zip, city }) => {
      setValue(
        'selectedRegion',
        { country, regionId, zip, city },
        { shouldDirty: true }
      )
    },
    [setValue]
  )

  return (
    <div className="space-y-8">
      <div className="border rounded-2xl p-10 flex flex-col items-center justify-center space-y-6 bg-gradient-to-b from-gray-100 to-gray-50">
        <Text align="center">{t('requestionSupervisionDescription')}</Text>
        <Checkbox
          className="scale-110"
          label={t('requestSupervisionConfirm')}
          name={name}
          onChange={handleCheckboxChange}
        />
        {showRegionPicker && providerCountries?.length && (
          <RegionPicker
            providerCountries={providerCountries}
            onChange={handleRegionChange}
          />
        )}
      </div>
    </div>
  )
}

RequestSupervision.propTypes = {
  name: PropTypes.string,
  providerCountries: PropTypes.array,
}
