import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'
import { isDocEmpty } from 'ui/typography/RichText'

const Icon = dynamic(() => import('ui/icons/Icon'))
const RichText = dynamic(() => import('ui/typography/RichText'))

// const styles = {
//   correct: {
//     container: 'bg-success-100 border-success-300',
//     icon: 'bg-success-300 text-success-700',
//   },
//   // correct: {
//   //   container: 'bg-secondary-200 border-secondary-300',
//   //   icon: 'bg-secondary-300 text-secondary-700',
//   // },
//   neutral: {
//     container: 'bg-gray-100 border-gray-300',
//     icon: 'bg-gray-300 text-gray-700',
//   },
//   info: {
//     container: 'bg-primary-50 border-primary-100',
//     icon: 'bg-primary-100 text-primary-500',
//   },
//   wrong: {
//     container: 'bg-danger-100 xx-text-danger-600 border-danger-300',
//     icon: 'bg-danger-300 text-danger-700',
//   },
// }

const styles = {
  correct: {
    container: 'bg-primary-50 border-primary-100',
    icon: 'bg-primary-100 text-primary-500',
  },
  neutral: {
    container: 'bg-primary-50 border-primary-100',
    icon: 'bg-primary-100 text-primary-500',
  },
  info: {
    container: 'bg-primary-50 border-primary-100',
    icon: 'bg-primary-100 text-primary-500',
  },
  wrong: {
    container: 'bg-primary-50 border-primary-100',
    icon: 'bg-primary-100 text-primary-500',
  },
}

export default function AnswerNotes({
  advisor,
  content,
  showIcon = true,
  text,
  type = 'automatic',
  variant = 'neutral',
}) {
  if (text) text = text.trim()
  if (!text && isDocEmpty(content)) return null

  const advisorName = advisor
    ? `${advisor.firstName || ''} ${advisor.lastName || ''}`.trim()
    : ''

  const containerClass = styles[variant]?.container || ''
  const iconClass = styles[variant]?.icon || ''

  return (
    <div className="flex">
      {showIcon && (
        <div
          className={`w-8 h-8 shrink-0 rounded-full rounded-tr-none rounded-r-none flex items-center justify-center mt-3 pl-1 text-sm ${iconClass}`}
        >
          <Icon name={type === 'automatic' ? 'info' : 'user'} />
        </div>
      )}
      <div
        className={`p-5 rounded-lg inline-block space-y-1 mr-2 sm:mr-16 relative border ${containerClass}`}
      >
        {advisor && advisorName && (
          <div className="text-sm">{advisorName} wrote:</div>
        )}
        {text && <div className="whitespace-pre-line">{text}</div>}
        {content && <RichText doc={content} className="richtext-small" />}
      </div>
    </div>
  )
}

AnswerNotes.propTypes = {
  advisor: PropTypes.object,
  content: PropTypes.object,
  showIcon: PropTypes.bool,
  text: PropTypes.string,
  type: PropTypes.oneOf(['automatic', 'advisor']),
  variant: PropTypes.oneOf(['correct', 'neutral', 'info', 'wrong']),
}
