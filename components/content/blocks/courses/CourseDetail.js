import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import RichText, { isDocEmpty } from 'ui/typography/RichText'

const Image = dynamic(() => import('ui/data-display/Image'))
const SeparatorLine = dynamic(() => import('ui/data-display/SeparatorLine'))

export default function CourseDetail({
  courseDetail,
  showAbstract,
  showBody,
  showImage,
  showTitle,
  title: pluginTitle,
}) {
  if (!courseDetail) return null

  const { abstract, body, images, title } = courseDetail

  const hasContent =
    (showImage && images?.default) ||
    (showAbstract && abstract) ||
    (showBody && !isDocEmpty(body)) ||
    showTitle

  if (!hasContent) return null

  return (
    <div className="w-full space-y-12">
      {pluginTitle && (
        <h3 className="font-bold uppercase leading-loose text-lg">
          {pluginTitle}
        </h3>
      )}
      {showImage && images?.default && (
        <div className="overflow-hidden">
          <Image
            className="w-full hover:scale-105 duration-500 transition-all"
            file={images.default}
            alt={title}
            imgClass="rounded-lg"
          />
        </div>
      )}
      {showTitle && (
        <div className="space-y-6">
          <h1 className="font-extrabold text-4xl">{title}</h1>
          <SeparatorLine />
        </div>
      )}
      {showAbstract && abstract && (
        <>
          <p className="line-clamp-6 pb-4 font-serif italic leading-relaxed text-gray-700 text-lg lg:pb-0">
            {abstract}
          </p>
          {showBody && !isDocEmpty(body) && (
            <div className="flex justify-start">
              <SeparatorLine variant="gray" />
            </div>
          )}
        </>
      )}
      {showBody && !isDocEmpty(body) && <RichText doc={body} />}
    </div>
  )
}
CourseDetail.propTypes = {
  courseDetail: PropTypes.object,
  showAbstract: PropTypes.bool,
  showBody: PropTypes.bool,
  showImage: PropTypes.bool,
  showTitle: PropTypes.bool,
  title: PropTypes.string,
}
