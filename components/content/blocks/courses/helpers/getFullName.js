const formatKeys = {
  p: 'prefix',
  f: 'firstName',
  m: 'middleName',
  l: 'lastName',
  s: 'suffix',
}

export default function getFullName(person = {}, format = '%p %f %m %l %s') {
  if (typeof person !== 'object' || typeof format !== 'string') return ''

  let output = format

  for (const key of Object.keys(formatKeys)) {
    output = output.replace(`%${key}`, person?.[formatKeys[key]] || '')
  }

  return output.replace(/\s+/g, ' ').trim()
}
