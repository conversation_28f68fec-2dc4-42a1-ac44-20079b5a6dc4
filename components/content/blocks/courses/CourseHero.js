import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { getResponsiveSizes, useImageLoader } from 'ui/data-display/Image'

import CourseActions from './CourseActions'
import { media } from 'utils/media'
import { useMatchMedia } from 'hooks/useMatchMedia'

const Image = dynamic(() => import('ui/data-display/Image'))

const HERO_IMAGE_QUALITY = 80

export default function CourseHero({
  className = '',
  courseDetail,
  courseActions,
  coursesProviderCountries,
  variant,
  id,
  pageData,
}) {
  const { title, images } = courseDetail || {}
  const subtitle = courseDetail?.subtitle || courseDetail?.abstract

  const image = images?.banner || images?.default
  const imageLoader = useImageLoader(image)

  const isMd = useMatchMedia(media.md)

  if (!title) return null

  const { chromeless, site } = pageData || {}
  const { topMenu, hideHeader } = site?.design?.header || {}

  return (
    <div
      id={id}
      className={`relative shadow flex items-center justify-center bg-gray-400 px-8 py-16 md:p-32 darken-bg-image-heavy
      overflow-hidden group md:min-h-[700px] ${variant === 'section' ? '' : 'rounded-lg'} ${className}`}
      style={{
        height: isMd
          ? ''
          : chromeless || hideHeader
            ? '80vh'
            : topMenu
              ? 'calc(80vh - 6rem)'
              : 'calc(80vh - 4rem)',
      }}
    >
      <Image
        className="absolute inset-0 max-h-full min-h-full w-full object-cover object-bottom brightness-50 group-hover:scale-105 duration-500 transition-all"
        file={image}
        alt={title}
        loader={imageLoader}
        quality={HERO_IMAGE_QUALITY}
        priority={true}
        lazy={false}
        sizes={getResponsiveSizes('lg:1180px md:768px 384px')}
      />
      <div className="relative h-full flex flex-col items-center justify-center gap-8 sm:gap-16">
        <div className="relative p-0 w-full">
          <div className="relative h-full flex flex-col items-start justify-start gap-2 sm:gap-4 lg:gap-8">
            <h1 className="text-white text-center text-3xl lg:text-8xl sm:text-6xl font-bold w-full">
              {title}
            </h1>
            <p className="text-white text-center text-xl sm:text-3xl font-normal w-full lg:max-w-5xl mx-auto">
              {subtitle}
            </p>
          </div>
        </div>
        <CourseActions
          courseActions={courseActions}
          coursesProviderCountries={coursesProviderCountries}
        />
      </div>
    </div>
  )
}
CourseHero.propTypes = {
  className: PropTypes.string,
  courseDetail: PropTypes.object,
  courseActions: PropTypes.object,
}
