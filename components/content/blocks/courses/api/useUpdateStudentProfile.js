import { useMutation } from 'react-query'

import { patchFetch } from 'utils/http'

export default function useUpdateStudentProfile(
  studentId,
  cacheKey,
  options = {}
) {
  return useMutation(
    formData =>
      patchFetch(
        '/api/courses/student/profile',
        {
          ...formData,
          id: studentId,
          cacheKey,
        },
        {
          baseURL: '',
        }
      ),
    options
  )
}
