import { useMutation } from 'react-query'

import { deleteFetch } from 'utils/http'

export default function useDeleteStudentAccount(
  studentId,
  cacheKey,
  keepWebUser,
  options = {}
) {
  return useMutation(
    () =>
      deleteFetch(
        '/api/courses/student/account/delete',
        {
          id: studentId,
          cacheKey,
          deleteWebUser: !keepWebUser,
        },
        {
          baseURL: '',
        }
      ),
    options
  )
}
