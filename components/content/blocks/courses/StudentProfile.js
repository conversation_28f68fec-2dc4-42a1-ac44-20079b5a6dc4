import React, { useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'
import EditStudent from './students/EditStudent'
import ViewStudent from './students/ViewStudent'
import getStudentName from './helpers/getStudentName'

const Button = dynamic(() => import('ui/buttons/Button'))
const Header = dynamic(() => import('ui/data-display/Header'))

export default function StudentProfile({
  allowEdit,
  className = '',
  coursesStudentProfile,
}) {
  const { t } = useTranslation('common')
  const [edit, setEdit] = useState(false)

  const { student } = coursesStudentProfile

  const { studentName, username } = getStudentName(student)

  return (
    <div className={`w-full space-y-12 ${className}`}>
      <Header title={studentName || username} />
      {edit ? (
        <EditStudent student={student} onCancel={() => setEdit(false)} />
      ) : (
        <div className="space-y-12">
          <ViewStudent student={student} />
          {allowEdit && (
            <Button label={t('editAccount')} onClick={() => setEdit(true)} />
          )}
        </div>
      )}
    </div>
  )
}
StudentProfile.propTypes = {
  className: PropTypes.string,
  coursesStudentProfile: PropTypes.object,
}
