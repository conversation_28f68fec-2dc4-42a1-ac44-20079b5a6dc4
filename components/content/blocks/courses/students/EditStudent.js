import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'

const Button = dynamic(() => import('ui/buttons/Button'))
const Form = dynamic(() => import('ui/data-entry/Form'))
const Input = dynamic(() => import('ui/data-entry/Input'))
const Submit = dynamic(() => import('ui/data-entry/Submit'))
// const DatePicker = dynamic(() =>
//   import('ui/data-entry/DateTime').then(m => m.DatePicker)
// )
// const Popover = dynamic(() => import('ui/feedback/Popover'))
// import Email from 'ui/data-entry/EmailField'

import { usePageContext } from 'components/PageProvider'
import useUpdateStudentProfile from '../api/useUpdateStudentProfile'

export default function EditStudent({ student, onCancel }) {
  const { t } = useTranslation(['common', 'courses'])
  const router = useRouter()
  const pageContext = usePageContext()
  const [persistedLoading, setPersistedLoading] = useState(false)

  const { mutate } = useUpdateStudentProfile(student._id, pageContext?.cacheKey)

  const onSubmit = useCallback(
    ({ profile }) => {
      setPersistedLoading(true)
      mutate(
        { profile },
        {
          onSuccess: () => {
            // NOTE: The page needs to reload since we are refreshing data in __NEXT_DATA__
            router.reload()
          },
          onError: () => {
            setPersistedLoading(false)
          },
        }
      )
    },
    [mutate, router]
  )

  return (
    <Form data={student} onSubmit={onSubmit}>
      <div className="-mt-8 text-gray-600">{student.profile.email}</div>
      <div className="space-y-8">
        {/* <Input label={t('username')} name="profile.username" required /> */}
        {/* <Email label={t('email')} name="profile.email" required /> */}
        <div className="flex flex-col md:flex-row gap-8 md:gap-4">
          <Input
            className="flex-1"
            label={t('firstName')}
            name="profile.firstName"
          />
          <Input
            className="flex-1"
            label={t('lastName')}
            name="profile.lastName"
          />
        </div>
        <Input label={t('street')} name="profile.address.street" />
        <Input
          label={t('additionalAddress')}
          name="profile.address.additionalAddress"
        />
        <div className="flex flex-col md:flex-row gap-8 md:gap-4">
          <Input
            className="w-full md:w-1/3"
            label={t('zip')}
            name="profile.address.zip"
            required
          />
          <Input
            className="w-full md:w-2/3"
            label={t('city')}
            name="profile.address.city"
            required
          />
        </div>
        {/* <Popover
          trigger={<Button variant="hollow" icon="calendar" size="sm" />}
        >
          {({ close }) => (
            <div className="flex flex-col space-y-5 p-4">
              <DatePicker
                date={calendarDate}
                onChange={date => setCalendarDate(date)}
                name="birthday"
              />
            </div>
          )}
        </Popover> */}
        {/* <div className="flex flex-col md:flex-row gap-8 md:gap-4">
          <Input className="flex-1" label={t('state')} name="address.state" />
          <Input
            className="flex-1"
            label={t('country')}
            name="address.country"
          />
        </div> */}
        <div className="flex gap-4 pt-6">
          <Submit label={t('save')} loading={persistedLoading} />
          <Button label={t('cancel')} variant="tertiary" onClick={onCancel} />
        </div>
      </div>
    </Form>
  )
}
EditStudent.propTypes = {
  student: PropTypes.object,
  onCancel: PropTypes.func,
}
