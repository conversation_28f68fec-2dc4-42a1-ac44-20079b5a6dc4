import React from 'react'
import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import { formatDate, useDatetimeLocale } from 'utils/datetime'

const Address = dynamic(() => import('ui/data-display/Address'))
const Heading = dynamic(() => import('ui/typography/Heading'))
const LabelIcon = dynamic(() => import('ui/data-display/LabelIcon'))

export default function ViewStudent({ student }) {
  const { t } = useTranslation('common')
  const locale = useDatetimeLocale()

  if (!student) return null

  const { createdAt, profile } = student
  const { email, address, username, birthday } = profile

  return (
    <section className="space-y-12">
      <div className="space-y-2">
        <LabelIcon icon="user" label={username} />
        <LabelIcon
          icon="clock"
          label={t('joinedOn', {
            date: formatDate(createdAt, 'PPP', { locale }),
          })}
        />
        {birthday && <LabelIcon icon="birthday-cake" label={birthday} />}
      </div>
      <div className="space-y-4">
        <Heading
          as="h2"
          fontWeight="semibold"
          title={t('address')}
          textSize={{ xs: 'xl', md: '2xl' }}
        />
        <Address
          {...address}
          country={address?.countryName}
          email={email}
          showEmailLink
        />
      </div>
    </section>
  )
}

ViewStudent.propTypes = {
  student: PropTypes.object,
}
