import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'

const Poster = dynamic(() => import('ui/data-display/Poster'))

export default function CoursePoster({
  course,
  enableTitle,
  enableAbstract,
  enableCallToAction,
  callToActionLabel,
}) {
  return (
    <Poster
      enableAnimation
      backgroundImage={course.images?.poster ? null : course.images?.default}
      image={course.images?.poster || course.images?.default}
      title={course.title}
      url={course.url}
      enableDescription={enableAbstract}
      enableTitle={enableTitle}
      description={course.abstract}
      enableCallToAction={enableCallToAction}
      callToActionLabel={callToActionLabel}
    />
  )
}

CoursePoster.propTypes = {
  callToActionLabel: PropTypes.string,
  course: PropTypes.object,
  enableCallToAction: PropTypes.bool,
  enableTitle: PropTypes.bool,
  enableAbstract: PropTypes.bool,
}
