import PropTypes from 'prop-types'
import React from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { formatDate } from 'utils/datetime'

const ProgressBar = dynamic(() => import('ui/feedback/ProgressBar'))

export default function CourseProgressBar({ className = '', courseStatus }) {
  const { t } = useTranslation('courses')

  const { finishedAt, startedLessons, totalLessons } = courseStatus || {}

  return (
    <div className={`text-gray-600 space-y-1 ${className}`}>
      <div className="flex justify-between items-center px-1">
        <div>
          {!finishedAt
            ? ''
            : t('completedOn', { date: formatDate(finishedAt, 'P') })}
        </div>
        <div>{`${t('lesson')} ${startedLessons} / ${totalLessons}`}</div>
      </div>
      <ProgressBar max={totalLessons} value={startedLessons} />
    </div>
  )
}

CourseProgressBar.propTypes = {
  className: PropTypes.string,
  courseStatus: PropTypes.object,
}
