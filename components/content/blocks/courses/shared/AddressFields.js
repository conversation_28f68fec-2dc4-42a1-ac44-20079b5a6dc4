import React, { useCallback, useEffect, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'
import useDebounce from 'hooks/useDebounce'

const Input = dynamic(() =>
  import('ui/data-entry/Input').then(m => m.InputField)
)

export default function AddressFields({ country, onChange }) {
  const { t } = useTranslation(['courses', 'common'])

  const [selectedZipcode, setSelectedZipcode] = useState('')
  const [selectedCity, setSelectedCity] = useState('')

  const debouncedZipcode = useDebounce(selectedZipcode, 300)
  const debouncedCity = useDebounce(selectedCity, 300)

  const { regionType } = country || {}

  // Reset values when country changes
  useEffect(() => {
    if (country) {
      setSelectedZipcode('')
      setSelectedCity('')
      onChange && onChange({ zip: '', city: '' })
    }
  }, [country, onChange])

  // Change address value
  const handleAddressChange = useCallback((field, value) => {
    if (field === 'zip') setSelectedZipcode(value)
    if (field === 'city') setSelectedCity(value)
  }, [])
  useEffect(() => {
    if (regionType === 'postalCode') {
      onChange && onChange({ zip: debouncedZipcode, city: debouncedCity })
    }
  }, [debouncedCity, debouncedZipcode, onChange, regionType])

  if (regionType !== 'postalCode') return null

  return (
    <div className="flex flex-col lg:flex-row gap-3 lg:gap-4">
      <div className="w-full lg:w-1/3">
        <Input
          name="zip"
          placeholder={t('common:zip')}
          onChange={e => handleAddressChange('zip', e.target.value)}
          value={selectedZipcode}
        />
      </div>
      <div className="w-full lg:w-2/3">
        <Input
          name="city"
          placeholder={t('common:city')}
          onChange={e => handleAddressChange('city', e.target.value)}
          value={selectedCity}
        />
      </div>
    </div>
  )
}

AddressFields.propTypes = {
  country: PropTypes.object,
  onChange: PropTypes.func,
}
