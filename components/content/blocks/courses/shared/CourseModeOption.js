import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Button = dynamic(() => import('ui/buttons/Button'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Text = dynamic(() => import('ui/typography/Text'))

export default function CourseModeOption({
  buttonLabel,
  description,
  disabled,
  extra,
  icon,
  label,
  onClick,
}) {
  const [loading, setLoading] = useState(false)

  const handleOnClick = useCallback(() => {
    setLoading(true)
    onClick()
  }, [onClick])

  return (
    <div className="flex items-start space-x-4 py-6">
      <div className="w-28 text-4xl">
        <Icon name={icon} />
      </div>
      <div className="space-y-4">
        <div className="space-y-3">
          {label && (
            <h4 className="font-semibold leading-none text-lg">{label}</h4>
          )}
          <Text text={description} />
        </div>
        {extra}
        <Button
          label={buttonLabel}
          size="sm"
          onClick={handleOnClick}
          icon={loading ? 'spinner-third' : ''}
          iconClass="animate-spin"
          disabled={disabled}
        />
      </div>
    </div>
  )
}

CourseModeOption.propTypes = {
  prop: PropTypes.string,
  buttonLabel: PropTypes.string,
  description: PropTypes.string,
  disabled: PropTypes.bool,
  extra: PropTypes.node,
  icon: PropTypes.string,
  label: PropTypes.string,
  onClick: PropTypes.func,
}
