import React, { useCallback, useEffect, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const Select = dynamic(() => import('ui/data-entry/Select').then(m => m.Select))

export default function RegionField({ country, onChange }) {
  const { t } = useTranslation(['courses', 'common'])
  const [selectedRegionId, setSelectedRegionId] = useState()

  const { regionType, regions } = country || {}

  // Reset region when country changes
  useEffect(() => {
    if (country) {
      setSelectedRegionId(null)
      onChange && onChange({ regionId: null })
    }
  }, [country, onChange])

  // Change region
  const handleRegionChange = useCallback(
    ({ target }) => {
      const regionId = target.value
      setSelectedRegionId(regionId)
      onChange && onChange({ regionId })
    },
    [onChange]
  )

  if (regionType !== 'region') return null

  return (
    <Select
      onChange={handleRegionChange}
      options={regions.map(region => ({
        label: region.config.name,
        value: region._id,
      }))}
      value={selectedRegionId}
      placeholder={t('common:select')}
    />
  )
}

RegionField.propTypes = {
  country: PropTypes.object,
  onChange: PropTypes.func,
}
