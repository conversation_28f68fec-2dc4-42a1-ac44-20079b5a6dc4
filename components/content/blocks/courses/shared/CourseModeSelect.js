import React, { useCallback, useMemo, useState } from 'react'
import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'

import CourseModeOption from './CourseModeOption'
import RegionPicker from './RegionPicker'

export default function CourseModeSelect({
  autonomous,
  supervised,
  onStart,
  providerCountries,
}) {
  const { t } = useTranslation('courses')
  const [selectedMode, setSelectedMode] = useState('')
  const [selectedRegion, setSelectedRegion] = useState({
    country: null,
    regionId: null,
    zip: null,
    city: null,
  })

  const handleChangeRegion = useCallback(({ country, regionId, zip, city }) => {
    setSelectedRegion({ country, regionId, zip, city })
  }, [])

  const handleStart = useCallback(
    mode => {
      setSelectedMode(mode)
      onStart({
        mode,
        countryId: selectedRegion?.country?._id,
        regionId: selectedRegion?.regionId,
        zip: selectedRegion?.zip,
        city: selectedRegion?.city,
      })
    },
    [onStart, selectedRegion]
  )

  const validRegion = useMemo(
    () =>
      providerCountries?.length
        ? selectedRegion?.country
          ? selectedRegion.country.regionType === 'region'
            ? selectedRegion.regionId
            : selectedRegion.zip && selectedRegion.city
          : false
        : true,
    [
      providerCountries?.length,
      selectedRegion.city,
      selectedRegion.zip,
      selectedRegion.country,
      selectedRegion.regionId,
    ]
  )

  return (
    <div className="space-y-4 divide-y">
      {autonomous && (
        <CourseModeOption
          icon="user-light"
          label={supervised && t('courseModeAlone')}
          description={
            supervised
              ? t('courseModeAloneDescriptionOptions')
              : t('courseModeAloneDescriptionSingle')
          }
          buttonLabel={t('courseModeAloneCallToAction')}
          onClick={() => handleStart('autonomous')}
          disabled={selectedMode === 'supervised'}
        />
      )}
      {supervised && (
        <CourseModeOption
          icon="user-group-light"
          label={autonomous && t('courseModeWithAdvisor')}
          description={
            autonomous
              ? t('courseModeWithAdvisorDescriptionOptions')
              : t('courseModeWithAdvisorDescriptionSingle')
          }
          extra={
            <RegionPicker
              className="pb-2"
              providerCountries={providerCountries}
              onChange={handleChangeRegion}
            />
          }
          buttonLabel={t('courseModeWithAdvisorCallToAction')}
          onClick={() => handleStart('supervised')}
          disabled={selectedMode === 'autonomous' || !validRegion}
        />
      )}
    </div>
  )
}

CourseModeSelect.propTypes = {
  autonomous: PropTypes.bool,
  supervised: PropTypes.bool,
  onStart: PropTypes.func,
  providerCountries: PropTypes.array,
  student: PropTypes.object,
}
