import React, { useCallback, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'
import AddressFields from './AddressFields'
import Text from 'ui/typography/Text'
import Tooltip from 'ui/feedback/Tooltip'

const Select = dynamic(() => import('ui/data-entry/Select').then(m => m.Select))
const RegionField = dynamic(() => import('./RegionField'))

export default function RegionPicker({
  className = '',
  onChange,
  providerCountries,
}) {
  const { t } = useTranslation(['courses', 'common'])
  const [selectedCountry, setSelectedCountry] = useState()

  const onCountryChange = useCallback(
    ({ target }) => {
      const selectCountryId = target.value
      const selectedCountry = providerCountries.find(
        country => country._id === selectCountryId
      )
      setSelectedCountry(selectedCountry)
    },
    [providerCountries]
  )

  const handleOnRegionChange = useCallback(
    ({ regionId, zip, city }) => {
      onChange &&
        onChange({
          country: selectedCountry,
          regionId,
          zip,
          city,
        })
    },
    [onChange, selectedCountry]
  )

  if (!providerCountries?.length) return null

  return (
    <div className={`flex flex-col gap-4 ${className}`}>
      <div className="flex flex-col lg:flex-row gap-4 items-top">
        <div className="lg:shrink-0">
          <Select
            onChange={onCountryChange}
            options={providerCountries.map(country => ({
              label: country.countryName,
              value: country._id,
            }))}
            value={selectedCountry?._id}
            placeholder={t('common:select')}
          />
        </div>
        {selectedCountry?.regionType === 'region' && (
          <div className="lg:grow">
            <RegionField
              country={selectedCountry}
              onChange={handleOnRegionChange}
            />
          </div>
        )}
      </div>
      {selectedCountry?.regionType === 'postalCode' && (
        <AddressFields
          country={selectedCountry}
          onChange={handleOnRegionChange}
        />
      )}
      <Tooltip
        content={t('regionPickerTooltip')}
        placement="top"
        size="sm"
        className="inline-block cursor-pointer"
      >
        <Text
          textSize={{ sm: 'sm' }}
          color="primary-600"
          className="ml-2 border-b border-dashed border-gray-300 inline-block"
        >
          {t('regionPickerInfo')}
        </Text>
      </Tooltip>
    </div>
  )
}
RegionPicker.propTypes = {
  providerCountries: PropTypes.array,
  className: PropTypes.string,
  onChange: PropTypes.func,
}
