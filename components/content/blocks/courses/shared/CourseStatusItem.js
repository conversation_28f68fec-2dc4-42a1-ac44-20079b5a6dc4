import PropTypes from 'prop-types'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { media } from 'utils/media'

const ConditionalLink = dynamic(() =>
  import('ui/navigation/Link').then(m => m.ConditionalLink)
)
const Button = dynamic(() => import('ui/buttons/Button'))
const Heading = dynamic(() => import('ui/typography/Heading'))
const Image = dynamic(() => import('ui/data-display/Image'))

const CourseMode = dynamic(() => import('./CourseMode'))
const CourseProgressBar = dynamic(() => import('./CourseProgressBar'))

export default function CourseStatusItem({
  course,
  courseStatus,
  coursePreviewMode,
  providerCountries,
}) {
  const { t } = useTranslation('courses')
  const { activeContentUrl, finishedAt } = courseStatus
  const { images, title, url } = course

  const isMd = useMatchMedia(media.md)

  return (
    <div className="flex flex-col md:flex-row border bg-white rounded-lg overflow-hidden">
      {/* Course Image */}
      <div className="w-full md:w-80 group shrink-0 overflow-hidden">
        <ConditionalLink className="cursor-pointer" to={url} condition={url}>
          <Image
            className="group-hover:scale-110 duration-300 transition-all bg-gray-200"
            alt={title}
            file={images?.default}
            aspectRatio="16/9"
          />
        </ConditionalLink>
      </div>

      <div className="w-full px-6 py-4 space-y-4 flex flex-col md:flex-row gap-4">
        {/* Content */}
        <div className="w-full space-y-4">
          <ConditionalLink className="cursor-pointer" to={url} condition={url}>
            <Heading textSize={{ md: '2xl' }} as="h2" title={title} />
          </ConditionalLink>

          <div className="max-w-lg space-y-4">
            <CourseProgressBar courseStatus={courseStatus} />
            <div className="flex flex-col md:flex-row gap-4 justify-between items-start md:items-center mx-2">
              <CourseMode
                course={course}
                courseStatus={courseStatus}
                hideSwitch={coursePreviewMode}
                providerCountries={providerCountries}
              />
              {isMd && <UnsubscribeLink courseStatus={courseStatus} />}
            </div>
          </div>
        </div>

        {/* Button */}
        <div className="md:self-center space-y-4">
          {!finishedAt && courseStatus.mode !== 'correspondence' && (
            <>
              {!coursePreviewMode ? (
                <Button
                  className="group hover:bg-secondary-400"
                  label={t('courseAction-continue-short')}
                  variant="alt"
                  icon="chevron-right"
                  iconClass="opacity-0 -ml-4 group-hover:-ml-0 group-hover:opacity-80 transition-all duration-300"
                  iconPosition="right"
                  url={activeContentUrl}
                />
              ) : (
                <Button
                  label={t('courseAction-continue-short')}
                  variant="alt"
                  url={null}
                  disabled
                />
              )}
            </>
          )}
          {!isMd && <UnsubscribeLink courseStatus={courseStatus} />}
        </div>
      </div>
    </div>
  )
}

CourseStatusItem.propTypes = {
  course: PropTypes.object,
  courseStatus: PropTypes.object,
  disabled: PropTypes.bool,
  providerCountries: PropTypes.array,
}

// eslint-disable-next-line no-unused-vars
function UnsubscribeLink({ courseStatus }) {
  // eslint-disable-next-line no-unused-vars
  const { t } = useTranslation('courses')

  return null

  // if (courseStatus.finishedAt) return null

  // return (
  //   <div className="text-danger-500 font-medium hover:underline hover:underline-offset-4 text-sm cursor-pointer whitespace-nowrap">
  //     {t('unsubscribeFromCourse')}
  //   </div>
  // )
}
