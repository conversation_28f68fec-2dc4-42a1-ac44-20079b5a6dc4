import { useQuery } from 'react-query'
import { getFetch } from 'utils/http'

export default function useChatMessages({
  chatId,
  search = '',
  limit = 10,
  page = 1,
}) {
  const url = chatId ? `/api/courses/chats/${chatId}/messages` : null

  const { data, error, isLoading, isFetching } = useQuery(
    [
      'courses-messages',
      {
        chatId,
        search,
        limit,
        page,
      },
    ],
    () =>
      getFetch(
        url,
        {
          limit,
          page,
          search,
        },
        {
          baseURL: '',
        }
      )
  )

  return {
    messages: data?.items,
    count: data?.count,
    error,
    loading: isLoading,
    fetching: isFetching,
  }
}
