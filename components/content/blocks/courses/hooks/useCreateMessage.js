import { useMutation, useQueryClient } from 'react-query'
import { postFetch } from 'utils/http'

export default function useCreateMessage(chatId) {
  const queryClient = useQueryClient()
  return useMutation(
    message =>
      postFetch(
        chatId ? `/api/courses/chats/${chatId}/messages` : null,
        message,
        {
          baseURL: '',
        }
      ),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['courses-messages'])
        queryClient.invalidateQueries(['courses-chats'])
      },
    }
  )
}
