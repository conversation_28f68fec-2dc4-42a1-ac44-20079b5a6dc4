import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'

import Tooltip from 'ui/feedback/Tooltip'

const Button = dynamic(() => import('ui/buttons/Button'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function CoursePagination({ coursePagination, className = '' }) {
  if (!coursePagination) return null
  const { nextPage, previousPage, slides, lesson } = coursePagination

  return (
    <div
      className={`flex w-full select-none flex-row items-center justify-between ${className}`}
    >
      <div className="w-1/2 sm:w-1/5">
        {previousPage && (
          <PaginationButton
            url={previousPage.url}
            locked={previousPage.isLocked}
          />
        )}
      </div>
      <div className="hidden items-center justify-center sm:flex flex-1 gap-2 px-2">
        <PageItem
          page={lesson}
          active={lesson.isActive && !slides.find(s => s.isActive)}
        />
        {slides.map((slide, key) => (
          <PageItem key={key} page={slide} active={slide.isActive} />
        ))}
        {false && (
          <div className="text-center space-y-2">
            <Button
              label="Mark as complete"
              icon="circle-check"
              // icon="lock"
              // icon="times"
              // disabled={true}
              // iconClass="text-sm"
              // className="text-sm font-semibold text-gray-500"
            />
            {false && (
              <div className="text-sm">
                Answer the questions before you can complete this page
              </div>
            )}
          </div>
        )}
      </div>
      <div className="w-1/2 sm:w-1/5 text-right">
        {nextPage && (
          <PaginationButton
            url={nextPage.url}
            locked={nextPage.isLocked}
            isNext
          />
        )}
      </div>
    </div>
  )
}
CoursePagination.propTypes = {
  className: PropTypes.string,
  coursePagination: PropTypes.object,
}

function PaginationButton({ isNext, locked, url }) {
  const { t } = useTranslation('courses')
  const isSm = useMatchMedia(media.sm)

  return (
    <Button
      className={`group hover:bg-secondary-400 ${isSm ? '' : 'w-10 h-10 rounded-full scale-125'}`}
      label={isSm ? t(isNext ? 'common:next' : 'common:back') : ''}
      iconPosition={isNext ? 'right' : 'left'}
      variant="alt"
      icon={locked ? 'lock' : isNext ? 'chevron-right' : 'chevron-left'}
      iconClass={`text-sm ${isSm ? 'opacity-0 -ml-4 group-hover:-ml-0 group-hover:opacity-80 transition-all duration-300' : ''}`}
      disabled={locked}
      url={url}
    />
  )
}
PaginationButton.propTypes = {
  url: PropTypes.string,
  locked: PropTypes.bool,
  isNext: PropTypes.bool,
}

function PageItem({ page, active }) {
  const { url } = page

  return (
    <Tooltip
      className={`flex-1 max-w-[4rem]`}
      content={page.title}
      placement="top"
    >
      <Link className="py-3 block group" to={url}>
        <div
          className={`rounded-full h-2 text-transparent ${
            active
              ? 'bg-primary-200'
              : url
                ? 'bg-gray-200 group-hover:bg-primary-100 transition duration-200'
                : 'bg-gray-100 cursor-not-allowed'
          }`}
        />
      </Link>
    </Tooltip>
  )
}
PageItem.propTypes = {
  page: PropTypes.object,
}
