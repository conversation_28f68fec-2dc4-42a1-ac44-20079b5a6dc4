import PropTypes from 'prop-types'
import React from 'react'

import { useRouter } from 'next/router'
import dynamic from 'next/dynamic'

import useCountUnreadMessages from '../hooks/useCountUnreadMessages'

const Box = dynamic(() => import('ui/data-display/Box'))

const ContactItem = dynamic(() => import('./ContactItem'))

export default function ContactList({ chats, selectedChatId }) {
  const router = useRouter()

  const { unreadMessagesCount } = useCountUnreadMessages()

  return (
    <Box className="p-0 overflow-hidden" innerClass="divide-y">
      {chats?.map((chat, key) => (
        <ContactItem
          key={key}
          chat={chat}
          unreadMessages={unreadMessagesCount?.[chat._id] || 0}
          href={{
            pathname: router.pathname,
            query: {
              ...router.query,
              chat: chat._id,
            },
          }}
          active={selectedChatId === chat._id}
        />
      ))}
    </Box>
  )
}
ContactList.propTypes = {
  chats: PropTypes.array,
  selectedChatId: PropTypes.string,
}
