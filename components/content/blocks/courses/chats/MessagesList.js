import PropTypes from 'prop-types'
import React, { useEffect, useState } from 'react'

import { useRouter } from 'next/router'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import { isEmpty } from 'utils/arrays'

import useChatMessages from '../hooks/useChatMessages'

const Box = dynamic(() => import('ui/data-display/Box'))
const Empty = dynamic(() => import('ui/data-display/Empty'))
const Heading = dynamic(() => import('ui/typography/Heading'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Loading = dynamic(() => import('ui/feedback/Loading'))
const Pagination = dynamic(() => import('ui/navigation/Pagination'))
const Text = dynamic(() => import('ui/typography/Text'))

const AbsenceMessage = dynamic(() => import('../advisors/AbsenceMessage'))
const ChatMessage = dynamic(() => import('./Message'))
const MessageForm = dynamic(() => import('./MessageForm'))

const limit = 15

export default function MessagesList({ chat }) {
  const { t } = useTranslation(['courses', 'common'])
  const router = useRouter()
  const [totalMessages, setTotalMessages] = useState(0)

  const page = router.query.page ? parseInt(router.query.page, 10) : 1

  const { messages, count, loading } = useChatMessages({
    chatId: chat?._id,
    page,
    limit,
  })

  useEffect(() => {
    if (!loading) {
      setTotalMessages(count)
    }
  }, [count, loading])

  return (
    <Box className="p-6 sm:p-12 lg:p-20" innerClass="space-y-6">
      {chat ? (
        <>
          <AbsenceMessage advisor={chat?.advisor} />
          <div className="space-y-4">
            <Heading
              textSize={{ sm: 'sm', lg: 'xl' }}
              as="h2"
              title={t('messagesCount', { count: totalMessages || 0 })}
              capitalized
            />
            {chat?.advisor?.enabled && <MessageForm chatId={chat?._id} />}
          </div>
          <div className="divide-y divide-gray-300 border-t border-gray-300">
            {loading && (
              <Loading label={t('common:loading')} className="my-6" />
            )}
            {isEmpty(messages) && !loading && (
              <div className="pt-6 md:pt-12 space-y-3">
                <Icon
                  name="inbox"
                  className="text-5xl md:text-6xl text-primary-600"
                />
                <Text textSize={{ sm: 'lg' }} align="center">
                  {t('noMessages')}
                </Text>
              </div>
            )}
            {messages?.map((message, key) => (
              <ChatMessage message={message} key={key} />
            ))}
            <Pagination
              itemsLabel={t('shows')}
              total={totalMessages}
              pageSize={limit}
            />
          </div>
        </>
      ) : (
        <Empty
          title={t('noMessagesTitle')}
          description={t('enableMessagesDescription')}
          icon="inbox"
        />
      )}
    </Box>
  )
}
MessagesList.propTypes = {
  chat: PropTypes.object,
}
