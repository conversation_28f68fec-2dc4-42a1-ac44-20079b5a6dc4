import PropTypes from 'prop-types'
import React, { useCallback, useState } from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import useCreateMessage from '../hooks/useCreateMessage'

const Alert = dynamic(() => import('ui/feedback/Alert'))
const Button = dynamic(() => import('ui/buttons/Button'))
const ButtonGroup = dynamic(() => import('ui/buttons/ButtonGroup'))
const Form = dynamic(() => import('ui/data-entry/Form'))
const Heading = dynamic(() => import('ui/typography/Heading'))
const Input = dynamic(() => import('ui/data-entry/Input'))
const Submit = dynamic(() => import('ui/data-entry/Submit'))
const TextArea = dynamic(() => import('ui/data-entry/TextArea'))

export default function MessageForm({ chatId }) {
  const { t } = useTranslation(['courses', 'common'])
  const [viewForm, setViewForm] = useState(false)
  const { mutate, isError, isLoading, error } = useCreateMessage(chatId)

  const handleSubmit = useCallback(
    data =>
      mutate(data, {
        onSuccess: () => setViewForm(false),
      }),
    [mutate]
  )

  // eslint-disable-next-line no-console
  if (error) console.log(error)

  return viewForm ? (
    <div className="py-6 px-12 space-y-8 bg-gray-100 border rounded-lg">
      <Heading as="h4" title={t('yourMessage')} />
      <Form onSubmit={handleSubmit} resetOnSubmit>
        <div className="space-y-4">
          {isError && (
            <Alert
              title={t('sendMessageErrorTitle')}
              message={t('sendErrorMessage')}
              type="danger"
            />
          )}

          <Input name="subject" placeholder={t('subject')} />
          <TextArea
            name="text"
            placeholder={t('writeMessagePlaceholder')}
            required
          />
          <ButtonGroup align="right">
            <Button
              label={t('common:cancel')}
              onClick={() => setViewForm(false)}
              variant="tertiary"
              className={isLoading ? 'hidden' : ''}
            />
            <Submit
              label={t('sendMessage')}
              icon={isLoading ? 'spinner-third' : ''}
              iconClass="animate-spin"
              disabled={isLoading}
            />
          </ButtonGroup>
        </div>
      </Form>
    </div>
  ) : (
    <Button
      label={t('writeMessage')}
      onClick={() => setViewForm(!viewForm)}
      variant="secondary"
      size="sm"
    />
  )
}

MessageForm.propTypes = {
  chatId: PropTypes.string.isRequired,
}
