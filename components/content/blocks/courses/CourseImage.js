import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Image = dynamic(() => import('ui/data-display/Image'))

export default function CourseImage({ courseImage, className = '' }) {
  if (!courseImage) return null
  const { images, title } = courseImage
  if (!images?.default) return null

  return (
    <Image
      className={`w-full ${className}`}
      file={images.default}
      alt={title}
      imgClass="rounded-lg"
    />
  )
}
CourseImage.propTypes = {
  className: PropTypes.string,
  courseImage: PropTypes.object,
}
