import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import Tabs from 'ui/data-display/Tabs'
import { isEmpty } from 'utils/arrays'

const Box = dynamic(() => import('ui/data-display/Box'))
const Empty = dynamic(() => import('ui/data-display/Empty'))

const SeparatorLine = dynamic(() => import('ui/data-display/SeparatorLine'))

const CourseStatusItem = dynamic(() => import('./shared/CourseStatusItem'))

export default function EnrolledCourses({
  className = '',
  coursesEnrolledCourses,
  coursesProviderCountries,
}) {
  const { t } = useTranslation('courses')

  // Return null if student is not logged in
  if (!coursesEnrolledCourses) return null

  const { activeCourses, completedCourses, totalCourses, coursePreviewMode } =
    coursesEnrolledCourses

  return (
    <div className={`w-full space-y-8 ${className}`}>
      {totalCourses > 0 && (
        <div className="space-y-6">
          <h1 className="font-extrabold text-3xl leading-tight">
            {t('myCourses')}
          </h1>
          <SeparatorLine />
        </div>
      )}

      {isEmpty(completedCourses) ? (
        <ActiveCourses
          activeCourses={activeCourses}
          completedCourses={completedCourses}
          coursePreviewMode={coursePreviewMode}
          providerCountries={coursesProviderCountries}
        />
      ) : (
        <Tabs>
          <Tabs.Pane name="activeCourses" title={t('activeCourses')}>
            <ActiveCourses
              activeCourses={activeCourses}
              completedCourses={completedCourses}
              coursePreviewMode={coursePreviewMode}
              providerCountries={coursesProviderCountries}
            />
          </Tabs.Pane>
          <Tabs.Pane name="completedCourses" title={t('completedCourses')}>
            <div className="space-y-4">
              {completedCourses?.map(courseStatus => (
                <CourseStatusItem
                  course={courseStatus.course}
                  courseStatus={courseStatus}
                  coursePreviewMode={coursePreviewMode}
                  key={courseStatus._id}
                />
              ))}
            </div>
          </Tabs.Pane>
        </Tabs>
      )}
    </div>
  )
}
EnrolledCourses.propTypes = {
  className: PropTypes.string,
  coursesEnrolledCourses: PropTypes.object,
}

function ActiveCourses({
  activeCourses,
  completedCourses,
  coursePreviewMode,
  providerCountries,
}) {
  const { t } = useTranslation('courses')

  return !isEmpty(activeCourses) ? (
    <div className="space-y-4">
      {activeCourses?.map(courseStatus => (
        <CourseStatusItem
          course={courseStatus.course}
          courseStatus={courseStatus}
          coursePreviewMode={coursePreviewMode}
          providerCountries={providerCountries}
          key={courseStatus._id}
        />
      ))}
    </div>
  ) : (
    <Box className="p-6 sm:p-12 lg:p-20" innerClass="space-y-6">
      <Empty
        title={
          isEmpty(completedCourses)
            ? t('noCoursesTitle')
            : t('noActiveCoursesTitle')
        }
        description={
          isEmpty(completedCourses)
            ? t('noCoursesDescription', { count: completedCourses.length })
            : t('noActiveCoursesDescription', {
                count: completedCourses.length,
              })
        }
        icon="books"
      />
    </Box>
  )
}
