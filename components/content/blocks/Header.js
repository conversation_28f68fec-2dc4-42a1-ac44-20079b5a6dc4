import React from 'react'
import PropTypes from 'prop-types'

import useWidth from 'ui/helpers/useWidth'

export default function Header({
  className = '',
  id,
  title,
  subtitle,
  kicker,
  width,
}) {
  const widthClasses = useWidth(width)

  return (
    <div className={`py-4 ${widthClasses} ${className}`} id={id}>
      {kicker && (
        <h3 className="font-bold uppercase text-primary-700 text-lg dark:text-secondary-500">
          {kicker}
        </h3>
      )}
      {title && (
        <h1 className="font-extrabold text-gray-800 text-4xl dark:text-gray-200">
          {title}
        </h1>
      )}
      {subtitle && (
        <h2 className="font-semibold text-gray-800 text-xl dark:text-gray-400">
          {subtitle}
        </h2>
      )}
    </div>
  )
}
Header.propTypes = {
  className: PropTypes.string,
  id: PropTypes.string,
  kicker: PropTypes.string,
  subtitle: PropTypes.string,
  title: PropTypes.string,
  width: PropTypes.object,
}
