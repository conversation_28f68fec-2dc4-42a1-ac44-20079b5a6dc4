import { useMemo } from 'react'
import PropTypes from 'prop-types'

import useWidth from 'ui/helpers/useWidth'
import useGap from 'ui/helpers/useGap'

const gridColumns = {
  xs: {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6',
  },
  sm: {
    1: 'sm:grid-cols-1',
    2: 'sm:grid-cols-2',
    3: 'sm:grid-cols-3',
    4: 'sm:grid-cols-4',
    5: 'sm:grid-cols-5',
    6: 'sm:grid-cols-6',
  },
  md: {
    1: 'md:grid-cols-1',
    2: 'md:grid-cols-2',
    3: 'md:grid-cols-3',
    4: 'md:grid-cols-4',
    5: 'md:grid-cols-5',
    6: 'md:grid-cols-6',
  },
  lg: {
    1: 'lg:grid-cols-1',
    2: 'lg:grid-cols-2',
    3: 'lg:grid-cols-3',
    4: 'lg:grid-cols-4',
    5: 'lg:grid-cols-5',
    6: 'lg:grid-cols-6',
  },
  xl: {
    1: 'xl:grid-cols-1',
    2: 'xl:grid-cols-2',
    3: 'xl:grid-cols-3',
    4: 'xl:grid-cols-4',
    5: 'xl:grid-cols-5',
    6: 'xl:grid-cols-6',
  },
}

const placeItemsClasses = {
  xs: {
    start: 'place-items-start',
    center: 'place-items-center',
    end: 'place-items-end',
    stretch: 'place-items-stretch',
  },
  sm: {
    start: 'sm:place-items-start',
    center: 'sm:place-items-center',
    end: 'sm:place-items-end',
    stretch: 'sm:place-items-stretch',
  },
  md: {
    start: 'md:place-items-start',
    center: 'md:place-items-center',
    end: 'md:place-items-end',
    stretch: 'md:place-items-stretch',
  },
  lg: {
    start: 'lg:place-items-start',
    center: 'lg:place-items-center',
    end: 'lg:place-items-end',
    stretch: 'lg:place-items-stretch',
  },
  xl: {
    start: 'xl:place-items-start',
    center: 'xl:place-items-center',
    end: 'xl:place-items-end',
    stretch: 'xl:place-items-stretch',
  },
}

function useColumnClass(columns, defaultValue = 1) {
  return useMemo(() => {
    if (!columns || typeof columns !== 'object')
      return gridColumns.xs[defaultValue]

    const classes = Object.keys(columns).reduce((acc, breakpoint) => {
      const value = columns[breakpoint]
      acc.push(gridColumns[breakpoint][value])
      return acc
    }, [])

    return classes.join(' ')
  }, [columns, defaultValue])
}

function usePlaceItemsClass(placeItems, defaultValue = 'stretch') {
  return useMemo(() => {
    if (!placeItems || typeof placeItems !== 'object')
      return placeItemsClasses.xs[defaultValue]

    const classes = Object.keys(placeItems).reduce((acc, breakpoint) => {
      const value = placeItems[breakpoint]
      acc.push(placeItemsClasses[breakpoint][value])
      return acc
    }, [])

    return classes.join(' ')
  }, [placeItems, defaultValue])
}

export default function Grid({
  children,
  className = '',
  columns,
  gap,
  id,
  placeItems,
  width,
}) {
  const columnClass = useColumnClass(columns)
  const gapClass = useGap(gap)
  const placeItemsClass = usePlaceItemsClass(placeItems)
  const widthClass = useWidth(width)

  return (
    <div
      className={`grid grid-flow-row ${columnClass} ${gapClass} ${placeItemsClass} ${widthClass} ${className}`}
      id={id}
    >
      {children}
    </div>
  )
}
Grid.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  children: PropTypes.node,
  columns: PropTypes.object,
  gap: PropTypes.object,
  placeItems: PropTypes.object,
  width: PropTypes.object,
}
