import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import {
  EmailShareButton,
  EmailIcon,
  FacebookShareButton,
  FacebookIcon,
  LineShareButton,
  LineIcon,
  PinterestShareButton,
  PinterestIcon,
  TelegramShareButton,
  TelegramIcon,
  TwitterShareButton,
  TwitterIcon,
  WhatsappShareButton,
  WhatsappIcon,
} from 'react-share'

const Heading = dynamic(() => import('ui/typography/Heading'))

const socialButtons = {
  email: { Button: EmailShareButton, Icon: EmailIcon, titleProp: 'subject' },
  Facebook: {
    Button: FacebookShareButton,
    Icon: FacebookIcon,
    titleProp: 'quote',
  },
  Line: { Button: LineShareButton, Icon: LineIcon },
  Pinterest: { Button: PinterestShareButton, Icon: PinterestIcon },
  Telegram: { Button: TelegramShareButton, Icon: TelegramIcon },
  Twitter: { Button: TwitterShareButton, Icon: TwitterIcon },
  Whatsapp: { Button: WhatsappShareButton, Icon: WhatsappIcon },
}

export default function ShareButton({
  className = '',
  id,
  title,
  pageData,
  iconSize = 32,
  ...socialProps
}) {
  const activeButtons = Object.entries(socialProps)
    .filter(([, value]) => value === true)
    .map(([key]) => key)

  return (
    <div className={`space-y-2 ${className}`} id={id}>
      <Heading title={title} as="h3" />
      <div className="space-x-1 rtl:space-x-reverse">
        {activeButtons.map(button => {
          const { Button, Icon, titleProp = 'title' } = socialButtons[button]
          const buttonProps = {}
          buttonProps[titleProp] = pageData.title

          return (
            <Button key={button} url={pageData.absoluteUrl} {...buttonProps}>
              <Icon size={iconSize} round />
            </Button>
          )
        })}
      </div>
    </div>
  )
}
ShareButton.propTypes = {
  className: PropTypes.string,
  iconSize: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  id: PropTypes.string,
  pageData: PropTypes.object,
  title: PropTypes.string,
}
