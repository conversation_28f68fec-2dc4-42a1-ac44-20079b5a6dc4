import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'

import { useFeatureFlagEnabled } from 'hooks/useFeatureFlagEnabled'
import ContentCard from 'ui/data-display/ContentCard'
import Alert from 'ui/feedback/Alert'

export function VerifyLogin({
  className,
  id,
  instruction,
  instructionTitle,
  title,
}) {
  const { t } = useTranslation()
  const isUserLoginEnabled = useFeatureFlagEnabled('user-login')

  if (!isUserLoginEnabled) {
    return null
  }

  return (
    <ContentCard id={id} className={className} title={title}>
      <Alert
        title={instructionTitle ?? t('verifyLoginTitle')}
        message={instruction ?? t('verifyLoginDescription')}
      />
    </ContentCard>
  )
}

VerifyLogin.propTypes = {
  className: PropTypes.string,
  id: PropTypes.string,
  instruction: PropTypes.string,
  instructionTitle: PropTypes.string,
  title: PropTypes.string,
}
