import PropTypes from 'prop-types'
import { useCallback } from 'react'

import { signIn } from 'next-auth/react'
import { useTranslation } from 'next-i18next'

import Button from 'ui/buttons/Button'

export default function GoogleLoginForm({ callbackUrl }) {
  const { t } = useTranslation()

  const onGoogleLogin = useCallback(() => {
    signIn('google', {
      callbackUrl,
    })
  }, [callbackUrl])

  return (
    <Button
      label={t('google')}
      variant="hollow"
      icon="google"
      onClick={onGoogleLogin}
    />
  )
}

GoogleLoginForm.propTypes = {
  callbackUrl: PropTypes.string,
}
