import PropTypes from 'prop-types'
import { Fragment, useMemo } from 'react'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { useSearchParams } from 'next/navigation'

import { useSession } from 'next-auth/react'

import { useSiteAuth } from 'hooks/useSiteAuth'

import ContentCard from 'ui/data-display/ContentCard'
import Icon from 'ui/icons/Icon'
import Link from 'ui/navigation/Link'
import Heading from 'ui/typography/Heading'

import Navigate from 'ui/helpers/Navigate'
import { AuthError } from 'ui/feedback/AuthError'
import { getUrlWithCallback } from '../shared/utils/getUrlWithCallback'
import clsx from 'clsx'

const EmailLoginForm = dynamic(() => import('./components/EmailLoginForm'))
const GoogleLoginForm = dynamic(() => import('./components/GoogleLoginForm'))

const supportedProviders = {
  email: EmailLoginForm,
  google: GoogleLoginForm,
}

export function LoginForm({
  callbackUrl,
  className,
  credentialsErrorDescription,
  credentialsErrorTitle,
  defaultErrorDescription,
  defaultErrorTitle,
  emailErrorDescription,
  emailErrorTitle,
  emailNotSentErrorDescription,
  emailNotSentErrorTitle,
  id,
  oAuthErrorDescription,
  oAuthErrorTitle,
  oAuthNotLinkedErrorDescription,
  oAuthNotLinkedErrorTitle,
  registrationLabel,
  sessionRequiredErrorDescription,
  sessionRequiredErrorTitle,
  title,
}) {
  const { t } = useTranslation()
  const searchParams = useSearchParams()
  const { enabled, providers = [], pages, registrationEnabled } = useSiteAuth()

  const callbackUrlToUse = searchParams.get('callbackUrl') ?? callbackUrl ?? '/'
  const errorCode = searchParams.get('error')

  const enabledProviders = useMemo(() => {
    const providerTypes = providers.reduce((acc, provider) => {
      if (!acc[provider.code]) {
        return {
          ...acc,
          [provider.code]: [[provider.type]],
        }
      }
      if (acc[provider.code].includes(provider.type)) {
        return acc
      }
      return {
        ...acc,
        [provider.code]: [...acc[provider.code], provider.type],
      }
    }, {})
    // If no providers are enabled, default to email
    if (Object.keys(providerTypes).length === 0) {
      return { email: ['email'] }
    }
    return providerTypes
  }, [providers])

  const error = useMemo(() => {
    const config = {
      OAuthSignin: {
        type: 'danger',
        title: oAuthErrorTitle ?? t('auth_error_default_title'),
        description: oAuthErrorDescription ?? t('auth_error_oauth_description'),
      },
      OAuthCallback: {
        type: 'danger',
        title: oAuthErrorTitle ?? t('auth_error_default_title'),
        description: oAuthErrorDescription ?? t('auth_error_oauth_description'),
      },
      OAuthCreateAccount: {
        type: 'danger',
        title: oAuthErrorTitle ?? t('auth_error_default_title'),
        description: oAuthErrorDescription ?? t('auth_error_oauth_description'),
      },
      OAuthAccountNotLinked: {
        type: 'warning',
        title: oAuthNotLinkedErrorTitle ?? t('auth_error_unableToSignIn'),
        description:
          oAuthNotLinkedErrorDescription ??
          t('auth_error_oauthNotLinked_description'),
      },
      EmailCreateAccount: {
        type: 'danger',
        title: emailErrorTitle ?? t('auth_error_default_title'),
        description: emailErrorDescription ?? t('auth_error_oauth_description'),
      },
      Callback: {
        type: 'danger',
        title: defaultErrorTitle ?? t('auth_error_default_title'),
        description:
          defaultErrorDescription ?? t('auth_error_oauth_description'),
      },
      EmailSignin: {
        type: 'warning',
        title: emailNotSentErrorTitle ?? t('auth_error_unableToSignIn'),
        description:
          emailNotSentErrorDescription ??
          t('auth_error_emailNotSent_description'),
      },
      CredentialsSignin: {
        type: 'warning',
        title: credentialsErrorTitle ?? t('auth_error_unableToSignIn'),
        description:
          credentialsErrorDescription ??
          t('auth_error_credentials_description'),
      },
      SessionRequired: {
        type: 'info',
        title: sessionRequiredErrorTitle ?? t('auth_error_session_title'),
        description:
          sessionRequiredErrorDescription ??
          t('auth_error_session_description'),
      },
      Default: {
        type: 'danger',
        title: defaultErrorTitle ?? t('auth_error_default_title'),
        description:
          defaultErrorDescription ?? t('auth_error_default_description'),
      },
    }
    return config[errorCode ?? '']
  }, [
    credentialsErrorDescription,
    credentialsErrorTitle,
    defaultErrorDescription,
    defaultErrorTitle,
    emailErrorDescription,
    emailErrorTitle,
    emailNotSentErrorDescription,
    emailNotSentErrorTitle,
    errorCode,
    oAuthErrorDescription,
    oAuthErrorTitle,
    oAuthNotLinkedErrorDescription,
    oAuthNotLinkedErrorTitle,
    sessionRequiredErrorDescription,
    sessionRequiredErrorTitle,
    t,
  ])

  // If user is already logged in, redirect to the callback URL
  const { data: session, status } = useSession()
  const isLoggedIn = Boolean(session?.user && status === 'authenticated')
  if (isLoggedIn) return <Navigate to={callbackUrlToUse} />

  if (!enabled || Object.keys(enabledProviders).length === 0) {
    return null
  }

  return (
    <ContentCard
      id={id}
      className={className}
      title={title}
      footer={
        registrationEnabled &&
        pages.registerPageUrl && (
          <nav>
            <ul className="space-y-4">
              <li className="flex space-x-2 items-center ">
                <Icon
                  name="chevron-right"
                  className="shrink-0 text-xs text-gray-400"
                />
                <Link
                  to={getUrlWithCallback(
                    pages.registerPageUrl,
                    callbackUrlToUse
                  )}
                  className="text-gray-500 hover:text-gray-700"
                >
                  {registrationLabel || t('register')}
                </Link>
              </li>
            </ul>
          </nav>
        )
      }
    >
      {Boolean(error) && <AuthError error={error} />}

      {Object.entries(enabledProviders).map(([code, providers], index) => {
        return (
          <Fragment key={code}>
            {index > 0 && <Heading as="h3">{t('orSignInWith')}</Heading>}
            <div
              className={clsx('flex', {
                'flex-col': code !== 'oauth',
                'flex-wrap items-start gap-4': code === 'oauth',
              })}
            >
              {providers.map(providerType => {
                const ProviderComponent = supportedProviders[providerType]
                if (!ProviderComponent) {
                  return null
                }
                return (
                  <ProviderComponent
                    key={providerType}
                    callbackUrl={callbackUrlToUse}
                  />
                )
              })}
            </div>
          </Fragment>
        )
      })}
    </ContentCard>
  )
}

LoginForm.propTypes = {
  callbackUrl: PropTypes.string,
  className: PropTypes.string,
  credentialsErrorDescription: PropTypes.string,
  credentialsErrorTitle: PropTypes.string,
  defaultErrorDescription: PropTypes.string,
  defaultErrorTitle: PropTypes.string,
  emailErrorDescription: PropTypes.string,
  emailErrorTitle: PropTypes.string,
  emailNotSentErrorDescription: PropTypes.string,
  emailNotSentErrorTitle: PropTypes.string,
  id: PropTypes.string,
  oAuthErrorDescription: PropTypes.string,
  oAuthErrorTitle: PropTypes.string,
  oAuthNotLinkedErrorDescription: PropTypes.string,
  oAuthNotLinkedErrorTitle: PropTypes.string,
  registrationLabel: PropTypes.string,
  sessionRequiredErrorDescription: PropTypes.string,
  sessionRequiredErrorTitle: PropTypes.string,
  title: PropTypes.string,
}
