import PropTypes from 'prop-types'
import { useCallback, useMemo, useState } from 'react'

import { signIn } from 'next-auth/react'
import { useTranslation } from 'next-i18next'
import { useSearchParams } from 'next/navigation'

import { useSession } from 'next-auth/react'

import { useSiteAuth } from 'hooks/useSiteAuth'
import ContentCard from 'ui/data-display/ContentCard'
import Icon from 'ui/icons/Icon'
import Link from 'ui/navigation/Link'
import Form from 'ui/data-entry/Form'
import FormBuilder from 'ui/data-entry/FormBuilder'
import InputController from 'ui/data-entry/Input'
import Navigate from 'ui/helpers/Navigate'
import Submit from 'ui/data-entry/Submit'
import { AuthError } from 'ui/feedback/AuthError'
import { parseAPIError } from 'utils/error'

import AcceptTermsCheckbox from '../shared/AcceptTermsCheckbox'
import { getUrlWithCallback } from '../shared/utils/getUrlWithCallback'
import { useRegisterUser } from './hooks/useRegisterUser'

export function RegistrationForm({
  callbackUrl,
  className,
  id,
  submitLabel,
  title,
}) {
  const { t } = useTranslation()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(false)
  const { enabled, registrationEnabled, pages, profileForm, terms } =
    useSiteAuth()

  const callbackUrlToUse =
    searchParams.get('callbackUrl') ??
    callbackUrl ??
    pages.newUserPageUrl ??
    '/'

  const loginPageUrl = pages?.loginPageUrl ?? '/login'

  const { mutate, error } = useRegisterUser({
    onSuccess: user => {
      // TODO: Only email provider is supported for now, so using it direcly by default
      signIn('email', {
        email: user.email,
        callbackUrl: callbackUrlToUse,
      })
    },
  })

  const onRegister = useCallback(
    formValues => {
      setLoading(true)
      mutate(formValues, {
        onError: () => {
          setLoading(false)
        },
      })
    },
    [mutate]
  )

  const errorConfig = useMemo(() => {
    if (error?.status === 409) {
      return {
        type: 'warning',
        title: t('auth_error_email_already_exists_title'),
        description: t('auth_error_email_already_exists_description'),
      }
    }
    if (error?.status === 400) {
      return {
        type: 'warning',
        title: t('auth_error_params_title'),
        description: t('auth_error_params_description'),
      }
    }

    return {
      type: 'danger',
      title: t('auth_error_default_title'),
      description: t('auth_error_default_description'),
    }
  }, [error?.status, t])

  const formData = useMemo(() => {
    return {
      email: searchParams.get('email') || '',
    }
  }, [searchParams])

  const formErrors = useMemo(() => {
    if (error?.status === 400) {
      return parseAPIError(error)
    }
    return null
  }, [error])

  // If user is already logged in, redirect to the callback URL
  const { data: session, status } = useSession()
  const isLoggedIn = Boolean(session?.user && status === 'authenticated')
  if (isLoggedIn) return <Navigate to={callbackUrlToUse} />

  if (!enabled || !registrationEnabled) {
    return null
  }

  const useDynamicForm = profileForm && profileForm?.fields?.length > 0

  return (
    <ContentCard
      id={id}
      className={className}
      size="lg"
      title={title}
      footer={
        registrationEnabled &&
        pages.loginPageUrl && (
          <nav>
            <ul className="space-y-4">
              <li className="flex space-x-2 items-center ">
                <Icon
                  name="chevron-right"
                  className="shrink-0 text-xs text-gray-400"
                />
                <Link
                  to={getUrlWithCallback(pages.loginPageUrl, callbackUrlToUse)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  {t('memberSignIn')}
                </Link>
              </li>
            </ul>
          </nav>
        )
      }
    >
      {Boolean(error) && (
        <AuthError
          error={errorConfig}
          showLink={error?.status === 409}
          url={getUrlWithCallback(loginPageUrl, callbackUrlToUse)}
          linkLabel={t('signIn')}
        />
      )}

      <Form
        data={formData}
        errors={formErrors}
        onSubmit={onRegister}
        className="flex flex-col space-y-4"
      >
        {useDynamicForm && <FormBuilder {...profileForm} />}
        {!useDynamicForm && (
          <InputController
            name="email"
            type="email"
            label={t('email')}
            required
          />
        )}
        <AcceptTermsCheckbox terms={terms} />
        <div>
          <Submit loading={loading} label={submitLabel || t('register')} />
        </div>
      </Form>
    </ContentCard>
  )
}

RegistrationForm.propTypes = {
  callbackUrl: PropTypes.string,
  className: PropTypes.string,
  id: PropTypes.string,
  submitLabel: PropTypes.string,
  terms: PropTypes.object,
  termsUrl: PropTypes.string,
  termsUrlLabel: PropTypes.string,
  title: PropTypes.string,
}
