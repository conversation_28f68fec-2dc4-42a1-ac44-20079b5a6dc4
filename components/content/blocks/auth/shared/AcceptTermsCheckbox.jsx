import PropTypes from 'prop-types'
import { useTranslation } from 'next-i18next'

import CheckboxController from 'ui/data-entry/Checkbox'
import Link from 'ui/navigation/Link'
import RichText from 'ui/typography/RichText'

export default function AcceptTermsCheckbox({ terms }) {
  const { t } = useTranslation()

  if (!terms) {
    return null
  }

  const { description, url, urlLabel, required } = terms

  return (
    <CheckboxController
      id="acceptUserTerms"
      name="terms"
      label={<RichText doc={description} />}
      labelExtra={
        url ? (
          <Link href={url} target="_blank" basic>
            {urlLabel || t('viewRegistrationTerms')}
          </Link>
        ) : undefined
      }
      required={required}
    />
  )
}

AcceptTermsCheckbox.propTypes = {
  terms: PropTypes.shape({
    description: PropTypes.any.isRequired,
    url: PropTypes.string,
    urlLabel: PropTypes.string,
    required: PropTypes.bool.isRequired,
  }).isRequired,
}
