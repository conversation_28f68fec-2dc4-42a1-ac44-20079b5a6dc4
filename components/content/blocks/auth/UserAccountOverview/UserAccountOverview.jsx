import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'

import { useFeatureFlagEnabled } from 'hooks/useFeatureFlagEnabled'
import { useSiteAuth } from 'hooks/useSiteAuth'
import Avatar from 'ui/data-display/Avatar'
import Alert from 'ui/feedback/Alert'
import Link from 'ui/navigation/Link'
import { formatDate, useDatetimeLocale } from 'utils/datetime'

export function UserAccountOverview({ className, id, user }) {
  const { t } = useTranslation()
  const locale = useDatetimeLocale()
  const { requireRegistration, pages } = useSiteAuth()
  const isUserLoginEnabled = useFeatureFlagEnabled('user-login')

  const { name, email, image, createdAt, isProfileComplete, lastLogin } =
    user ?? {}

  if (!isUserLoginEnabled || !user) {
    return null
  }

  return (
    <div id={id} className={className}>
      {requireRegistration && !isProfileComplete && (
        <Alert
          type="warning"
          title={t('profileIncomplete')}
          className="mb-4"
          messageClassName="flex flex-col items-start space-y-4"
        >
          <p>{t('completeProfileToContinue')}</p>
          {pages?.newUserPageUrl && (
            <Link
              to={pages?.newUserPageUrl}
              className="font-bold uppercase"
              underline
              basic
            >
              {t('completeProfile')}
            </Link>
          )}
        </Alert>
      )}

      <Avatar
        title={name ?? email}
        subtitle={
          name ? (
            <Link href={`mailto:${email}`} basic>
              {email}
            </Link>
          ) : undefined
        }
        description={`${t('joinedOn', {
          date: formatDate(createdAt, 'PPP', { locale }),
        })} \n ${t('lastLogin', {
          date: formatDate(lastLogin, 'PPP p', { locale }),
        })}`}
        size="xl"
        className="w-full shrink-0"
        image={image}
      />
    </div>
  )
}

UserAccountOverview.propTypes = {
  className: PropTypes.string,
  id: PropTypes.string,
  user: PropTypes.object,
}
