import PropTypes from 'prop-types'
import { useCallback, useMemo, useState } from 'react'

import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'

import { usePageContext } from 'components/PageProvider'
import Button from 'ui/buttons/Button'
import Form from 'ui/data-entry/Form'
import FormBuilder from 'ui/data-entry/FormBuilder'
import Submit from 'ui/data-entry/Submit'
import { AuthError } from 'ui/feedback/AuthError'
import { parseAPIError } from 'utils/error'

import { useUpdateProfile } from '../hooks/useUpdateProfile'

export function Edit({ profile, profileForm, onView }) {
  const { t } = useTranslation()
  const router = useRouter()
  const pageContext = usePageContext()

  // Needed to prevent flickering of the submit button before reloading the page
  const [persistedLoading, setPersistedLoading] = useState(false)

  const { mutate, error } = useUpdateProfile(
    profileForm.csrfToken,
    pageContext?.cacheKey
  )

  const onSubmit = useCallback(
    formValues => {
      setPersistedLoading(true)
      mutate(formValues, {
        onSuccess: () => {
          // NOTE: The page needs to reload since we are refreshing data in __NEXT_DATA__
          router.reload()
        },
        onError: () => {
          setPersistedLoading(false)
        },
      })
    },
    [mutate, router]
  )

  const displayedError = useMemo(() => {
    if (error?.status === 400) {
      if (error.code === 'TOKEN_NOT_VALID') {
        return {
          type: 'warning',
          title: t('profile_error_tokenTitle'),
          description: t('profile_error_tokenDescription'),
        }
      }

      return {
        type: 'warning',
        title: t('profile_error_params_title'),
        description: t('profile_error_params_description'),
      }
    }

    return {
      type: 'error',
      title: t('auth_error_default_title'),
      description: t('auth_error_default_description'),
    }
  }, [error, t])

  const formErrors = useMemo(() => {
    if (error?.status === 400) {
      return parseAPIError(error)
    }
    return null
  }, [error])

  return (
    <Form
      data={{ profile } ?? { profile: {} }}
      errors={formErrors}
      onSubmit={onSubmit}
      className="flex w-full flex-col space-y-4"
    >
      {error && <AuthError error={displayedError} />}
      <FormBuilder {...profileForm} fieldsNamePath={['profile']} />
      <div className="flex justify-between">
        <Button label={t('cancel')} variant="secondary" onClick={onView} />
        <Submit label={t('update')} loading={persistedLoading} />
      </div>
    </Form>
  )
}

Edit.propTypes = {
  profile: PropTypes.object,
  profileForm: PropTypes.object,
  onView: PropTypes.func,
  userId: PropTypes.string.isRequired,
}
