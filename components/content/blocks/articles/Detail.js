import PropTypes from 'prop-types'

import { NewsArticleJsonLd } from 'next-seo'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import dynamic from 'next/dynamic'

import { usePageContext } from 'components/PageProvider'
import { FormatDate } from 'utils/datetime'
import { getImageUrl } from 'utils/images'
import LinkList, { LinkItem } from 'ui/navigation/LinkList'
import { formatBytes } from 'utils/strings'

const Picture = dynamic(() => import('ui/data-display/Picture'))
const NotFound = dynamic(() => import('ui/feedback/NotFound'))
const RichText = dynamic(() => import('ui/typography/RichText'))

export default function ArticleDetail({
  Article,
  article: staticArticle,
  showTitle = true,
  showAbstract = true,
  showAuthor = true,
  showBody = true,
  showDate = true,
  showImage = true,
  showLocation = true,
  showFiles = true,
  filesLabel = 'files',
}) {
  const { t } = useTranslation()
  const page = usePageContext()

  const article = staticArticle ?? Article

  if (!article || article.notFound)
    return (
      <div className="grow">
        <NotFound
          title={t('articleNotFound')}
          description={t('articleNotFoundDescription')}
          headTitle={`${t('articleNotFound')} | ${page.site.title}`}
          code="404"
        />
      </div>
    )

  const {
    title,
    subtitle,
    abstract,
    author,
    location: articleLocation,
    body,
    image,
    publishedAt,
    files,
  } = article

  // TODO: Remove location field type checks when we location field migration is done
  let location
  if (typeof articleLocation === 'string' && showLocation && articleLocation) {
    location = articleLocation
  }
  if (
    typeof articleLocation === 'object' &&
    showLocation &&
    ('placeName' in articleLocation || 'name' in articleLocation) &&
    (articleLocation.placeName || articleLocation.name)
  ) {
    location = articleLocation.name || articleLocation.placeName
  }

  return (
    <div className="space-y-6">
      <ArticleSEO article={article} site={page.site} />
      {showImage && image?.file && (
        <Picture
          file={image.file}
          alt={image.alt ?? title}
          caption={image.caption}
          copyright={image.copyright}
          className="rounded-lg"
          priority
          sizes="lg:774px md:975px sm:720px 592px"
          lazy={false}
        />
      )}
      <div className="space-y-4">
        <div className="space-y-1">
          {showTitle !== false && (
            <h1 className="font-extrabold text-4xl">{title}</h1>
          )}
          <h3 className="font-semibold text-gray-600 text-xl">{subtitle}</h3>
        </div>
        {showAbstract && (
          <p className="italic leading-6 text-gray-600 text-xl">{abstract}</p>
        )}
      </div>
      <p className="divide-gray-300 text-lg space-x-2 divide-x-2 text-gray-600 rtl:space-x-reverse">
        {showAuthor && author && <strong>{author}</strong>}
        {showLocation && location && (
          <span className={author ? 'pl-2' : ''}>{location}</span>
        )}
        {showDate && publishedAt && (
          <span className={author || location ? 'pl-2' : ''}>
            <FormatDate date={publishedAt} format="PPp" />
          </span>
        )}
      </p>
      {showBody && <RichText doc={body} decreaseHeaders />}
      {showFiles && files?.length > 0 && (
        <div className={`w-full space-y-4`}>
          <LinkList title={filesLabel}>
            {files.map((item, key) => {
              return (
                <LinkItem
                  icon="file-invoice"
                  key={key}
                  label={item.name}
                  extra={formatBytes(item.size, 0)}
                  url={item.url}
                />
              )
            })}
          </LinkList>
        </div>
      )}
    </div>
  )
}
ArticleDetail.propTypes = {
  Article: PropTypes.object,
  showAbstract: PropTypes.bool,
  showAuthor: PropTypes.bool,
  showBody: PropTypes.bool,
  showDate: PropTypes.bool,
  showImage: PropTypes.bool,
  showLocation: PropTypes.bool,
  showSubtitle: PropTypes.bool,
  showTitle: PropTypes.bool,
  showFiles: PropTypes.bool,
  filesLabel: PropTypes.string,
}

const imgFormats = [`w:500,h:500`, `w:800,h:600`, `w:1280,h:600`]

function ArticleSEO({ article, site }) {
  const router = useRouter()
  if (!article) return null

  return (
    <NewsArticleJsonLd
      title={article.title}
      description={article.abstract || article.subtitle || article.title}
      section="news"
      authorName={article.author}
      keywords={article.tags?.join(',')}
      url={`https://${site.domain}${router.asPath}`}
      dateCreated={article.createdAt}
      datePublished={article.createdAt}
      dateModified={article.updatedAt}
      publisherName={site.name}
      publisherLogo={getImageUrl(site.logo, 'w:500')}
      images={
        article.image?.file
          ? imgFormats.map(format => getImageUrl(article.image.file, format))
          : []
      }
    />
  )
}
ArticleSEO.propTypes = {
  article: PropTypes.object.isRequired,
  site: PropTypes.object,
}
