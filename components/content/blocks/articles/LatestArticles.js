import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const ListTitle = dynamic(() => import('ui/data-display/ListTitle'))
const Link = dynamic(() => import('ui/navigation/Link'))
const Articles = dynamic(() => import('./shared/Articles'))

export default function LatestArticles({
  articles = { items: [], count: 0 },
  columns,
  displayMode,
  showDate,
  showDescription,
  showImage,
  enableAnimations,
  title,
}) {
  const { t } = useTranslation()

  return (
    <div className="space-y-4">
      <ListTitle
        title={title}
        extra={
          articles.listPagePath && (
            <Link
              basic
              className="font-semibold uppercase"
              to={articles.listPagePath}
            >
              {t('readMore')}
            </Link>
          )
        }
      />
      <Articles
        items={articles?.items}
        displayMode={displayMode}
        columns={columns}
        showDate={showDate}
        showDescription={showDescription}
        showImage={showImage}
        enableAnimations={enableAnimations}
      />
    </div>
  )
}
LatestArticles.propTypes = {
  articles: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
    listPagePath: PropTypes.string,
  }),
  columns: PropTypes.object,
  displayMode: PropTypes.oneOf(['cards', 'list']),
  showDate: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
  enableAnimations: PropTypes.bool,
  title: PropTypes.string,
}
