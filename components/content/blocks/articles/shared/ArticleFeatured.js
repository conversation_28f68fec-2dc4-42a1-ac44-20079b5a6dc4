import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { FormatDate } from 'utils/datetime'
import { getImageUrl } from 'utils/images'

const Link = dynamic(() => import('ui/navigation/Link'))

export default function ArticleFeatured({ article, showDate, showImage }) {
  const { startsAt } = article.site || {}

  const imageUrl = showImage ? getImageUrl(article.image?.file, 'w:500') : null

  return (
    <Link
      className="relative flex min-h-[250px] flex-col rounded-md border border-gray-100 bg-primary-400 bg-cover bg-center shadow-md"
      style={showImage ? { backgroundImage: `url(${imageUrl})` } : undefined}
      to={article.url}
    >
      <div className="flex flex-grow flex-col justify-center space-y-0 rounded-md bg-gradient-to-b from-transparent via-gray-900/40 to-gray-900/80 p-6 text-white">
        <div>
          {showDate && (
            <p className="font-semibold text-gray-300 text-xs">
              <FormatDate date={startsAt || article.createdAt} format="PP" />
            </p>
          )}
          <h3 className="font-bold text-xl">{article.title}</h3>
        </div>
      </div>
    </Link>
  )
}
ArticleFeatured.propTypes = {
  article: PropTypes.object.isRequired,
  showDate: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
  site: PropTypes.object,
}
