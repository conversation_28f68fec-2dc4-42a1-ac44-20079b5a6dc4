import PropTypes from 'prop-types'
import { useCallback, useEffect, useState } from 'react'

import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'

import useColumns from 'ui/helpers/useColumns'
import useGap from 'ui/helpers/useGap'
import useWidth from 'ui/helpers/useWidth'

import { Dialog, DialogContent } from 'ui/feedback/FloatingDialog'

const Picture = dynamic(() => import('ui/data-display/Picture'))
const Image = dynamic(() => import('ui/data-display/Image'))
const Icon = dynamic(() => import('ui/icons/Icon'))

export default function ImageGallery({
  blockId,
  columns,
  gap,
  items,
  showCaptionsInList,
  showTitleInList,
  thumbAspectRatio,
  title,
  width,
}) {
  const router = useRouter()

  const columnClass = useColumns(columns)
  const gapClass = useGap(gap)
  const widthClass = useWidth(width)
  const [loading, setLoading] = useState(true)

  const queryIndex = parseInt(router.query[`galleryImage-${blockId}`])
  const isDialogOpen = !isNaN(queryIndex)
  const currentItem = isDialogOpen ? items[queryIndex] : null

  const goToItem = useCallback(
    (i, push = false) => {
      const totalItems = items.length
      const lastIndex = totalItems - 1
      const index = i < 0 ? lastIndex : i > lastIndex ? 0 : i

      setLoading(true)

      if (index === queryIndex) {
        return
      }

      const query = {
        ...router.query,
        [`galleryImage-${blockId}`]: index,
      }

      if (i === null) {
        delete query[`galleryImage-${blockId}`]
      }

      const args = [
        {
          pathname: router.pathname,
          query,
        },
        null,
        {
          shallow: true,
        },
      ]

      // We use `push` only when initially opening the image gallery.
      // This allows users to navigate between the page and the gallery.
      // When switching between images, we use `replace` to prevent
      // the user from needing to click repeatedly the back button
      // to return to the previous page.
      push ? router.push(...args) : router.replace(...args)
    },
    [items.length, queryIndex, router, blockId]
  )

  useEffect(() => {
    if (!isNaN(queryIndex)) {
      goToItem(queryIndex)
    }
  }, [goToItem, queryIndex])

  useEffect(() => {
    if (!isDialogOpen) return

    const handleKeyPress = event => {
      if (event.key === 'ArrowLeft') {
        goToItem(queryIndex - 1)
      }
      if (event.key === 'ArrowRight') {
        goToItem(queryIndex + 1)
      }
    }

    window.addEventListener('keydown', handleKeyPress)

    return () => {
      window.removeEventListener('keydown', handleKeyPress)
    }
  }, [isDialogOpen, queryIndex, goToItem])

  const onCloseDialog = useCallback(() => {
    goToItem(null)
  }, [goToItem])

  return (
    <div>
      <div className="space-y-4">
        {showTitleInList && title && (
          <h3 className="font-bold text-xl">{title}</h3>
        )}

        <div className={`grid ${columnClass} ${gapClass} ${widthClass}`}>
          {items.map((item, i) => (
            <Picture
              key={`gallery-item-${i}`}
              className="focus-visible:outline-0 focus-visible:ring-1 focus-visible:ring-primary-600 rounded-md"
              innerClassName="h-full w-full"
              file={item.image}
              caption={showCaptionsInList ? item.caption : undefined}
              copyright={showCaptionsInList ? item.copyright : undefined}
              aspectRatio={thumbAspectRatio || '16/9'}
              sizes="320px"
              onClick={() => goToItem(i, true)}
              lazy
            />
          ))}
        </div>
      </div>

      <Dialog prefix={title} open={isDialogOpen} onOpenChange={onCloseDialog}>
        <DialogContent
          overlayClass="p-0 lg:p-2 2xl:p-4 bg-gradient-to-b from-black/10 to-black/70"
          boxClass="h-full w-full"
          contentClass="flex select-none flex-col justify-between w-full h-full gap-4"
          transparent
        >
          <div className="relative flex h-full w-full flex-grow-0 flex-col">
            <div className="relative flex h-full w-full flex-grow-0 items-center justify-center">
              {currentItem && (
                <Image
                  className={`absolute inset-0 h-full drop-shadow-lg mx-auto ${
                    loading ? 'blur' : ''
                  }`}
                  objectFit="contain"
                  objectPosition="center"
                  file={currentItem.image}
                  sizes="xl:1280px lg:1024px md:720px 320px"
                  alt={
                    currentItem.caption ||
                    currentItem.description ||
                    currentItem.image?.originalFilename
                  }
                  onLoadingComplete={() => setLoading(false)}
                />
              )}
              <div
                className={`absolute inset-0 flex h-full w-full items-center justify-center transition-opacity duration-100 ease-in-out ${
                  loading ? 'opacity-100' : 'opacity-0'
                }`}
              >
                <Icon
                  className="animate-spin text-white text-4xl"
                  name="spinner"
                />
              </div>
            </div>
            <div className="absolute inset-0 flex items-stretch justify-between">
              <NavButton
                direction="prev"
                onClick={() => goToItem(queryIndex - 1)}
              />
              <NavButton
                direction="next"
                onClick={() => goToItem(queryIndex + 1)}
              />
            </div>
          </div>

          <div
            className={`flex h-12 flex-col items-center justify-center overflow-hidden transition-all duration-100 ease-in-out ${
              loading ? 'opacity-0' : 'opacity-100'
            }`}
          >
            <h3 className="font-bold text-white/80 text-xl">
              {currentItem?.caption}
            </h3>
            <p className="text-gray-500">{currentItem?.description}</p>
          </div>

          <div className="flex shrink-0 snap-x snap-proximity flex-row flex-nowrap gap-2 overflow-x-auto p-2">
            {items.map((item, i) => (
              <button
                className="z-10 w-24 shrink-0 snap-start drop-shadow-lg"
                id={`gallery-image-${blockId}-${i}`}
                key={`gallery-image-${blockId}-${i}`}
                onClick={() => goToItem(i)}
              >
                <Image
                  className={`w-24 ${
                    i === queryIndex ? 'ring-[3px] ring-white/70' : ''
                  }`}
                  file={item.image}
                  aspectRatio="1/1"
                  sizes="96px"
                  lazy
                />
              </button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
ImageGallery.propTypes = {
  blockId: PropTypes.string,
  columns: PropTypes.object,
  gap: PropTypes.object,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      image: PropTypes.object,
      caption: PropTypes.string,
      copyright: PropTypes.string,
    })
  ),
  showCaptionsInList: PropTypes.bool,
  showTitleInList: PropTypes.bool,
  thumbAspectRatio: PropTypes.string,
  title: PropTypes.string,
  width: PropTypes.object,
}

function NavButton({ direction = 'prev', onClick }) {
  const bgClass =
    direction === 'next' ? 'pl-16 bg-gradient-to-l' : 'pr-16 bg-gradient-to-r'

  return (
    <button className="group relative p-8 focus:outline-none" onClick={onClick}>
      <div
        className={`absolute inset-0 from-white/0 to-white/0 opacity-40 transition-all duration-500 ease-in-out group-hover:opacity-100 group-focus:opacity-100 ${bgClass}`}
      />
      <div
        className={`flex h-12 w-12 items-center justify-center rounded-full bg-black/50 text-white opacity-0 transition-all duration-300 text-2xl group-hover:scale-110 group-hover:opacity-80 group-focus:scale-110 group-focus:opacity-80 group-focus:ring-[3px] group-focus:ring-white/50  ${
          direction === 'next' ? 'pl-1' : 'pr-1'
        }`}
      >
        <Icon name={direction === 'next' ? 'chevron-right' : 'chevron-left'} />
      </div>
    </button>
  )
}
NavButton.propTypes = {
  direction: PropTypes.oneOf(['prev', 'next']),
  onClick: PropTypes.func,
}
