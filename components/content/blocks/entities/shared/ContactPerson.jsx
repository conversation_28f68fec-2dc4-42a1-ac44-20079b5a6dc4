import PropTypes from 'prop-types'
import React from 'react'

import { useTranslation } from 'next-i18next'

import Avatar from 'ui/data-display/Avatar'
import Link from 'ui/navigation/Link'

import { getPreacherName } from '../utils/getPreacherName'

export function ContactPerson({ contactPerson }) {
  const { t } = useTranslation('church-finder')

  if (!contactPerson) {
    return null
  }

  const {
    title: roleTitle,
    role,
    avatar: roleAvatar,
    email: roleEmail,
    phone: rolePhone,
    person,
  } = contactPerson

  const { avatar, image, email, mobile, phone } = person || {}

  return (
    <Avatar
      image={roleAvatar ?? avatar ?? image}
      title={getPreacherName(person)}
      subtitle={roleTitle || t(`role_${role}`)}
      description={
        <>
          <Link
            to={`mailto:${roleEmail || email}`}
            className="text-primary-800 dark:text-secondary-400 hover:text-primary-900 dark:hover:text-secondary-500/90"
          >
            {roleEmail || email}
          </Link>
          <br />
          {rolePhone || mobile || phone}
        </>
      }
      size="xl"
    />
  )
}

ContactPerson.propTypes = {
  contactPerson: PropTypes.object,
}
