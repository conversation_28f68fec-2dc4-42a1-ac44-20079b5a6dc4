import PropTypes from 'prop-types'
import { useMemo } from 'react'

import { formatISO } from 'date-fns/formatISO'
import { useTranslation } from 'next-i18next'

import LabelIcon from 'ui/data-display/LabelIcon'
import Divider from 'ui/helpers/Divider'
import { isEmpty } from 'utils/arrays'
import { FormatDate } from 'utils/datetime'

import { Preacher } from './Preacher'

const serviceIcons = {
  baptism: 'water',
  communion: 'flatbread',
  outdoorService: 'trees',
  thanksgiving: 'wheat',
  potluck: 'shopping-basket',
  youthSabbath: 'face-awesome',
  pathfinderSabbath: 'campfire',
  womenSabbath: 'people-dress',
}

export function Sermon({
  date,
  time,
  weekday,
  location,
  preacher,
  firstName,
  lastName,
  title,
  description,
  special = {},
}) {
  const { t } = useTranslation('church-finder')

  const specials = useMemo(
    () =>
      Object.entries(special).reduce((acc, [key, value]) => {
        if (value) {
          return [...acc, key]
        }
        return acc
      }, []),
    [special]
  )

  const displayedTime = useMemo(() => {
    if (date && weekday && new Date(date).getDay() !== weekday) {
      return `${t(`weekday-${weekday}`)}, ${time}`
    }
    return time
  }, [date, t, time, weekday])

  return (
    <div className="flex flex-col gap-3">
      <div className="space-y-4">
        <div className="flex w-auto flex-col space-y-2 sm:flex-row sm:justify-between sm:space-y-0 sm:space-x-2 rtl:sm:space-x-reverse">
          {date && (
            <LabelIcon
              icon="calendar"
              iconClass="text-gray-700 dark:text-gray-400"
              label={
                <time
                  className="text-sm font-bold"
                  dateTime={
                    date
                      ? formatISO(new Date(date), {
                          representation: 'date',
                        })
                      : undefined
                  }
                >
                  <FormatDate date={date} format="PPPP" />
                </time>
              }
            />
          )}
          {Boolean(displayedTime) && (
            <LabelIcon
              icon="clock"
              iconClass="text-gray-900 dark:text-gray-300"
              label={displayedTime}
              className="items-center sm:justify-end"
              size="sm"
            />
          )}
        </div>
        <Divider direction="horizontal" />
        <div>
          <div>
            {title && (
              <h3 className="font-bold">
                <span className="block text-sm font-light">
                  {t('sermonTheme')}:
                </span>
                {title}
              </h3>
            )}
            {description && <p className="text-sm">{description}</p>}
          </div>
          {(preacher || firstName || lastName) && (
            <Preacher
              preacher={preacher}
              firstName={firstName}
              lastName={lastName}
            />
          )}
        </div>
        {Boolean(location) && (
          <>
            <Divider direction="horizontal" />
            <LabelIcon
              icon="map-marker-alt"
              iconClass="text-gray-900 dark:text-gray-300"
              label={location}
              className="items-center"
              size="sm"
            />
          </>
        )}
        {!isEmpty(specials) && (
          <>
            <Divider direction="horizontal" />
            <ul className="flex flex-col space-y-1">
              {specials.map(special => (
                <li key={`special-${special}`}>
                  <LabelIcon
                    icon={serviceIcons[special]}
                    iconClass="text-gray-900 dark:text-gray-300"
                    label={t(`service_${special}`)}
                    size="sm"
                    className="items-center"
                  />
                </li>
              ))}
            </ul>
          </>
        )}
      </div>
    </div>
  )
}

Sermon.propTypes = {
  date: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
  time: PropTypes.string,
  weekday: PropTypes.number,
  location: PropTypes.string,
  preacher: PropTypes.object,
  firstName: PropTypes.string,
  lastName: PropTypes.string,
  title: PropTypes.string,
  description: PropTypes.string,
  special: PropTypes.object,
}
