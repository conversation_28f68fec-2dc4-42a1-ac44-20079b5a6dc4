import PropTypes from 'prop-types'
import React from 'react'

import { useTranslation } from 'next-i18next'

import Avatar from 'ui/data-display/Avatar'
import Link from 'ui/navigation/Link'

import { getPreacherName } from '../utils/getPreacherName'

export function Service({ title, showServiceTitle, contactPersons = [] }) {
  const { t } = useTranslation('church-finder')

  if (contactPersons.length === 0) {
    return null
  }

  return (
    <div className="flex flex-col gap-4">
      {showServiceTitle && (
        <h4 className="font-bold text-lg text-gray-800 dark:text-primary-dark-100">
          {title}
        </h4>
      )}
      <ul className="flex flex-col gap-8">
        {contactPersons.map(
          ({
            id,
            title: roleTitle,
            role,
            avatar: roleAvatar,
            email: roleEmail,
            phone: rolePhone,
            person,
          }) => {
            const { avatar, image, email, mobile, phone } = person
            return (
              <li key={id}>
                <Avatar
                  image={roleAvatar ?? avatar ?? image}
                  title={getPreacherName(person)}
                  subtitle={roleTitle || t(`role_${role}`)}
                  description={
                    <>
                      <Link
                        to={`mailto:${roleEmail || email}`}
                        className="text-primary-800 dark:text-secondary-400 hover:text-primary-900 dark:hover:text-secondary-500/90"
                      >
                        {roleEmail || email}
                      </Link>
                      <br />
                      {rolePhone || mobile || phone}
                    </>
                  }
                  size="xl"
                />
              </li>
            )
          }
        )}
      </ul>
    </div>
  )
}

Service.propTypes = {
  title: PropTypes.string,
  showServiceTitle: PropTypes.bool,
  contactPersons: PropTypes.arrayOf(PropTypes.object),
}
