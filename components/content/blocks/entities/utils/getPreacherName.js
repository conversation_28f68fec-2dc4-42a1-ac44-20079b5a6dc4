import { isEmpty } from 'utils/objects'

function getFullName({ prefix, firstName, middleName, lastName, suffix }) {
  return [
    ...(prefix ? [prefix] : []),
    ...(firstName ? [firstName] : []),
    ...(middleName ? [`${middleName.slice(0, 1).toUpperCase()}.`] : []),
    ...(lastName ? [lastName] : []),
    ...(suffix ? [suffix] : []),
  ].join(' ')
}

export function getPreacherName(preacher, firstName, lastName) {
  return !isEmpty(preacher)
    ? getFullName(preacher)
    : firstName || lastName
    ? getFullName({ firstName, lastName })
    : ''
}
