import PropTypes from 'prop-types'
import { useEffect, useMemo, useRef, useState } from 'react'

import clsx from 'clsx'
import isEmpty from 'lodash/isEmpty'
import { useTranslation } from 'next-i18next'

import { usePageContext } from 'components/PageProvider'
import { useMatchMedia } from 'hooks/useMatchMedia'
import Image from 'ui/data-display/Image'
import LabelIcon from 'ui/data-display/LabelIcon'
import Map from 'ui/data-display/Map'
import NotFound from 'ui/feedback/NotFound'
import { media } from 'utils/media'
import Header from '../../Header'
import ChurchContactPersons from '../ChurchContactPersons'
import ChurchSermons from '../ChurchSermons'

export function ChurchDetail({
  Entity: entity,
  sermons,
  entityServices,
  showAmenities = true,
  showDescription = true,
  showImage = true,
  showContactInfo = true,
  showAddress = true,
  showMap = true,
  showLogo = true,
  showServices = true,
  titleColor,
  id,
}) {
  const { t } = useTranslation('church-finder')
  const page = usePageContext()
  const [viewport, setViewport] = useState()
  const imageRef = useRef(null)
  const isMd = useMatchMedia(media.md)

  const { location, contactPersons = [] } = entity || {}

  useEffect(() => {
    if (!location) return

    const { coordinates } = location
    setViewport({
      longitude: coordinates[0],
      latitude: coordinates[1],
      zoom: 16,
    })
  }, [location])

  const locationMarker = useMemo(() => [{ location }], [location])

  if (!entity || entity.notFound)
    return (
      <div className="grow">
        <NotFound
          title={t('common:pageNotFound')}
          description={t('common:pageNotFoundDescription')}
          headTitle={`${t('common:pageNotFound')} | ${page.site.title}`}
          code="404"
        />
      </div>
    )

  const {
    amenities = {},
    name,
    description,
    address,
    logo,
    image,
    phone,
    email,
    siteURL,
    type,
  } = entity
  const showLogoXs = !isMd && showLogo && logo
  const showLogoMd = isMd && showLogo && logo

  const showServiceSection =
    showServices && (contactPersons.length > 0 || type == 'church')

  return (
    <article id={id}>
      <Header as="header" title={name} titleColor={titleColor} />
      <div className="mt-4 flex flex-col rounded-t-lg overflow-hidden divide-y-2 divide-white dark:divide-primary-dark-700 md:flex-row md:divide-x-2 md:divide-y-0">
        {showImage && image && (
          <div
            className={clsx('w-full flex items-center justify-center', {
              'md:h-96 lg:h-[30rem]': !showMap,
              'aspect-4/3 md:aspect-square md:w-2/3 lg:h-96 lg:w-2/3': showMap,
            })}
            ref={imageRef}
          >
            <Image file={image} className="min-h-full min-w-full" />
          </div>
        )}
        {showMap && viewport && (
          <Map
            className={clsx('min-h-[7rem] w-full aspect-video lg:aspect-21/9', {
              'md:min-h-[24rem]': !(showImage && image),
              'md:h-auto md:w-1/2 lg:min-h-[24rem] lg:w-1/3 md:aspect-auto':
                showImage && image,
            })}
            onMove={setViewport}
            viewport={viewport}
            markers={locationMarker}
          />
        )}
      </div>
      <div
        className={`flex flex-col gap-4 bg-white dark:bg-primary-dark-700 p-4 sm:p-6 md:p-8 lg:p-12  ${showServiceSection ? 'border-b-2 dark:border-primary-dark-800 border-dashed' : 'rounded-b-lg'}`}
      >
        <div className="space-y-4 md:order-2">
          {showLogoXs && (
            <Image
              file={logo}
              className="mx-auto my-2 h-24 w-24"
              sizes="sm:100px 200px"
            />
          )}
          {showAddress && !isEmpty(address) && (
            <address className="not-italic text-gray-800 dark:text-primary-dark-100">
              {address.street}
              <br />
              {address.additionalAddress && (
                <>
                  {address.additionalAddress}
                  <br />
                </>
              )}
              {(address.city || address.zip) && (
                <>
                  {address.zip} {address.city}
                  <br />
                </>
              )}
            </address>
          )}
          {showContactInfo && (siteURL || phone || email) && (
            <div className="flex flex-col space-y-1 text-gray-700 dark:text-gray-400">
              {siteURL && (
                <LabelIcon icon="globe" to={siteURL} label={siteURL} />
              )}
              {phone && (
                <LabelIcon icon="telephone" to={`tel:${phone}`} label={phone} />
              )}
              {email && (
                <LabelIcon icon="at" to={`mailto:${email}`} label={email} />
              )}
            </div>
          )}

          {showAmenities &&
            (amenities.accessibility?.stepFreeEntrance ||
              amenities.accessibility?.accessibleParkingSpots ||
              amenities.services?.parkingSpots ||
              amenities.services?.wifi ||
              amenities.services?.nursingRoom) && (
              <div className=" flex flex-row flex-wrap gap-3 text-gray-700 dark:text-gray-400 md:order-5">
                {amenities.accessibility?.stepFreeEntrance && (
                  <LabelIcon icon="wheelchair" label={t('stepFreeEntrance')} />
                )}
                {amenities.accessibility?.accessibleParkingSpots && (
                  <LabelIcon
                    icon="parking-circle"
                    label={t('accessibleParkingSpots')}
                  />
                )}
                {amenities.services?.parkingSpots && (
                  <LabelIcon icon="parking-circle" label={t('parkingSpots')} />
                )}
                {amenities.services?.wifi && (
                  <LabelIcon icon="wifi" label={t('wifi')} />
                )}
                {amenities.services?.nursingRoom && (
                  <LabelIcon icon="baby" label={t('nursingRoom')} />
                )}
              </div>
            )}
        </div>

        {(showLogoMd || (showDescription && description)) && (
          <div className="border-t border-gray-200 pt-4 md:order-1 md:border-none md:pt-0 lg:col-span-2 lg:flex lg:gap-10">
            {showLogoMd && (
              <Image
                file={logo}
                className="mx-auto my-8 h-24 w-24 lg:mx-0 lg:my-0 lg:shrink-0 lg:grow-0"
                sizes="sm:100px 200px"
              />
            )}
            {showDescription && description && (
              <p className="lg:flex-1">{description}</p>
            )}
          </div>
        )}
      </div>

      {showServiceSection && (
        <div className="flex flex-col w-full bg-white dark:bg-primary-dark-700 space-y-8 lg:space-y-12 rounded-b-lg p-4 sm:p-6 md:p-8 lg:p-12">
          {type == 'church' && <ChurchSermons sermons={sermons} />}
          <ChurchContactPersons
            contactPersons={contactPersons}
            entityServices={entityServices}
          />
        </div>
      )}
    </article>
  )
}
ChurchDetail.propTypes = {
  Entity: PropTypes.object,
  sermons: PropTypes.object,
  entityServices: PropTypes.object,
  showAmenities: PropTypes.bool,
  showDescription: PropTypes.bool,
  showImage: PropTypes.bool,
  showContactInfo: PropTypes.bool,
  showAddress: PropTypes.bool,
  showMap: PropTypes.bool,
  showLogo: PropTypes.bool,
  showServices: PropTypes.bool,
  titleColor: PropTypes.string,
  id: PropTypes.string,
}
