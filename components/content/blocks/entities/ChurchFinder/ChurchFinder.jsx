import clsx from 'clsx'
import 'mapbox-gl/dist/mapbox-gl.css'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'

import { SEARCH_RESULT_POINT_ID } from './constants'
import { useChurchFinder } from './hooks/useChurchFinder'

const Button = dynamic(() => import('ui/buttons/Button'))
const Pagination = dynamic(() => import('ui/navigation/Pagination'))
const Map = dynamic(() => import('ui/data-display/Map'))
const NoResultsFound = dynamic(() => import('ui/feedback/NoResultsFound'))
const GeocodeSearch = dynamic(() => import('ui/data-entry/GeocodeSearch'))
const Select = dynamic(() => import('ui/data-entry/Select').then(m => m.Select))

const ChurchItem = dynamic(() => import('./ChurchItem'))
const ChurchMarker = dynamic(() => import('./ChurchMarker'))

/**
 * ChurchFinder component for displaying and searching churches on a map
 * @param {Object} props
 * @param {string} [props.accessToken] Mapbox access token
 * @param {string} [props.className] Additional CSS classes
 * @param {Object} [props.churchFinder] Church finder data
 * @param {Array} props.churchFinder.items List of churches
 * @param {Array} props.churchFinder.markers List of map markers
 * @param {number} props.churchFinder.count Total count of churches
 * @param {boolean} [props.pagination=false] Whether to show pagination
 * @param {number} [props.perPage=20] Items per page
 * @param {string} [props.country] Country code
 * @param {Object} [props.region] Region data
 * @param {boolean} [props.showAmenities] Whether to show amenities
 * @param {boolean} [props.showResults] Whether to show search results
 * @param {boolean} [props.showSearch] Whether to show search controls
 * @param {'white'|'light'|'dark'} [props.searchFormVariant] Search form variant
 * @returns {React.ReactComponent}
 */
export function ChurchFinder({
  accessToken,
  country,
  churchFinder = {
    items: [],
    markers: [],
    count: 0,
  },
  entityTypes,
  region,
  showAmenities,
  showResults,
  showSearch,
  className = '',
  searchFormVariant,
  pagination = false,
  perPage = 20,
}) {
  const { t } = useTranslation(['church-finder', 'common'])
  const {
    searchRadiuses,
    boundingBox,
    radiusBoundingBox,
    items,
    allMarkers,
    viewport,
    searchRadius,
    setViewport,
    setMap,
    geocodeSearch,
    setGeocodeSearch,
    onChurchLocate,
    onResetMap,
    onGeolocate,
    onGeolocateChange,
    onRadiusChange,
  } = useChurchFinder(region, churchFinder)

  const isMd = useMatchMedia(media.md)
  const entityTypeIsChurch =
    entityTypes == [] || entityTypes == ['church'] || entityTypes == 'church'

  return (
    <div className={clsx('relative flex flex-col gap-6', className)}>
      <Map
        className="aspect-4/3 md:aspect-video rounded-lg"
        boundingBox={radiusBoundingBox || boundingBox}
        token={accessToken}
        markers={allMarkers.map(
          ({ location, name, address, description, siteURL, url, id }) => ({
            location,
            name,
            address,
            description,
            siteURL,
            url,
            type: id === SEARCH_RESULT_POINT_ID ? 'point' : 'church',
          })
        )}
        viewport={viewport}
        markerIcon="church"
        onMove={setViewport}
        onGeolocate={onGeolocate}
        getMap={setMap}
        renderItemPopover={marker => <ChurchMarker marker={marker} />}
        showGeolocate
        showControls
      />

      {showSearch && (
        <div className="flex w-full grow flex-row items-center justify-between space-x-2 self-end md:w-2/3 lg:w-1/2">
          <GeocodeSearch
            className="grow basis-3/5"
            country={country}
            onChange={onGeolocateChange}
            onSearch={value => setGeocodeSearch(value)}
            value={geocodeSearch}
            variant={searchFormVariant}
            size="sm"
          />
          <Select
            className="basis-2/5"
            value={searchRadius}
            onChange={onRadiusChange}
            options={searchRadiuses.map(radius => ({
              value: radius,
              label: `${radius} km`,
            }))}
            size="sm"
          />
          <Button
            label={t('common:reset')}
            onClick={onResetMap}
            icon="arrow-rotate-right"
            variant="secondary"
            iconOnly={!isMd}
            disabled={!searchRadius || !geocodeSearch}
          />
        </div>
      )}
      {showResults && items?.length > 0 && (
        <ul className="flex flex-col gap-6 divide-y divide-gray-200 dark:divide-gray-700 rounded-lg bg-white dark:bg-primary-dark-700 p-6 md:gap-8 md:p-8 lg:gap-10 lg:p-10">
          {items.map(church => (
            <ChurchItem
              church={church}
              onLocate={onChurchLocate}
              showAmenities={showAmenities}
              key={church._id}
            />
          ))}
        </ul>
      )}

      {showResults && items?.length === 0 && (
        <NoResultsFound
          icon={entityTypeIsChurch ? 'church' : 'map-marker'}
          label={
            entityTypeIsChurch ? t('noChurchesFound') : t('noLocationsFound')
          }
          description={
            entityTypeIsChurch
              ? t('noChurchesFoundDescription')
              : t('noInstitutionsFoundDescription')
          }
        />
      )}

      {churchFinder?.count > 0 && pagination && (
        <Pagination total={churchFinder.count} pageSize={perPage} />
      )}
    </div>
  )
}
