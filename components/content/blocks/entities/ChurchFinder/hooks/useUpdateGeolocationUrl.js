import { useCallback } from 'react'

import omit from 'lodash/omit'
import { useRouter } from 'next/router'

import { usePageLoading } from 'ui/feedback/PageLoading'

export function useUpdateGeolocationUrl() {
  const router = useRouter()
  const { setPage } = usePageLoading()

  return useCallback(
    (center, radius) => {
      setPage(router.asPath)

      const hasValidValue = Array.isArray(center) && radius

      router.push(
        {
          pathname: router.pathname,
          query: hasValidValue
            ? {
                ...omit(router.query, ['page']),
                geolocationCenter: center.join(','),
                geolocationRadius: radius,
              }
            : {
                ...omit(router.query, [
                  'page',
                  'geolocationCenter',
                  'geolocationRadius',
                ]),
              },
        },
        undefined,
        { scroll: false }
      )
    },
    [router, setPage]
  )
}
