import { useCallback, useEffect, useMemo, useState } from 'react'

import fitMapToBounds from 'ui/data-display/Map/helpers/fitMapToBounds'

import { SEARCH_RESULT_POINT_ID } from '../constants'
import { getRadiusBounds } from '../helpers/getRadiusBounds'
import { useUpdateGeolocationUrl } from './useUpdateGeolocationUrl'

const searchRadiuses = [10, 20, 30, 40, 50] // in Kilometers

export function useChurchFinder(region, churchFinder) {
  const { longitude, latitude, boundingBox } = region || {}
  const { items, markers } = churchFinder || {}

  const [viewport, setViewport] = useState({
    longitude,
    latitude,
  })
  const [map, setMap] = useState()
  const [searchRadius, setSearchRadius] = useState(searchRadiuses[2]) // 30
  const [geolocation, setGeolocation] = useState()
  const [geocodeSearch, setGeocodeSearch] = useState('')
  const updateGeolocationUrl = useUpdateGeolocationUrl()

  const radiusBoundingBox = useMemo(
    () => getRadiusBounds(geolocation, searchRadius),
    [searchRadius, geolocation]
  )

  useEffect(() => {
    if (!(longitude && latitude)) return undefined

    setViewport({
      longitude,
      latitude,
    })
  }, [longitude, latitude])

  const allMarkers = useMemo(() => {
    const items = []

    if (geolocation) {
      items.push({
        _id: SEARCH_RESULT_POINT_ID,
        id: SEARCH_RESULT_POINT_ID,
        name: SEARCH_RESULT_POINT_ID,
        url: '',
        location: {
          coordinates: geolocation,
          center: true,
        },
        type: 'point',
      })
    }

    if (markers?.length > 0) {
      items.push(...markers)
    }

    return items
  }, [markers, geolocation])

  const onChurchLocate = useCallback(
    church => {
      if (!church?.location?.coordinates) return

      map?.flyTo?.({
        center: church.location.coordinates,
        essential: true,
        speed: 8,
        curve: 1,
        zoom: 18,
        minZoom: 10,
      })
    },
    [map]
  )

  const onResetMap = useCallback(() => {
    fitMapToBounds(map, boundingBox)
    setGeocodeSearch('')
    setGeolocation(null)

    updateGeolocationUrl()
  }, [map, boundingBox, updateGeolocationUrl])

  const onGeolocate = useCallback(
    response => {
      if (!response || typeof response.coords !== 'object') return
      const { coords } = response
      const { longitude, latitude } = coords

      if (geolocation?.[0] !== longitude || geolocation?.[1] !== latitude) {
        setGeolocation([longitude, latitude])
      }
    },
    [geolocation]
  )

  const onGeolocateChange = useCallback(
    value => {
      setGeolocation(value.center)
      setGeocodeSearch(value.place_name)

      updateGeolocationUrl(value.center, searchRadius)
    },
    [searchRadius, updateGeolocationUrl]
  )

  const onRadiusChange = useCallback(
    ({ target }) => {
      const { value } = target

      if (!Array.isArray(geolocation)) return

      setSearchRadius(value)
      updateGeolocationUrl(geolocation, value)
    },
    [geolocation, updateGeolocationUrl]
  )

  return {
    searchRadiuses,
    boundingBox,
    radiusBoundingBox,
    items,
    allMarkers,
    viewport,
    searchRadius,
    setViewport,
    setMap,
    geocodeSearch,
    setGeocodeSearch,
    onChurchLocate,
    onResetMap,
    onGeolocate,
    onGeolocateChange,
    onRadiusChange,
  }
}
