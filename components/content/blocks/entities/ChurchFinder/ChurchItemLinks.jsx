import PropTypes from 'prop-types'
import React from 'react'

import { useTranslation } from 'next-i18next'

import LabelIcon from 'ui/data-display/LabelIcon'

export function ChurchItemLinks({ church, onLocate, directionURL, siteURL }) {
  const { t } = useTranslation('church-finder')
  return (
    <div className="flex flex-col gap-y-2">
      {typeof onLocate === 'function' && (
        <LabelIcon
          label={t('locateChurch')}
          icon="map-marker"
          iconClass="text-secondary-600"
          onClick={() => onLocate(church)}
        />
      )}
      {directionURL && (
        <LabelIcon
          label={t('getChurchDirections')}
          icon="route"
          iconClass="text-secondary-600"
          to={directionURL}
        />
      )}
      {siteURL && (
        <LabelIcon
          label={t('visitChurchSite')}
          icon="external-link"
          iconClass="text-secondary-600"
          to={siteURL}
        />
      )}
    </div>
  )
}

ChurchItemLinks.propTypes = {
  directionURL: PropTypes.string,
  siteURL: PropTypes.string,
  church: PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
    image: PropTypes.object,
    siteURL: PropTypes.string,
    url: PropTypes.string,
    amenities: PropTypes.object,
    address: PropTypes.shape({
      street: PropTypes.string,
      additionalAddress: PropTypes.string,
      city: PropTypes.string,
      zip: PropTypes.string,
    }),
  }),
  onLocate: PropTypes.func,
}
