import React, { useMemo } from 'react'

import { usePageContext } from 'components/PageProvider'
import { NewletterSubscriptionForm } from 'ui/feedback/NewsletterSubscription'

/**
 * NewsletterSubscription component
 * Renders a newsletter subscription form based on the provided newsletterSubscription data.
 * @param {Object} props Component properties
 * @param {Object} props.newsletterSubscription Newsletter subscription data
 * @param {string} [props.termsPageUrl] URL for the terms and conditions
 * @param {string} [props.listsLabel] Custom label for the lists field
 * @param {Object} rest Additional properties to pass to the form
 * @returns {React.ReactElement|null} The rendered form or null if no newsletter is configured
 */
export function NewsletterSubscription({
  newsletterSubscription = {},
  termsPageUrl,
  listsLabel,
  ...rest
}) {
  const { site } = usePageContext() ?? {}
  const { fields } = newsletterSubscription

  // Extract the options of the 'lists' field if it exists
  const listOptions = useMemo(() => {
    const listsField = fields?.find(field => field.name === 'lists')
    return listsField?.options || []
  }, [fields])

  const fieldsWithLists = useMemo(() => {
    if (!fields?.length) {
      return []
    }

    // Find the 'lists' field and check its options length
    const listsField = fields.find(field => field.name === 'lists')
    if (
      listsField &&
      Array.isArray(listsField.options) &&
      listsField.options.length === 1
    ) {
      // Exclude 'lists' field if it has only one option
      return fields.filter(field => field.name !== 'lists')
    }

    // Map fields and update 'lists' label if needed
    const updatedFields = fields.map(field => {
      if (field.name === 'lists') {
        return {
          ...field,
          label: listsLabel,
        }
      }
      return field
    })

    // Find and move 'lists' field to the front if it exists
    const listsFieldIndex = updatedFields.findIndex(f => f.name === 'lists')
    if (listsFieldIndex > -1) {
      const [listsField] = updatedFields.splice(listsFieldIndex, 1)
      return [listsField, ...updatedFields]
    }

    return updatedFields
  }, [fields, listsLabel])

  if (!site?.newsletter) {
    return null
  }

  if (!site.newsletter) {
    return null
  }

  return (
    <NewletterSubscriptionForm
      {...rest}
      action="subscribe"
      termsUrl={termsPageUrl}
      fields={fieldsWithLists}
      className="w-full"
      listOptions={listOptions}
    />
  )
}

export default NewsletterSubscription
