import React from 'react'
import PropTypes from 'prop-types'

import clsx from 'clsx'
import dynamic from 'next/dynamic'

import useWidth from 'ui/helpers/useWidth'

const Picture = dynamic(() => import('ui/data-display/Picture'))

export default function Image({
  width = { xs: 'full' },
  className = '',
  ...props
}) {
  const widthClass = useWidth(width)
  return (
    <Picture
      className={clsx(widthClass, className)}
      hasCustomWidth
      {...props}
    />
  )
}
Image.propTypes = {
  className: PropTypes.string,
  width: PropTypes.object,
}
