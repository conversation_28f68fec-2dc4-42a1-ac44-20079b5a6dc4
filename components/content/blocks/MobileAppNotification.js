import React from 'react'
import PropTypes from 'prop-types'

import { useSession } from 'next-auth/react'

import { useMedianBridge } from 'hooks/useMedianBridge'

export default function MobileAppNotification() {
  const { data: session, status } = useSession()
  useMedianBridge({ session, status }) // Handles mobile app notifications

  return null
}
MobileAppNotification.propTypes = {
  code: PropTypes.string,
  blockId: PropTypes.string,
}
