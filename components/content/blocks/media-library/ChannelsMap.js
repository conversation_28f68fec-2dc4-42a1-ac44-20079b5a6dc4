import React, { useCallback, useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'
import ReactMapGL, { Marker } from 'react-map-gl'
import 'mapbox-gl/dist/mapbox-gl.css'

import useClickOutside from 'ui/helpers/useClickOutside'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const SvgWrap = dynamic(() => import('ui/icons/SvgWrap'))

const MAP_STYLE = process.env.NEXT_PUBLIC_MAP_STYLE
const MAP_TOKEN = process.env.NEXT_PUBLIC_MAP_TOKEN

const globalRegions = [
  'africa',
  'america/north-america',
  'america/inter-america',
  'america/south-america',
  'asia',
  'europe',
  'oceania',
]

const centerState = {
  longitude: 25,
  latitude: 12,
  zoom: 1.4,
}

function useChannelsWithTitleOverride(channels = [], mapOverrides = {}) {
  return channels.map(channel => ({
    ...channel,
    title: mapOverrides?.[channel.id] || channel.mapTitle || channel.title,
  }))
}

export default function ChannelsMap({ accessToken, channels, channel }) {
  const mapRef = useRef()
  const [current, setCurrent] = useState(null)

  const _channels = useChannelsWithTitleOverride(
    channels?.items,
    channel?.mapOverrides
  )

  const recenterMap = useCallback(() => {
    setCurrent(null)

    mapRef.current?.flyTo({
      center: [centerState.longitude, centerState.latitude],
      zoom: centerState.zoom,
      duration: 2000,
    })
  }, [])

  const flyToChannel = useCallback(channel => {
    if (!channel) return
    const { title, mapPoint } = channel

    const longitude = mapPoint.coordinates[0]
    const latitude = mapPoint.coordinates[1]

    mapRef.current?.flyTo({
      center: [longitude, latitude],
      zoom: mapPoint.zoom || centerState.zoom,
      duration: 1000,
    })

    setCurrent(title)
  }, [])

  return (
    <div className="relative">
      <ReactMapGL
        initialViewState={centerState}
        ref={mapRef}
        attributionControl={false}
        mapStyle={MAP_STYLE}
        mapboxAccessToken={accessToken || MAP_TOKEN}
        style={{ width: '100%', height: 650 }}
        maxZoom={4}
        maxPitch={0}
        dragRotate={false}
        scrollZoom={false}
      >
        {current && (
          <Clickable
            className="absolute bottom-2 left-2 rounded-md bg-white px-3 py-2 shadow-lg text-xl hover:text-primary-600 hover:shadow-md"
            onClick={recenterMap}
          >
            <Icon name="expand-arrows" />
          </Clickable>
        )}
        {_channels
          .filter(
            channel =>
              !channel.hideInMap && channel.mapPoint?.coordinates?.length === 2
          )
          .map((channel, i) => (
            <ChannelMarker
              channel={channel}
              isCurrent={channel.title === current}
              onClick={channel.title === current ? recenterMap : flyToChannel}
              key={`dot-marker-${i}`}
            />
          ))}
      </ReactMapGL>
      <div className="container mx-auto max-w-screen-xl px-12 py-8">
        <LinkGroups current={current} channels={_channels} />
      </div>
      <span className="mapboxgl-ctrl-logo" />
    </div>
  )
}
ChannelsMap.propTypes = {
  accessToken: PropTypes.string,
  channels: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
  }),
  channel: PropTypes.object,
}

function ChannelMarker({ channel, isCurrent, onClick }) {
  const { nodeRef, open, setOpen } = useClickOutside()
  const { mapPoint } = channel

  useEffect(() => {
    if (!open) {
      onClick(null)
    }
  }, [open, onClick])

  if (!mapPoint || !Array.isArray(mapPoint?.coordinates)) return null

  return (
    <Marker
      longitude={mapPoint.coordinates[0] || 0}
      latitude={mapPoint.coordinates[1] || 0}
      offsetLeft={-12}
      offsetTop={-12}
      style={{ zIndex: isCurrent ? 9999999 : 0 }}
    >
      <SvgWrap
        className="cursor-pointer"
        style={undefined}
        width="24"
        height="24"
        onClick={() => {
          onClick(channel)
          setOpen(true)
        }}
      >
        <circle
          r={10}
          cx={12}
          cy={12}
          fill="#f2d434"
          stroke={isCurrent ? '#f2d434' : undefined}
          strokeWidth={4}
          strokeOpacity={0.5}
        />
      </SvgWrap>
      {isCurrent && (
        <div className="absolute mt-3">
          <div className="relative -ml-[50%] mr-[50%]">
            <span className="absolute left-1/2 -top-2 ml-3 block h-4 w-4 -translate-x-1/2 rotate-45 border-l border-t bg-white" />
            <div
              className="ml-4 flex flex-col items-center space-y-2 rounded-lg border bg-white px-3 py-2 text-center shadow-md"
              ref={nodeRef}
            >
              <div className="min-w-[256px] space-y-1">
                <h3 className="font-semibold text-lg">{channel.title}</h3>
                {channel.description && (
                  <p className="leading-4 text-gray-600 text-xs">
                    {channel.description}
                  </p>
                )}
              </div>
              {channel.siteUrl && (
                <a
                  href={channel.siteUrl}
                  className="inline-flex items-center space-x-1 text-primary-600 text-sm rtl:space-x-reverse"
                >
                  <span>{channel.siteUrl}</span>
                  <Icon name="external-link" className="text-xs" />
                </a>
              )}
            </div>
          </div>
        </div>
      )}
    </Marker>
  )
}
ChannelMarker.propTypes = {
  isCurrent: PropTypes.bool,
  channel: PropTypes.shape({
    description: PropTypes.string,
    mapPoint: PropTypes.object,
    title: PropTypes.string,
    siteUrl: PropTypes.string,
  }),
  onClick: PropTypes.func,
}

function useChannelsGroups(channels = []) {
  const groupsMap = {}

  for (const globalRegion of globalRegions) {
    const [continent, region] = globalRegion.split('/')

    const groupChannels = {
      channels: channels.filter(
        channel => channel.mapRegion === globalRegion && !channel.hideInMap
      ),
    }

    if (!groupsMap[continent]) {
      groupsMap[continent] = {}
    }

    if (region) {
      groupsMap[continent][region] = groupChannels
    } else {
      groupsMap[continent] = groupChannels
    }
  }

  return groupsMap
}

function LinkGroups({ channels = [], current }) {
  const groups = useChannelsGroups(channels)

  return (
    <ul className="grid grid-cols-1 gap-3 gap-y-12 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-5 xl:gap-6">
      {Object.entries(groups).map(([name, { channels, ...regions }], i) => (
        <LinkGroup
          title={name}
          channels={channels}
          regions={regions}
          current={current}
          key={`link-group-${name}-${i}`}
        />
      ))}
    </ul>
  )
}
LinkGroups.propTypes = {
  current: PropTypes.string,
  channels: PropTypes.array,
}

function LinkGroup({
  title,
  continent,
  channels = [],
  regions,
  current,
  level,
}) {
  const { t } = useTranslation('channels-map')
  const hasChannels = channels.length > 0
  const hasRegions = Object.entries(regions).length > 0
  if (!hasChannels && !hasRegions) return null

  return (
    <li className={level === 1 ? 'space-y-1' : 'space-y-3'}>
      <span
        className={`font-semibold uppercase ${
          level === 1 ? 'text-gray-700 text-lg' : 'text-xl'
        }`}
      >
        {t(`globalRegion_${continent ? `${continent}/` : ''}${title}`)}
      </span>
      {hasRegions && (
        <ul className="space-y-4">
          {Object.entries(regions).map(
            ([name, { channels, ...regions }], i) => (
              <LinkGroup
                title={name}
                channels={channels}
                continent={title}
                regions={regions}
                current={current}
                level={1}
                key={`link-group-${name}-${i}`}
              />
            )
          )}
        </ul>
      )}
      {hasChannels && (
        <ul className="space-y-1">
          {channels.map((channel, i) => (
            <li key={`link-channel-${i}`}>
              <LinkChannel
                channel={channel}
                isCurrent={channel.title === current}
              />
            </li>
          ))}
        </ul>
      )}
    </li>
  )
}
LinkGroup.propTypes = {
  channels: PropTypes.array,
  continent: PropTypes.string,
  current: PropTypes.string,
  level: PropTypes.number,
  regions: PropTypes.object,
  title: PropTypes.string,
}

function LinkChannel({ isCurrent, channel }) {
  return (
    <a
      className={`rounded-md py-1 transition-all duration-300 ease-in-out ${
        isCurrent
          ? '-mx-2 bg-secondary-500 px-2 font-semibold text-primary-800'
          : 'text-primary-700'
      }`}
      href={channel.siteUrl || '#'}
      target="_blank"
      rel="noreferrer"
    >
      {channel.title}
    </a>
  )
}
LinkChannel.propTypes = {
  isCurrent: PropTypes.bool,
  channel: PropTypes.shape({
    mapPoint: PropTypes.object,
    title: PropTypes.string,
    siteUrl: PropTypes.string,
  }),
}
