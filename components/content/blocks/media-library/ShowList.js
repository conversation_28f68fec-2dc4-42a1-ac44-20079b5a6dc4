import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'

import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { usePageLoading } from 'ui/feedback/PageLoading'

import { isArray } from 'utils/types'

import Tag, { Tags } from 'ui/data-display/Tag'
const CardContainer = dynamic(() => import('ui/data-display/CardContainer'))
const Pagination = dynamic(() => import('ui/navigation/Pagination'))
const Select = dynamic(() => import('ui/data-entry/Select').then(m => m.Select))

import ShowCard from './shared/ShowCard'
import { useCallback } from 'react'

export default function ShowList({
  limit = 10,
  shows = { items: [], count: 0 },
  showFilters,
  showDescription = true,
  enableAnimations,
}) {
  const { t } = useTranslation('media-library')
  const router = useRouter()
  const { setPage } = usePageLoading()

  const onAddFilter = useCallback(
    (type, selectedId) => {
      const ids = router.query[type]?.split(',') || []
      if (!ids.includes(selectedId)) {
        ids.push(selectedId)
      }
      delete router.query.page

      setPage(router.asPath)

      router.push({
        pathname: router.pathname,
        query: { ...router.query, [type]: ids.join(',') },
      })
    },
    [router, setPage]
  )

  const onRemoveFilter = (type, selectedId) => {
    let ids = router.query[type]?.split(',') || []
    ids = ids.filter(id => id !== selectedId)
    if (ids.length === 0) delete router.query[type]
    delete router.query.page
    setPage(router.asPath)
    router.push({
      pathname: router.pathname,
      query: {
        ...router.query,
        ...(ids.length > 0 ? { [type]: ids.join(',') } : {}),
      },
    })
  }

  const onClearAllFilters = () => {
    Object.keys(showFilters).forEach(filterType => {
      delete router.query[filterType]
    })
    delete router.query.page
    setPage(router.asPath)
    router.push({
      pathname: router.pathname,
      query: router.query,
    })
  }

  const filterCount = Object.keys(showFilters).reduce((acc, filterType) => {
    const selectedFilters = showFilters[filterType]?.filter(filterOption =>
      router.query[filterType]?.split(',')?.includes(filterOption.id)
    )
    return isArray(selectedFilters) ? acc + selectedFilters.length : acc
  }, 0)

  return (
    <div className="space-y-8">
      <div className="hope-filters flex flex-col space-y-2 rtl:space-x-reverse sm:flex-1 sm:flex-row sm:space-x-2 sm:space-y-0">
        {Object.keys(showFilters).map((filterType, key) => {
          const options = showFilters[filterType].map(filterOption => ({
            label: `${filterOption.title} (${filterOption.count})`,
            value: filterOption.id,
            disabled: router.query[filterType]
              ?.split(',')
              ?.includes(filterOption.id),
          }))
          if (options.length <= 1) return null
          return (
            <Select
              key={key}
              onChange={e => {
                const selectedId = e.target.value
                onAddFilter(filterType, selectedId)
              }}
              options={options}
              placeholder={t(`filterBy-${filterType}`)}
            />
          )
        })}
        {/* <div className="text-gray-400 px-2">|</div>
        <div>
          <Select
            onChange={() => {}}
            options={[
              { label: 'A - Z', value: 'a-z' },
              { label: 'Z - A', value: 'z-a' },
            ]}
          />
        </div> */}
      </div>
      {filterCount > 0 && (
        <div className="flex flex-col space-y-1 sm:flex-row sm:items-center sm:space-x-2 sm:space-y-0 rtl:sm:space-x-reverse">
          <div className="whitespace-nowrap dark:text-gray-300">
            {t('common:yourFilters')}:
          </div>
          <Tags>
            {Object.keys(showFilters).map(filterType => {
              return showFilters[filterType]
                .filter(filterOption =>
                  router.query[filterType]
                    ?.split(',')
                    ?.includes(filterOption.id)
                )
                .map(filterOption => (
                  <Tag
                    key={filterOption.id}
                    label={filterOption.title}
                    deletable
                    variant="filter"
                    onClick={() => onRemoveFilter(filterType, filterOption.id)}
                  />
                ))
            })}
          </Tags>
          {filterCount > 1 && (
            <button
              className="cursor-pointer whitespace-nowrap text-primary-700 hover:text-danger-600 dark:text-secondary-500 dark:hover:text-secondary-500/90"
              onClick={onClearAllFilters}
              type="button"
            >
              {t('common:clearAll')}
            </button>
          )}
        </div>
      )}
      <CardContainer>
        {shows?.items.map(show => (
          <ShowCard
            show={show}
            showDescription={showDescription}
            key={show._id}
            enableAnimation={enableAnimations}
          />
        ))}
      </CardContainer>
      <Pagination
        itemsLabel={t('shows')}
        total={shows?.count}
        pageSize={limit}
      />
    </div>
  )
}
ShowList.propTypes = {
  limit: PropTypes.number,
  shows: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
  }),
  showFilters: PropTypes.object,
  showDescription: PropTypes.bool,
}
