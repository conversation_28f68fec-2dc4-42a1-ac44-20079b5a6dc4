import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import PropTypes from 'prop-types'

import {
  formatISO,
  intervalToDuration,
  isToday,
  add,
  addDays,
  subDays,
  startOfDay,
} from 'date-fns'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'

import { usePageLoading } from 'ui/feedback/PageLoading'
import { FormatDate } from 'utils/datetime'
import { isEmpty } from 'utils/objects'
import useSchedule from './hooks/useSchedule'
import { usePageContext } from 'components/PageProvider'
import Alert from 'ui/feedback/Alert'
import Loading from 'ui/feedback/Loading'

const Button = dynamic(() => import('ui/buttons/Button'))
const Image = dynamic(() => import('ui/data-display/Image'))
const DatePicker = dynamic(() =>
  import('ui/data-entry/DateTime').then(m => m.DatePicker)
)
const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Popover = dynamic(() => import('ui/feedback/Popover'))
const Heading = dynamic(() => import('ui/typography/Heading'))

const ScheduleDayContext = React.createContext()

function useScheduleDay() {
  return useContext(ScheduleDayContext)
}

export const broadcastShape = {
  title: PropTypes.string,
  description: PropTypes.string,
  image: PropTypes.object,
  startsAt: PropTypes.string,
  endsAt: PropTypes.string,
  type: PropTypes.oneOf(['']), // TODO: what kind of broadcats exist? (the design has different colors for broadcasts)
}

export default function ScheduleDay({
  channel,
  episodeDetailPageId,
  // broadcast,
}) {
  const { t } = useTranslation(['media-library', 'common'])
  const router = useRouter()
  const { site } = usePageContext()
  const { setPage } = usePageLoading()
  const [calendarDate, setCalendarDate] = useState(new Date())
  const [currentBroadcast, setCurrentBroadcast] = useState()

  const day = useMemo(
    () =>
      router.query.day ? new Date(router.query.day) : startOfDay(new Date()),
    [router.query.day]
  )

  const { broadcasts, error, loading } = useSchedule(
    {
      channelId: channel.id,
      episodeDetailPageId,
      siteId: site.id,
      from: formatISO(day),
      to: formatISO(add(day, { hours: 23, minutes: 59, seconds: 59 })),
    }
    // {
    //   onSuccess: ({ items, count }) => {
    //     setCurrentBroadcast(broadcasts)

    //     var broadcastElement = document.getElementById(broadcast?.id)

    //     // Scroll to the broadcast element to make it visible
    //     broadcastElement?.scrollIntoView({
    //       behavior: 'smooth',
    //       block: 'center',
    //     })
    //   },
    // }
  )

  const handleClick = useCallback(
    date => () => {
      setPage(router.asPath) // triggers "loading"

      const query = {
        slug: router.query.slug,
      }

      if (date) {
        query.day = formatISO(startOfDay(date))
      }

      router.push(
        {
          pathname: router.pathname,
          query,
        },
        null,
        { scroll: false }
      )
    },
    [router, setPage]
  )

  const handleDatePicker = useCallback(() => {
    const query = {}

    query.day = formatISO(calendarDate, { representation: 'date' })

    setPage(router.asPath)

    router.push(
      {
        pathname: router.pathname,
        query: {
          slug: router.query.slug,
          ...query,
        },
      },
      null,
      { scroll: false }
    )
  }, [calendarDate, router, setPage])

  if (!channel) return null

  return (
    <ScheduleDayContext.Provider
      value={{
        currentBroadcast,
        setCurrentBroadcast,
      }}
    >
      <div className="w-full space-y-8">
        <div className="flex flex-col items-start justify-between space-y-8 sm:flex-row sm:space-x-8 sm:space-y-0 rtl:sm:space-x-reverse">
          <div>
            <Heading className="space-x-2 rtl:space-x-reverse" as="h2">
              {isToday(day) && (
                <span className="text-gray-500">{t('today')}:</span>
              )}
              <span>
                <FormatDate date={day} format="PPPP" />
              </span>
            </Heading>
          </div>
          <div className="flex flex-row space-x-2 rtl:space-x-reverse">
            <Button
              icon="chevron-left"
              variant="hollow"
              className="rtl:rotate-180"
              size="sm"
              onClick={handleClick(subDays(day, 1))}
            />
            {!isToday(day) && (
              <Button
                variant="hollow"
                label={t('today')}
                size="sm"
                onClick={handleClick()}
              />
            )}
            <Popover
              trigger={<Button variant="hollow" icon="calendar" size="sm" />}
              triggerAs="div"
            >
              {({ close }) => (
                <div className="flex flex-col space-y-5 p-4">
                  <DatePicker
                    date={calendarDate}
                    onChange={date => setCalendarDate(date)}
                  />
                  <Button
                    variant="primary"
                    label={t('common:go')}
                    onClick={() => {
                      handleDatePicker()
                      close()
                    }}
                  />
                </div>
              )}
            </Popover>
            <Button
              icon="chevron-right"
              variant="hollow"
              className="rtl:rotate-180"
              size="sm"
              onClick={handleClick(addDays(day, 1))}
            />
          </div>
        </div>

        {error && (
          <Alert
            title={t('scheduleErrorTitle')}
            message={t('scheduleErrorMessage')}
            type="danger"
          />
        )}

        {loading && (
          <div className="rounded-lg border border-dashed border-gray-300 p-6 text-center text-gray-500">
            <Loading />
          </div>
        )}

        {!loading && !error && (
          <div className="relative">
            {broadcasts?.length > 0 ? (
              broadcasts.map(broadcast => (
                <Broadcast
                  broadcast={broadcast}
                  key={`broadcast-${broadcast.id}`}
                />
              ))
            ) : (
              <p className="rounded-lg border border-dashed border-gray-300 p-6 text-center text-gray-500 text-lg">
                {t('noBroadcasts')}
              </p>
            )}
          </div>
        )}
      </div>
    </ScheduleDayContext.Provider>
  )
}
ScheduleDay.propTypes = {
  broadcast: PropTypes.shape(broadcastShape),
  channel: PropTypes.object,
  episodeDetailPageId: PropTypes.string,
}

function Broadcast({ broadcast }) {
  const { t } = useTranslation('media-library')
  const router = useRouter()
  const { currentBroadcast, setCurrentBroadcast } = useScheduleDay()
  const startsAt = useMemo(() => new Date(broadcast.startsAt), [broadcast])
  const endsAt = useMemo(() => new Date(broadcast.endsAt), [broadcast])

  const { episode } = broadcast

  const isCurrent = currentBroadcast?.id === broadcast.id
  const duration = intervalToDuration({ start: startsAt, end: endsAt })
  const durationMinutes = duration.minutes + ((duration.hours ?? 0) * 60)

  useEffect(() => {
    if (router.query?.broadcastId === broadcast.id) {
      setCurrentBroadcast(broadcast)
    }
  }, [broadcast, router.query?.broadcastId, setCurrentBroadcast])

  const handleClick = useCallback(() => {
    setCurrentBroadcast(isCurrent ? undefined : broadcast)

    router.push(
      {
        pathname: router.pathname,
        query: {
          ...router.query,
          broadcastId: isCurrent ? undefined : broadcast.id,
        },
      },
      null,
      { scroll: false }
    )
  }, [broadcast, setCurrentBroadcast, isCurrent, router])

  const showTitle = episode?.show ? episode.show.title : broadcast.showTitle
  const episodeTitle = episode ? episode.title : broadcast.episodeTitle

  return (
    <Clickable
      className={`group py-1 outline-none transition-all duration-300 ease-in-out ${
        isCurrent ? 'z-20' : 'z-10 hover:z-30 focus:z-30'
      }`}
      onClick={handleClick}
      id={broadcast.id}
    >
      <div
        className={`flex flex-row justify-between space-x-4 rounded-lg p-4 ring-inset ring-primary-400 transition-all duration-300 ease-in-out group-focus:ring-2 rtl:space-x-reverse md:space-x-6  ${
          isCurrent
            ? `bg-primary-700`
            : 'bg-white group-hover:bg-primary-50 group-focus:bg-primary-100'
        }`}
      >
        <div className="md:shrink-0">
          <div
            className={`text-right font-semibold ${
              isCurrent ? 'text-primary-100' : 'text-gray-600'
            }`}
          >
            <FormatDate date={startsAt} format="p" />
          </div>
        </div>
        <div className="flex w-full flex-grow flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0 rtl:md:space-x-reverse">
          <div className="flex flex-grow flex-col space-y-4">
            <div>
              <h3
                className={`font-bold leading-5 text-lg ${
                  isCurrent ? 'text-primary-50' : 'text-gray-800'
                }`}
              >
                {showTitle}
              </h3>
              <p
                className={`leading-5 ${
                  isCurrent ? 'text-primary-100' : 'text-gray-600'
                }`}
              >
                {episodeTitle}
              </p>
            </div>
            {Boolean(episode?.url) && (
              <div className="justify-self-end">
                <Button
                  label={t('watchNow')}
                  size="xs"
                  icon="play"
                  variant="alt"
                  url={episode.url}
                />
              </div>
            )}
          </div>
          <div>
            {episode?.image && !isEmpty(episode.image) && (
              <Image
                className="w-40 rounded"
                alt={episodeTitle}
                file={episode.image}
                sizes="md:384px sm:256px 200px"
                lazy
                quality={50}
              />
            )}
          </div>
        </div>

        <div className="md:shrink-0">
          <span
            className={`${
              isCurrent ? 'font-semibold text-primary-100' : 'text-gray-500'
            }`}
          >
            {t('durationInMinutes', { duration: durationMinutes })}
          </span>
        </div>
      </div>
    </Clickable>
  )
}
Broadcast.propTypes = {
  broadcast: PropTypes.shape(broadcastShape),
}
