import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const Image = dynamic(() => import('ui/data-display/Image'))

export function MetaItem({ title, value }) {
  return value ? (
    <div className="flex">
      <div className="w-1/3 font-bold">{title}</div>
      <div className="w-2/3">{value}</div>
    </div>
  ) : null
}
MetaItem.propTypes = {
  title: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}

export default function VideoCategories({ showImage, videoParent }) {
  const { t } = useTranslation('media-library')
  if (!videoParent) return null

  const { episode, show } = videoParent

  const image = episode ? episode.image : show.images?.default
  const imageTitle = episode ? episode.title : show.title

  return (
    <div className="w-full space-y-4 text-gray-500">
      {showImage && image && (
        <Image file={image} alt={imageTitle} className="rounded-lg" />
      )}
      <div>
        {episode?.title && (
          <MetaItem title={t('episode')} value={episode.title} />
        )}
        {show?.title && <MetaItem title={t('show')} value={show.title} />}
      </div>
    </div>
  )
}
VideoCategories.propTypes = {
  showImage: PropTypes.bool,
  videoParent: PropTypes.object,
}
