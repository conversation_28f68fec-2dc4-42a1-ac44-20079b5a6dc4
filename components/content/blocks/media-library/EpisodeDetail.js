import { useMemo } from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const RichText = dynamic(() => import('ui/typography/RichText'))

export default function EpisodeDetail({
  episodeDetail,
  showAbstract,
  showBody,
  showShowTitle,
  showTitle,
  showViews,
}) {
  const {
    abstract,
    body,
    plays,
    show,
    title,
    views = {},
    provider,
  } = episodeDetail

  const episodeViews = useMemo(() => {
    if (provider === 'youtube') {
      return views[provider] // YouTube terms dictate that only YT statistics should be shown
    }
    const providerViews = Object.entries(views).reduce((acc, [key, value]) => {
      if (key === 'youtube') {
        return acc
      }
      // Other providers can be aggregated into a single value
      return acc + value
    }, 0)

    if (providerViews === 0 && plays > 0) {
      return plays
    }

    return providerViews
  }, [plays, provider, views])

  if (!episodeDetail) return null

  return (
    <div className="w-full space-y-6">
      {(showShowTitle || showTitle || showViews) && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            {showShowTitle && (
              <h3 className="font-bold uppercase leading-loose text-lg dark:text-primary-dark-100">
                {show?.title}
              </h3>
            )}
            {showViews && episodeViews > 0 && (
              <div className="rounded bg-success-200 py-0.5 px-2 font-bold uppercase text-gray-800 text-sm">
                {episodeViews} views
              </div>
            )}
          </div>
          {showTitle && (
            <h1 className="font-extrabold text-3xl dark:text-primary-dark-300">
              {title}
            </h1>
          )}
        </div>
      )}
      {showAbstract && abstract && (
        <p className="font-serif italic leading-6 text-gray-500 dark:text-gray-300 text-lg">
          {abstract}
        </p>
      )}
      {showBody && body && <RichText doc={body} />}
    </div>
  )
}
EpisodeDetail.propTypes = {
  episodeDetail: PropTypes.object,
  showAbstract: PropTypes.bool,
  showBody: PropTypes.bool,
  showShowTitle: PropTypes.bool,
  showTitle: PropTypes.bool,
  showViews: PropTypes.bool,
}
