import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'

export function MetaItem({ title, value }) {
  return value ? (
    <div className="flex">
      <div className="w-1/3 font-bold">{title}</div>
      <div className="w-2/3">{value}</div>
    </div>
  ) : null
}
MetaItem.propTypes = {
  title: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
}

export default function ShowMeta({
  className = '',
  showMetaData = {
    hosts: [],
    categories: [],
    language: null,
    seasonsCount: 0,
    episodesCount: 0,
  },
  showTitle,
  showHosts,
  showCategories,
  showLanguage,
  showSeasons,
  showEpisodes,
}) {
  const { t } = useTranslation('media-library')

  const { hosts, categories, language, seasonsCount, title, episodesCount } =
    showMetaData

  return (
    <div className={`w-full text-gray-500 dark:text-gray-100 ${className}`}>
      {showTitle && title && <MetaItem title={t('show')} value={title} />}
      {showHosts && hosts && (
        <MetaItem
          title={t('hosts')}
          value={hosts.map(host => host.fullName).join(', ')}
        />
      )}
      {showCategories && categories && (
        <MetaItem
          title={t('categories')}
          value={categories.map(category => category.title).join(', ')}
        />
      )}
      {showLanguage && language && (
        <MetaItem title={t('language')} value={language} />
      )}
      {showSeasons && seasonsCount > 0 && (
        <MetaItem title={t('seasons')} value={seasonsCount} />
      )}
      {showEpisodes && episodesCount > 0 && (
        <MetaItem title={t('episodes')} value={episodesCount} />
      )}
    </div>
  )
}
ShowMeta.propTypes = {
  className: PropTypes.string,
  showMetaData: PropTypes.object,
  showTitle: PropTypes.bool,
  showHosts: PropTypes.bool,
  showCategories: PropTypes.bool,
  showLanguage: PropTypes.bool,
  showSeasons: PropTypes.bool,
  showEpisodes: PropTypes.bool,
}
