import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

import { formatBytes } from 'utils/strings'

const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const LinkItem = dynamic(() =>
  import('ui/navigation/LinkList').then(m => m.LinkItem)
)

export default function EpisodeDocuments({
  className = '',
  episodeDocuments = [],
  title,
}) {
  const { t } = useTranslation('media-library')

  if (!episodeDocuments?.length) return null

  return (
    <div className={`w-full space-y-4 ${className}`}>
      <LinkList title={title || t('documents')}>
        {episodeDocuments
          .filter(episodeDocument => episodeDocument.file)
          .map((episodeDocument, key) => {
            const { file, title } = episodeDocument
            return (
              <LinkItem
                icon="file-invoice"
                key={key}
                label={title}
                extra={formatBytes(file.size, 0)}
                file={file}
                type="download"
              />
            )
          })}
      </LinkList>
    </div>
  )
}
EpisodeDocuments.propTypes = {
  className: PropTypes.string,
  episodeDocuments: PropTypes.array,
  title: PropTypes.string,
}
