import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const LinkItem = dynamic(() =>
  import('ui/navigation/LinkList').then(m => m.LinkItem)
)

export default function ShowPodcasts({
  className = '',
  showPodcasts = [],
  title,
}) {
  const { t } = useTranslation('media-library')

  if (!showPodcasts?.length) return null

  return (
    <div className={`w-full space-y-4 ${className}`}>
      <LinkList title={title || t('podcasts')}>
        {showPodcasts.map((podcast, key) => {
          const { title, url } = podcast
          const icon = title.toLowerCase().includes('apple')
            ? 'podcast'
            : title.toLowerCase().includes('spotify')
              ? 'spotify'
              : 'video'
          return <LinkItem icon={icon} key={key} label={title} url={url} />
        })}
      </LinkList>
    </div>
  )
}
ShowPodcasts.propTypes = {
  className: PropTypes.string,
  showPodcasts: PropTypes.array,
  title: PropTypes.string,
}
