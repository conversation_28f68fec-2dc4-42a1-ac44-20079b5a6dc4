import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const CardContainer = dynamic(() => import('ui/data-display/CardContainer'))
const Card = dynamic(() => import('ui/data-display/Card'))

export default function EpisodesTeaser({
  enableAnimation,
  teaserEpisodes = { items: [], count: 0 },
  title,
}) {
  return (
    <CardContainer title={title}>
      {teaserEpisodes?.items.map(episode => {
        const { id, image, title, url } = episode
        return (
          <Card
            className="mb-6 sm:w-1/2 md:w-1/3 lg:w-1/4"
            key={id}
            image={image}
            title={title}
            url={url}
            enableAnimation={enableAnimation}
          />
        )
      })}
    </CardContainer>
  )
}
EpisodesTeaser.propTypes = {
  teaserEpisodes: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
  }),
  title: PropTypes.string,
}
