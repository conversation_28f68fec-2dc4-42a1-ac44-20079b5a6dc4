import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const Player = dynamic(() => import('ui/data-display/Player'))

export default function EpisodePlayer({ episodePlayer, fullSize }) {
  const { t } = useTranslation('media-library')

  if (!episodePlayer) return null

  const { error, provider, playlist, playlistIndex, poster } = episodePlayer
  let id = episodePlayer.id

  if (error) {
    return (
      <div className="w-full py-8 text-center font-extralight text-gray-300 text-2xl">
        {t('videoMissing')}
      </div>
    )
  }

  if (provider === 'jetstream' && id && !id.startsWith('jsv:')) {
    id = `jsv:${id}`
  }

  if (provider === 'jetstream-live' && id && !id.startsWith('jsl:')) {
    id = `jsl:${id}`
  }

  return (
    <Player
      fullSize={fullSize}
      id={id}
      key={id}
      playlist={playlist}
      playlistIndex={playlistIndex}
      poster={poster}
      provider={provider}
      sources={[{ src: id }]}
    />
  )
}
EpisodePlayer.propTypes = {
  episodePlayer: PropTypes.object,
  fullSize: PropTypes.bool,
}
