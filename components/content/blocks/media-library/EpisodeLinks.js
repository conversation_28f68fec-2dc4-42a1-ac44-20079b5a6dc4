import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const LinkItem = dynamic(() =>
  import('ui/navigation/LinkList').then(m => m.LinkItem)
)

export default function EpisodeLinks({
  className = '',
  episodeLinks = [],
  title,
}) {
  const { t } = useTranslation('media-library')

  if (!episodeLinks?.length) return null

  return (
    <div className={`w-full space-y-4 ${className}`}>
      <LinkList title={title || t('links')}>
        {episodeLinks
          .filter(episodeLink => episodeLink.url)
          .map((episodeLink, key) => {
            const { title, url } = episodeLink
            return <LinkItem icon="link" key={key} label={title} url={url} />
          })}
      </LinkList>
    </div>
  )
}
EpisodeLinks.propTypes = {
  className: PropTypes.string,
  episodeLinks: PropTypes.array,
  title: PropTypes.string,
}
