import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const CardContainer = dynamic(() => import('ui/data-display/CardContainer'))

import EpisodeCard from './shared/EpisodeCard'

export default function RelatedEpisodes({
  relatedEpisodes,
  title,
  showDescription = true,
  enableAnimations,
}) {
  const { t } = useTranslation('media-library')

  if (!relatedEpisodes?.length) return null

  return (
    <CardContainer title={title ? title : t('relatedEpisodes')}>
      {relatedEpisodes.map(episode => (
        <EpisodeCard
          episode={episode}
          key={episode._id}
          showDescription={showDescription}
          showKicker
          enableAnimation={enableAnimations}
        />
      ))}
    </CardContainer>
  )
}
RelatedEpisodes.propTypes = {
  relatedEpisodes: PropTypes.array,
  title: PropTypes.string,
  showDescription: PropTypes.bool,
}
