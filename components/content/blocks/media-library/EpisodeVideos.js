import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const CardContainer = dynamic(() => import('ui/data-display/CardContainer'))
const List = dynamic(() => import('ui/data-display/List'))

const VideoCard = dynamic(() => import('./shared/VideoCard'))
const VideoListItem = dynamic(() => import('./shared/VideoListItem'))

export default function EpisodeVideos({
  episodeVideos,
  displayMode = 'list',
  enableAnimations,
}) {
  const { t } = useTranslation('media-library')

  if (!episodeVideos) return null

  const { title, videos } = episodeVideos

  if (displayMode === 'list') {
    return (
      <div className="flex flex-col space-y-4">
        <List title={title || t('videos')}>
          {videos?.map(video => (
            <VideoListItem key={video.id} video={video} />
          ))}
        </List>
      </div>
    )
  } else {
    return (
      <CardContainer title={title || t('videos')}>
        {videos.map(video => (
          <VideoCard
            key={video.id}
            video={video}
            enableAnimation={enableAnimations}
          />
        ))}
      </CardContainer>
    )
  }
}
EpisodeVideos.propTypes = {
  displayMode: PropTypes.oneOf(['cards', 'list']),
  episodeVideos: PropTypes.object,
  title: PropTypes.string,
}
