import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const Heading = dynamic(() => import('ui/typography/Heading'))
const Image = dynamic(() => import('ui/data-display/Image'))
const ConditionalLink = dynamic(() =>
  import('ui/navigation/Link').then(m => m.ConditionalLink)
)

export default function ShowParticipants({ showParticipants }) {
  const { t } = useTranslation('media-library')

  const { participants, title } = showParticipants || {}

  if (!participants?.length) return null

  return (
    <div className="space-y-4">
      <Heading as="h5" title={title || t('participants')} capitalized />
      <div className="-m-4 flex flex-wrap">
        {participants.map((participant, key) => {
          const { avatar, fullName, url } = participant
          return (
            <div className="m-4 w-16 shrink-0 space-y-2 md:w-24" key={key}>
              {avatar ? (
                <Image
                  alt={fullName}
                  aspectRatio="1/1"
                  file={avatar}
                  className="rounded-full"
                  sizes="md:96px 64px"
                  url={url}
                />
              ) : (
                <ConditionalLink condition={url} to={url}>
                  <div className="h-16 w-16 rounded-full bg-gray-200 md:h-24 md:w-24" />
                </ConditionalLink>
              )}
              <h3 className="text-center leading-tight">{fullName}</h3>
            </div>
          )
        })}
      </div>
    </div>
  )
}
ShowParticipants.propTypes = {
  Show: PropTypes.object,
  showParticipants: PropTypes.object,
}
