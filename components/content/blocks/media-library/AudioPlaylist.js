import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { getProxiedDocumentUrl } from 'utils/documents'
import { usePageContext } from 'components/PageProvider'
import { useMemo } from 'react'

const UIAudioPlaylist = dynamic(() => import('ui/data-display/AudioPlaylist'))

export default function AudioPlaylist({
  id,
  className,
  image,
  title,
  items = [],
  showImage,
  showTitle,
  showKicker,
  showSubtitle,
  showAbstract,
  showDescription,
  showPlaylistAbstract,
  variant,
}) {
  const { site } = usePageContext()

  const playlist = useMemo(
    () =>
      items.map(({ file, ...rest }, index) => ({
        ...rest,
        id: `${index}-${file.name}`,
        sources: [
          {
            src: getProxiedDocumentUrl(file, site.entity),
            type: file.mime,
            duration: file.duration,
          },
        ],
      })),
    [items, site.entity]
  )

  return (
    <UIAudioPlaylist
      id={id}
      className={className}
      image={showImage ? image : undefined}
      title={showSubtitle ? title : undefined}
      playlist={playlist}
      showTitle={showTitle}
      showKicker={showKicker}
      showAbstract={showAbstract}
      showDescription={showDescription}
      showPlaylistAbstract={showPlaylistAbstract}
      variant={variant ? variant : 'lg'}
    />
  )
}
AudioPlaylist.propTypes = {
  id: PropTypes.string,
  className: PropTypes.string,
  image: PropTypes.object,
  title: PropTypes.string,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      file: PropTypes.object,
      title: PropTypes.string,
      kicker: PropTypes.string,
      abstract: PropTypes.string,
      description: PropTypes.object,
    })
  ),
  showImage: PropTypes.bool,
  showTitle: PropTypes.bool,
  showKicker: PropTypes.bool,
  showSubtitle: PropTypes.bool,
  showAbstract: PropTypes.bool,
  showDescription: PropTypes.bool,
  showPlaylistAbstract: PropTypes.bool,
  variant: PropTypes.oneOf(['sm', 'md', 'lg']),
}
