import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import PropTypes from 'prop-types'

import {
  format,
  intervalToDuration,
  formatISO,
  set,
  getDay,
  getHours,
  addDays,
  startOfWeek,
  isSameDay,
  subDays,
} from 'date-fns'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'

import { usePageLoading } from 'ui/feedback/PageLoading'
import useToggle from 'ui/helpers/useToggle'
import { range } from 'utils/arrays'
import { isEmpty } from 'utils/objects'
import useBreakpoint, { useIsSmallScreen } from 'ui/helpers/useBreakpoint'
import useSchedule from './hooks/useSchedule'
import { usePageContext } from 'components/PageProvider'
import {
  firstDayOfWeek,
  formatDate,
  FormatDate,
  lastDayOfWeek,
  useDatetimeLocale,
} from 'utils/datetime'
import Alert from 'ui/feedback/Alert'
import Loading from 'ui/feedback/Loading'
import clsx from 'clsx'

const Button = dynamic(() => import('ui/buttons/Button'))
const Image = dynamic(() => import('ui/data-display/Image'))
const DatePicker = dynamic(() =>
  import('ui/data-entry/DateTime').then(m => m.DatePicker)
)

const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Popover = dynamic(() => import('ui/feedback/Popover'))
const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))
const Heading = dynamic(() => import('ui/typography/Heading'))

const ScheduleWeekContext = React.createContext()

const today = set(new Date(), { hours: 0, minutes: 0, seconds: 0 })

function useScheduleWeek() {
  return useContext(ScheduleWeekContext)
}

function useWeeklyHours() {
  return useMemo(() => {
    const weekdays = range(0, 6)
    return weekdays.map(() => range(0, 23))
  }, [])
}

function useBroadcastHours(broadcasts = []) {
  const broadcastsHours = {}

  for (const broadcast of broadcasts) {
    const startsAt = new Date(broadcast.startsAt)
    const dayOfWeek = startsAt.getDay()
    const startHour = startsAt.getHours()

    if (!broadcastsHours[dayOfWeek]) {
      broadcastsHours[dayOfWeek] = []
    }

    if (!broadcastsHours[dayOfWeek][startHour]) {
      broadcastsHours[dayOfWeek][startHour] = []
    }

    broadcastsHours[dayOfWeek][startHour].push(broadcast)
  }
  return broadcastsHours
}

const broadcastShape = {
  title: PropTypes.string,
  description: PropTypes.string,
  image: PropTypes.object,
  startsAt: PropTypes.string,
  endsAt: PropTypes.string,
}

export default function ScheduleWeek({
  channel,
  episodeDetailPageId,
  broadcastsWeek,
  broadcast,
}) {
  const router = useRouter()
  const { site } = usePageContext()
  const { setPage } = usePageLoading()
  const { t } = useTranslation(['media-library', 'common'])
  const [calendarDate, setCalendarDate] = useState(new Date())
  const [currentBroadcast, setCurrentBroadcast] = useState()
  const days = useWeeklyHours()
  const { daySchedulePage } = broadcastsWeek

  const from = useMemo(
    () =>
      router.query.weekStart
        ? firstDayOfWeek(new Date(router.query.weekStart))
        : firstDayOfWeek(),
    [router.query.weekStart]
  )
  const to = useMemo(() => lastDayOfWeek(from), [from])

  const scheduleContext = useMemo(
    () => ({
      currentBroadcast,
      setCurrentBroadcast,
    }),
    [currentBroadcast]
  )

  const { broadcasts, error, loading } = useSchedule(
    {
      channelId: channel.id,
      episodeDetailPageId,
      siteId: site.id,
      from: formatISO(from),
      to: formatISO(to),
    },
    {
      onSuccess: ({ broadcasts }) => {
        setCurrentBroadcast(broadcasts)

        var broadcastElement = document.getElementById(broadcast?.id)

        // Scroll to the broadcast element to make it visible
        broadcastElement?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        })
      },
    }
  )

  const broadcastsHours = useBroadcastHours(broadcasts)

  const handleClick = useCallback(
    date => () => {
      const query = {}

      if (date) {
        query.weekStart = formatISO(date)
      }

      setPage(router.asPath)

      router.push(
        {
          pathname: router.pathname,
          query: {
            slug: router.query.slug,
            ...query,
          },
        },
        null,
        { scroll: false }
      )
    },
    [router, setPage]
  )

  const handleDatePicker = useCallback(() => {
    const query = {}

    query.weekStart = formatISO(startOfWeek(calendarDate))

    setPage(router.asPath)

    router.push(
      {
        pathname: router.pathname,
        query: {
          slug: router.query.slug,
          ...query,
        },
      },
      null,
      { scroll: false }
    )
  }, [calendarDate, router, setPage])

  if (!channel) return null

  return (
    <ScheduleWeekContext.Provider value={scheduleContext}>
      <div className="m-auto max-w-screen-3xl space-y-8">
        <div className="flex flex-col space-y-6 p-6 lg:flex-row lg:items-center lg:justify-between lg:space-x-8 lg:space-y-0 rtl:lg:space-x-reverse">
          <Heading className="space-x-2 rtl:space-x-reverse" as="h2">
            <span>{t('week')}:</span>
            <span>
              <FormatDate date={from} format="PP" />
            </span>
            <span>-</span>
            <span>
              <FormatDate date={to} format="PP" />
            </span>
          </Heading>
          <div className="flex flex-row space-x-2 rtl:space-x-reverse">
            <Button
              icon="chevron-left"
              variant="hollow"
              className="rtl:rotate-180"
              size="sm"
              onClick={handleClick(subDays(from, 7))}
            />

            <Popover
              trigger={<Button variant="hollow" icon="calendar" size="sm" />}
              triggerAs="div"
            >
              {({ close }) => (
                <div className="flex flex-col space-y-5 p-4">
                  <DatePicker
                    date={calendarDate}
                    onChange={date => setCalendarDate(date)}
                  />
                  <Button
                    variant="primary"
                    label={t('common:go')}
                    onClick={() => {
                      handleDatePicker()
                      close()
                    }}
                  />
                </div>
              )}
            </Popover>
            <Button
              variant="hollow"
              label={t('thisWeek')}
              size="sm"
              onClick={handleClick()}
              disabled={isSameDay(from, startOfWeek(today))}
            />
            <Button
              icon="chevron-right"
              variant="hollow"
              className="rtl:rotate-180"
              size="sm"
              onClick={handleClick(addDays(from, 7))}
            />
          </div>
        </div>

        {error && (
          <Alert
            title={t('scheduleErrorTitle')}
            message={t('scheduleErrorMessage')}
            type="danger"
          />
        )}

        {loading && (
          <div className="rounded-lg border border-dashed border-gray-300 p-6 text-center text-gray-500">
            <Loading />
          </div>
        )}

        {!loading && !error && (
          <div className="space-y-4">
            <div className="relative flex flex-col divide-y lg:flex-row lg:divide-y-0">
              <div className="mt-16 hidden w-24 lg:block">
                {range(0, 23).map(hour => (
                  <Hour
                    time={set(today, { hours: hour })}
                    showTime
                    key={`hour-${hour}`}
                  />
                ))}
              </div>
              {days.map((hours, dayOfWeek) => (
                <Day
                  date={addDays(from, dayOfWeek)}
                  daySchedulePath={daySchedulePage?.path}
                  broadcasts={broadcastsHours[dayOfWeek]}
                  hours={hours}
                  key={`day-${dayOfWeek}`}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </ScheduleWeekContext.Provider>
  )
}
ScheduleWeek.propTypes = {
  broadcastsWeek: PropTypes.shape({
    items: PropTypes.arrayOf(PropTypes.shape(broadcastShape)),
    count: PropTypes.number,
    weekStart: PropTypes.string,
    daySchedulePage: PropTypes.object,
  }),
  broadcast: PropTypes.shape(broadcastShape),
  channel: PropTypes.object,
  episodeDetailPageId: PropTypes.string,
}

function Day({ date, hours, broadcasts, daySchedulePath }) {
  const [open, toggle] = useToggle()

  return (
    <div className="space-y-2 lg:w-1/7">
      <Clickable
        className="flex flex-row items-center justify-between px-6 pb-3 pt-6 lg:hidden"
        onClick={toggle}
      >
        <DayHeader date={date} daySchedulePath={daySchedulePath} />
        <Icon name={open ? 'chevron-up' : 'chevron-down'} />
      </Clickable>
      <DayHeader
        date={date}
        daySchedulePath={daySchedulePath}
        className="hidden lg:flex lg:flex-row lg:items-center lg:justify-center"
      />
      <div
        className={`transition-all duration-1000 ease-in-out ${
          open
            ? 'max-h-fit opacity-100'
            : 'max-h-0 overflow-hidden opacity-0 lg:max-h-max lg:opacity-100'
        }`}
      >
        {hours.map(hour => (
          <Hour
            time={set(today, { hours: hour })}
            broadcasts={broadcasts?.[hour]}
            key={`day-${getDay(date)}-hour-${hour}`}
          />
        ))}
      </div>
    </div>
  )
}
Day.propTypes = {
  date: PropTypes.object,
  hours: PropTypes.array,
  broadcasts: PropTypes.array,
  daySchedulePath: PropTypes.string,
}

function DayHeader({ className = '', date, daySchedulePath }) {
  const locale = useDatetimeLocale()
  const breakpoint = useBreakpoint()
  const smallScreen = useIsSmallScreen(breakpoint)

  return (
    <Link
      className={`flex-row items-center justify-center p-1 ${className}`}
      to={`${daySchedulePath}?day=${encodeURIComponent(formatISO(date))}`}
    >
      <div
        className={`flex w-full flex-col rounded-lg leading-4 lg:justify-center lg:px-3 lg:py-2 lg:text-center ${
          isSameDay(date, today) ? 'bg-primary-700 px-3 py-1 text-white' : ''
        }`}
      >
        <div className="font-bold text-2xl lg:text-xl">
          {formatDate(date, smallScreen ? 'd LLLL' : 'd LLL', { locale })}
        </div>
        <div
          className={`text-xl lg:text-base ${
            isSameDay(date, today) ? 'text-primary-100' : 'text-gray-500'
          }`}
        >
          {formatDate(date, 'cccc', { locale })}
        </div>
      </div>
    </Link>
  )
}
DayHeader.propTypes = {
  className: PropTypes.string,
  date: PropTypes.object,
  daySchedulePath: PropTypes.string,
}

function Hour({ time, broadcasts, showTime }) {
  const locale = useDatetimeLocale()
  return (
    <div className={`relative ${showTime ? 'pl-24' : ''}`}>
      {showTime && (
        <div className="absolute -top-4 left-0 w-20 p-1 text-center text-gray-500">
          {format(time, 'p', { locale })}
        </div>
      )}

      <div className="relative px-1 lg:h-64">
        {broadcasts?.map((broadcast, i) => (
          <Broadcast
            broadcast={broadcast}
            key={`broadcast-${getHours(time)}-${i}`}
          />
        ))}
      </div>
    </div>
  )
}
Hour.propTypes = {
  broadcasts: PropTypes.arrayOf(PropTypes.shape(broadcastShape)),
  showTime: PropTypes.bool,
  time: PropTypes.object,
}

function Broadcast({ broadcast }) {
  const { t } = useTranslation('media-library')
  const router = useRouter()
  const { setPage } = usePageLoading()

  const { currentBroadcast, setCurrentBroadcast } = useScheduleWeek()

  const startsAt = useMemo(() => new Date(broadcast.startsAt), [broadcast])
  const endsAt = useMemo(() => new Date(broadcast.endsAt), [broadcast])

  const { episode } = broadcast
  const isCurrent = currentBroadcast?.id === broadcast.id
  const duration = intervalToDuration({ start: startsAt, end: endsAt })
  const durationMinutes = duration.minutes + (duration.hours ?? 0) * 60

  useEffect(() => {
    if (router.query?.broadcastId === broadcast.id) {
      setCurrentBroadcast(broadcast)
    }
  }, [broadcast, router.query?.broadcastId, setCurrentBroadcast])

  const wrapStyle = useMemo(() => {
    const startMinutes = startsAt.getMinutes()

    const style = {
      top: `${100 * (startMinutes / 60)}%`,
      height: isCurrent
        ? durationMinutes > 45
          ? '100%'
          : 'auto'
        : `${Math.max(100 * (durationMinutes / 60), 5)}%`,
    }

    return style
  }, [startsAt, durationMinutes, isCurrent])

  const handleClick = useCallback(() => {
    setCurrentBroadcast(isCurrent ? undefined : broadcast)
    setPage(router.asPath)

    router.push(
      {
        pathname: router.pathname,
        query: {
          ...router.query,
          broadcastId: isCurrent ? undefined : broadcast.id,
        },
      },
      null,
      { scroll: false }
    )
  }, [broadcast, setCurrentBroadcast, isCurrent, router, setPage])

  const showTitle = episode?.show ? episode.show.title : broadcast.showTitle
  const episodeTitle = episode ? episode.title : broadcast.episodeTitle
  const isSmall = (100 * durationMinutes) / 60 < 16

  return (
    <Clickable
      className={`group left-0 right-0 h-full p-1 outline-none transition-all duration-300 ease-in-out lg:absolute lg:overflow-hidden ${
        isCurrent ? 'z-20' : 'z-10 hover:z-30 focus:z-30'
      }`}
      onClick={handleClick}
      style={wrapStyle}
      id={broadcast.id}
    >
      <div
        className={clsx(
          'relative flex h-full flex-row justify-between space-x-4 overflow-hidden rounded-lg p-4 ring-inset transition-all duration-100 group-focus:ring-2 rtl:space-x-reverse md:space-x-2',
          {
            'bg-primary-700 ring-primary-800': isCurrent,
            'bg-white ring-primary-400 group-hover:bg-primary-100 group-focus:bg-primary-100':
              !isCurrent,
            'lg:p-0 lg:group-focus:ring-0': !isCurrent && isSmall,
          }
        )}
      >
        <div className="block shrink-0 lg:hidden">
          <div
            className={`text-right font-semibold ${
              isCurrent ? 'text-primary-100' : 'text-gray-600'
            }`}
          >
            {format(startsAt, 'p')}
          </div>
        </div>
        <div
          className={clsx(
            'flex flex-grow flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0 rtl:md:space-x-reverse',
            {
              'lg:hidden': isSmall && !isCurrent,
            }
          )}
        >
          <div className="flex flex-grow flex-col space-y-4">
            <div>
              <h3
                className={`... overflow-hidden text-ellipsis font-bold leading-5 text-lg ${
                  isCurrent ? 'text-primary-50' : 'text-gray-800'
                }`}
              >
                {showTitle}
              </h3>
              <p
                className={`leading-5 ${
                  isCurrent ? 'text-primary-100' : 'text-gray-600'
                }`}
              >
                {episodeTitle}
              </p>
            </div>
            {isCurrent && episode && (
              <div className="justify-self-end">
                <Button
                  label={t('watchNow')}
                  size="xs"
                  icon="play"
                  variant="alt"
                  url={episode.url}
                />
              </div>
            )}
          </div>
          <div className="lg:hidden">
            {episode?.image && !isEmpty(episode.image) && (
              <Image
                className="w-40 rounded"
                alt={episodeTitle}
                file={episode.image}
                sizes="md:384px sm:256px 200px"
                lazy
                quality={50}
              />
            )}
          </div>
        </div>

        <div className="block shrink-0 lg:hidden">
          <span
            className={`${
              isCurrent ? 'font-semibold text-primary-100' : 'text-gray-500'
            }`}
          >
            {t('durationInMinutes', { duration: durationMinutes })}
          </span>
        </div>

        {!isCurrent && !isSmall && (
          <div className="absolute -left-2 bottom-0 right-0 hidden h-6 rounded-b-lg border-primary-400 bg-gradient-to-b from-transparent via-white/80 to-white group-hover:via-primary-100/80 group-hover:to-primary-100 group-focus:border-x-2 group-focus:border-b-2 group-focus:via-primary-100/80 group-focus:to-primary-100 lg:block" />
        )}
      </div>
    </Clickable>
  )
}
Broadcast.propTypes = {
  broadcast: PropTypes.shape(broadcastShape),
}

function BroadcastTime({ isCurrent, isEnd, time }) {
  const currentClass = isCurrent
    ? 'opacity-100 max-h-screen'
    : 'opacity-0 max-h-0'
  const positionClass = isEnd ? '-bottom-4' : '-top-4'

  return (
    <div
      className={`absolute -left-24 w-20 rounded-lg bg-primary-100 p-1 text-center text-primary-700 transition-all duration-300 ease-in-out ${currentClass} ${positionClass}`}
    >
      <div className="absolute -right-1 top-3 h-2 w-2 rotate-45 bg-primary-100" />
      <span className="font-semibold">{format(time, 'p')}</span>
    </div>
  )
}
BroadcastTime.propTypes = {
  isCurrent: PropTypes.bool,
  isEnd: PropTypes.bool,
  time: PropTypes.object,
}
