import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Image = dynamic(() => import('ui/data-display/Image'))

export default function ShowImage({ showImage }) {
  if (!showImage?.images?.default) return null

  const { images, title } = showImage

  // TODO: add size for row or container
  return (
    <Image alt={title} className="w-full rounded-lg" file={images.default} />
  )
}
ShowImage.propTypes = {
  showImage: PropTypes.object,
}
