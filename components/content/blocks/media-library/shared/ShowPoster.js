import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Poster = dynamic(() => import('ui/data-display/Poster'))

export default function ShowPoster({
  animationOrigin = 'center',
  enableTitle,
  enableAbstract,
  enableCallToAction,
  callToActionLabel,
  enableAnimation,
  show,
}) {
  return (
    <Poster
      backgroundImage={show.images?.poster ? null : show.images?.default}
      image={show.images?.poster || show.images?.default}
      title={show.title}
      url={show.url}
      animationOrigin={animationOrigin}
      enableAnimation={enableAnimation}
      enableDescription={enableAbstract}
      enableTitle={enableTitle}
      description={show.abstract}
      enableCallToAction={enableCallToAction}
      callToActionLabel={callToActionLabel}
      callToActionIcon="play"
    />
  )
}

ShowPoster.propTypes = {
  animationOrigin: PropTypes.oneOf(['center', 'left', 'right']),
  callToActionLabel: PropTypes.string,
  enableCallToAction: PropTypes.bool,
  enableTitle: PropTypes.bool,
  enableAbstract: PropTypes.bool,
  enableAnimation: PropTypes.bool,
  show: PropTypes.object,
}
