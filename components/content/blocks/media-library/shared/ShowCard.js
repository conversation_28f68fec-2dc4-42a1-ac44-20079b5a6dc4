import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Card = dynamic(() => import('ui/data-display/Card'))

export default function ShowCard({
  animationOrigin = 'center',
  enableAnimation,
  show,
  showDescription,
}) {
  return (
    <Card
      className="h-full"
      enableAnimation={enableAnimation}
      animationOrigin={animationOrigin}
      image={show.images?.default}
      imageAspectRatio="16/9"
      imageSizes="md:300px sm:350px 180px"
      title={show.title}
      maxLinesTitle={2}
      description={showDescription ? show.abstract : null}
      maxLinesDescription={3}
      url={show.url}
    />
  )
}

ShowCard.propTypes = {
  animationOrigin: PropTypes.oneOf(['center', 'left', 'right']),
  enableAnimation: PropTypes.bool,
  show: PropTypes.object,
  showDescription: PropTypes.bool,
}
