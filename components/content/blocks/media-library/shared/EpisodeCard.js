import PropTypes from 'prop-types'
import React from 'react'

import dynamic from 'next/dynamic'
import { isEmpty } from 'utils/objects'

const Card = dynamic(() => import('ui/data-display/Card'))

export default function EpisodeCard({
  animationOrigin = 'center',
  enableAnimation,
  episode,
  showDescription = true,
  showKicker,
}) {
  const { jetstream, youtube } = episode.mediaLinks || {}
  const duration = jetstream?.duration || youtube?.duration || 0

  return (
    <Card
      enableAnimation={enableAnimation}
      animationOrigin={animationOrigin}
      className="h-full"
      kicker={showKicker ? episode.show?.title : null}
      title={episode.title}
      maxLinesTitle={2}
      description={showDescription ? episode.abstract : null}
      image={
        !isEmpty(episode.image) ? episode.image : episode.show?.images?.default
      }
      imageAspectRatio="16/9"
      imageSizes="md:300px sm:350px 180px"
      maxLinesDescription={3}
      url={episode.url}
      extra={
        duration > 0 ? (
          <div className="flex justify-between pt-2 text-gray-400">
            {Math.floor(duration / 60)} min
          </div>
        ) : null
      }
    />
  )
}
EpisodeCard.propTypes = {
  animationOrigin: PropTypes.oneOf(['center', 'left', 'right']),
  enableAnimation: PropTypes.bool,
  episode: PropTypes.object,
  showDescription: PropTypes.bool,
  showKicker: PropTypes.bool,
}
