import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function ViewAll({ url }) {
  const { t } = useTranslation('media-library')
  const isSm = useMatchMedia(media.sm)
  if (!url) return null
  return (
    <Link className="font-semibold uppercase" to={url} basic>
      {isSm ? (
        t('common:viewAll')
      ) : (
        <Icon name="chevron-right" className="-mr-2 px-2 py-1" />
      )}
    </Link>
  )
}

ViewAll.propTypes = {
  url: PropTypes.string,
}
ViewAll.defaultProps = {}
