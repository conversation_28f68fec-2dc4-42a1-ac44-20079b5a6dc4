import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const Hero = dynamic(() => import('ui/data-display/Hero'))

export default function EpisodeHero({ episode }) {
  const { t } = useTranslation('media-library')

  const { abstract, categories, image, title, url } = episode

  return (
    <Hero
      image={image}
      title={title}
      description={abstract}
      ctaLabel={t('watchNow')}
      ctaUrl={url}
      tags={categories}
    />
  )
}

EpisodeHero.prepisode = {
  episode: PropTypes.object,
}
