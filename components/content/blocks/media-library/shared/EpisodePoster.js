import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Poster = dynamic(() => import('ui/data-display/Poster'))

export default function EpisodePoster({
  animationOrigin = 'center',
  enableTitle,
  enableAbstract,
  enableCallToAction,
  callToActionLabel,
  enableAnimation,
  episode,
}) {
  const image = episode?.images?.poster ?? episode?.image

  return (
    <Poster
      backgroundImage={image}
      image={image}
      title={episode.title}
      url={episode.url}
      animationOrigin={animationOrigin}
      enableAnimation={enableAnimation}
      enableDescription={enableAbstract}
      enableTitle={enableTitle}
      description={episode.abstract}
      enableCallToAction={enableCallToAction}
      callToActionLabel={callToActionLabel}
      callToActionIcon="play"
    />
  )
}

EpisodePoster.propTypes = {
  animationOrigin: PropTypes.oneOf(['center', 'left', 'right']),
  episode: PropTypes.object,
  callToActionLabel: PropTypes.string,
  enableCallToAction: PropTypes.bool,
  enableAbstract: PropTypes.bool,
  enableTitle: PropTypes.bool,
  enableAnimation: PropTypes.bool,
}
