import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

const Player = dynamic(() => import('ui/data-display/Player'))

export default function LivestreamPlayer({ channel }) {
  const { t } = useTranslation('media-library')

  const { jetstreamId } = channel || {}
  const video = jetstreamId
    ? {
        provider: 'jetstream-live',
        id: !jetstreamId.startsWith('jsl:')
          ? `jsl:${jetstreamId}`
          : jetstreamId,
      }
    : // : hls
      // ? {
      //     provider: 'hopemedia',
      //     id: hls,
      //   }
      null

  return (
    <>
      {video ? (
        <Player key={video.id} provider={video.provider} id={video.id} />
      ) : (
        <div className="w-full py-8 text-center font-extralight text-gray-300 text-2xl">
          {t('livestreamMissing')}
        </div>
      )}
    </>
  )
}
LivestreamPlayer.propTypes = {
  channel: PropTypes.object,
}
