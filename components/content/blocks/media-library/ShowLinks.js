import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const LinkItem = dynamic(() =>
  import('ui/navigation/LinkList').then(m => m.LinkItem)
)

export default function ShowLinks({ className = '', showLinks = [], title }) {
  const { t } = useTranslation('media-library')

  if (!showLinks?.length) return null

  return (
    <div className={`w-full space-y-4 ${className}`}>
      <LinkList title={title || t('links')}>
        {showLinks
          .filter(showLink => showLink.url)
          .map((showLink, key) => {
            const { title, url } = showLink
            return <LinkItem icon="link" key={key} label={title} url={url} />
          })}
      </LinkList>
    </div>
  )
}
ShowLinks.propTypes = {
  className: PropTypes.string,
  showLinks: PropTypes.array,
  title: PropTypes.string,
}
