import PropTypes from 'prop-types'

import { useTranslation } from 'next-i18next'

import { FormatDate } from 'utils/datetime'

export function MetaItem({ title, value }) {
  return value ? (
    <div className="flex">
      <div className="w-1/3 font-bold">{title}</div>
      <div className="w-2/3">{value}</div>
    </div>
  ) : null
}
MetaItem.propTypes = {
  title: PropTypes.string,
  value: PropTypes.node,
}

export default function EpisodeMeta({
  className = '',
  episodeMetaData = {
    publicationDate: null,
    firstAirDate: null,
    hosts: [],
    guests: [],
    categories: [],
    language: null,
    subtitles: [],
  },
  showPublicationDate,
  showFirstAirDate,
  showHosts,
  showGuests,
  showCategories,
  showLanguage,
  showSubtitles,
}) {
  const { t } = useTranslation('media-library')

  const {
    publicationDate,
    firstAirDate,
    hosts,
    guests,
    categories,
    language,
    subtitles,
  } = episodeMetaData || {}

  return (
    <div className={`w-full text-gray-500 dark:text-gray-100 ${className}`}>
      {showPublicationDate && publicationDate && (
        <MetaItem
          title={t('onlineSince')}
          value={<FormatDate date={publicationDate} />}
        />
      )}
      {showFirstAirDate && firstAirDate && (
        <MetaItem
          title={t('firstAirDate')}
          value={<FormatDate date={firstAirDate} />}
        />
      )}
      {showHosts && hosts && (
        <MetaItem
          title={t('hosts')}
          value={hosts.map(host => host.fullName).join(', ')}
        />
      )}
      {showGuests && guests && (
        <MetaItem
          title={t('guests')}
          value={guests.map(guest => guest.fullName).join(', ')}
        />
      )}
      {showCategories && categories && (
        <MetaItem
          title={t('categories')}
          value={categories.map(category => category.title).join(', ')}
        />
      )}
      {showLanguage && language && (
        <MetaItem title={t('language')} value={language} />
      )}
      {showSubtitles && Array.isArray(subtitles) && (
        <MetaItem
          title={t('subtitles')}
          value={subtitles
            .map(language => (language === 'en' ? 'English' : language))
            .join(', ')}
        />
      )}
    </div>
  )
}
EpisodeMeta.propTypes = {
  className: PropTypes.string,
  episodeMetaData: PropTypes.object,
  showPublicationDate: PropTypes.bool,
  showFirstAirDate: PropTypes.bool,
  showHosts: PropTypes.bool,
  showGuests: PropTypes.bool,
  showCategories: PropTypes.bool,
  showLanguage: PropTypes.bool,
  showSubtitles: PropTypes.bool,
}
