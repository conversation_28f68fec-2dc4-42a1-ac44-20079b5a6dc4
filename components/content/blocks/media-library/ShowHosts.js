import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

const Heading = dynamic(() => import('ui/typography/Heading'))
const Image = dynamic(() => import('ui/data-display/Image'))
const ConditionalLink = dynamic(() =>
  import('ui/navigation/Link').then(m => m.ConditionalLink)
)

export default function ShowHosts({ showHosts }) {
  const { t } = useTranslation('media-library')

  if (!showHosts?.length) return null

  return (
    <div className="space-y-4">
      <Heading
        as="h5"
        title={showHosts.length === 1 ? t('host') : t('hosts')}
        capitalized
      />
      <div className="flex space-x-8 rtl:space-x-reverse">
        {showHosts.map((host, key) => {
          const { avatar, fullName, url } = host
          return (
            <div className="w-16 space-y-2 md:w-24" key={key}>
              <ConditionalLink condition={url} to={url}>
                {avatar ? (
                  <Image
                    alt={fullName}
                    aspectRatio="1/1"
                    file={avatar}
                    className="rounded-full"
                    sizes="md:96px 64px"
                  />
                ) : (
                  <div className="h-16 w-16 rounded-full bg-gray-200 md:h-24 md:w-24" />
                )}
              </ConditionalLink>
              <h3 className="text-center leading-tight">{fullName}</h3>
            </div>
          )
        })}
      </div>
    </div>
  )
}
ShowHosts.propTypes = {
  Show: PropTypes.object,
  showHosts: PropTypes.array,
}
