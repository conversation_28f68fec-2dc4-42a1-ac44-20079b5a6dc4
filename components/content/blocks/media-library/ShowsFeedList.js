import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { isEmpty } from 'utils/arrays'

const Link = dynamic(() => import('ui/navigation/Link'))

export default function ShowsFeedList({ showsFeedList = [], title }) {
  if (isEmpty(showsFeedList)) return null

  return (
    <div className="space-y-6">
      {title && <h3 className="font-bold text-2xl">{title}</h3>}
      <div className="space-y-4">
        {showsFeedList.map((show, key) => (
          <div key={key}>
            <div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="font-semibold text-lg">{show.title}</div>
                {/* <div>-</div>
                <div className="text-sm font-light text-gray-500">
                  {show.episodes} episodes
                </div> */}
              </div>
            </div>
            <Link basic to={show.feedUrl}>
              {show.feedUrl}
            </Link>
          </div>
        ))}
      </div>
    </div>
  )
}
ShowsFeedList.propTypes = {
  title: PropTypes.string,
  showsFeedList: PropTypes.array,
}
