import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const RichText = dynamic(() => import('ui/typography/RichText'))

export default function VideoDetail({
  showAbstract,
  showBody,
  showTitle,
  video,
}) {
  if (!video) return null

  const { abstract, body, title } = video

  return (
    <div className="space-y-6">
      {showTitle && <h1 className="text-3xl font-extrabold">{title}</h1>}
      {showAbstract && abstract && (
        <p className="font-serif text-lg italic leading-6 text-gray-500">
          {abstract}
        </p>
      )}
      {showBody && body && <RichText doc={body} />}
    </div>
  )
}
VideoDetail.propTypes = {
  showAbstract: PropTypes.bool,
  showBody: PropTypes.bool,
  showTitle: PropTypes.bool,
  video: PropTypes.object,
}
