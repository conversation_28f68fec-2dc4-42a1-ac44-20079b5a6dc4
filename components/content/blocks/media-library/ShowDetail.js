import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'
import { useTranslation } from 'next-i18next'

import RichText, { isDocEmpty } from 'ui/typography/RichText'

const Image = dynamic(() => import('ui/data-display/Image'))
const Player = dynamic(() => import('ui/data-display/Player'))
const SeparatorLine = dynamic(() => import('ui/data-display/SeparatorLine'))

export default function ShowDetail({
  showDetail,
  showAbstract,
  showBody,
  showImage,
  showTitle,
  showTrailer,
  title: pluginTitle,
}) {
  const { t } = useTranslation('media-library')
  if (!showDetail) return null

  const { abstract, body, images, title, trailer } = showDetail

  const hasContent =
    (showImage && images?.default) ||
    (showAbstract && abstract) ||
    (showBody && !isDocEmpty(body)) ||
    showTitle

  if (!hasContent) return null

  return (
    <div className="space-y-6">
      {pluginTitle && (
        <h3 className="font-bold uppercase leading-loose text-lg dark:text-primary-dark-100">
          {pluginTitle}
        </h3>
      )}
      {showImage && images?.default && (
        <Image file={images.default} alt={title} className="rounded-lg" />
      )}
      {showTitle && (
        <>
          <h1 className="font-extrabold text-4xl dark:text-primary-dark-300">
            {title}
          </h1>
          <SeparatorLine />
        </>
      )}
      {showAbstract && abstract && (
        <p className="pb-4 font-serif italic leading-relaxed text-gray-700 text-lg lg:pb-0 dark:text-gray-300">
          {abstract}
        </p>
      )}
      {showTrailer && trailer?.provider && trailer?.videoId && (
        <div className="max-w-sm">
          <Player
            provider={trailer.provider}
            id={trailer.videoId}
            ctaIcon="play"
            ctaLabel={t('watchTrailer')}
            ctaVariant="secondary"
            variant="lightboxButton"
          />
        </div>
      )}
      {showBody && !isDocEmpty(body) && <RichText doc={body} />}
    </div>
  )
}
ShowDetail.propTypes = {
  showDetail: PropTypes.object,
  showAbstract: PropTypes.bool,
  showBody: PropTypes.bool,
  showImage: PropTypes.bool,
  showTitle: PropTypes.bool,
  showTrailer: PropTypes.bool,
  title: PropTypes.string,
}
