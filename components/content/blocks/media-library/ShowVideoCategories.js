import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { isEmpty } from 'utils/arrays'

const Link = dynamic(() => import('ui/navigation/Link'))
const LinkList = dynamic(() => import('ui/navigation/LinkList'))
const LinkItem = dynamic(() =>
  import('ui/navigation/LinkList').then(m => m.LinkItem)
)

export default function ShowVideoCategories({
  displayMode,
  showVideoCategories = [],
  videoCategory,
}) {
  if (isEmpty(showVideoCategories)) return null

  return displayMode === 'list' ? (
    <ul className="ml-8 list-disc space-y-1">
      {showVideoCategories.map(category => (
        <li key={category._id}>
          <Link to={category.url} basic>
            {category.title}
          </Link>
        </li>
      ))}
    </ul>
  ) : (
    <LinkList>
      {showVideoCategories.map((category, key) => {
        const { title, url } = category
        const icon = 'video'
        const active = videoCategory?.id === category.id
        return (
          <LinkItem
            active={active}
            icon={icon}
            key={key}
            label={title}
            url={url}
          />
        )
      })}
    </LinkList>
  )
}
ShowVideoCategories.propTypes = {
  displayMode: PropTypes.oneOf(['list', 'menu']),
  showVideoCategories: PropTypes.array,
  videoCategory: PropTypes.object,
}
