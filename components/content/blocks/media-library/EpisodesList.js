import { useState, useEffect } from 'react'
import PropTypes from 'prop-types'

import axios from 'axios'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { useTranslation } from 'next-i18next'

const API_URL = process.env.NEXT_PUBLIC_API_URL
const API_CLIENT_TOKEN = process.env.NEXT_PUBLIC_API_CLIENT_TOKEN

const CardContainer = dynamic(() => import('ui/data-display/CardContainer'))
const Select = dynamic(() => import('ui/data-entry/Select').then(m => m.Select))
const Button = dynamic(() => import('ui/buttons/Button'))

import EpisodeCard from './shared/EpisodeCard'

const itemsToLoad = 16

export default function EpisodesList({
  episodes = { items: [], count: 0 },
  episodeDetailPageId,
  limit,
  title,
  show,
  sortField,
  sortOrder,
  showDescription = true,
  enableAnimations,
}) {
  const { t } = useTranslation('media-library')
  const router = useRouter()
  const seasonSlug = router.query.season
  const [_episodes, setEpisodes] = useState(episodes?.items || [])
  const [_loading, setLoading] = useState(false)
  const [_seasonSlug, setSeasonSlug] = useState(seasonSlug)
  const [_count, setCount] = useState(episodes?.count || 0)
  const [_itemsLoaded, setItemsLoaded] = useState(limit)

  useEffect(() => {
    setSeasonSlug(seasonSlug)
    setEpisodes(episodes?.items || [])
    setLoading(false)
    setCount(episodes?.count || 0)
    setItemsLoaded(limit)
  }, [show, episodes, limit, seasonSlug])

  if (!show) return null

  const handleLoadMore = async () => {
    try {
      setLoading(true)
      const res = await axios.get(
        `${API_URL}/media-library/shows/${show._id}/episodes/load-more`,
        {
          headers: { ClientToken: API_CLIENT_TOKEN },
          params: {
            ...(_seasonSlug && { season: _seasonSlug }),
            ...(sortField && {
              sort: `${sortOrder === 'desc' ? '-' : ''}${sortField}`,
            }),
            skip: _itemsLoaded,
            limit: itemsToLoad,
            episodeDetailPageId,
          },
        }
      )
      const result = res.data
      if (result) {
        setItemsLoaded(_itemsLoaded + itemsToLoad)
        setEpisodes([..._episodes, ...result.items])
        setCount(result.count)
      }
    } catch (error) {
      console.log(error) // eslint-disable-line no-console
    } finally {
      setLoading(false)
    }
  }

  const onSeasonChange = ({ target }) => {
    const selectedSeasonSlug = target.value
    setSeasonSlug(selectedSeasonSlug)
    setEpisodes([])
    setCount(0)
    setLoading(true)
    router.push({
      pathname: router.pathname,
      query: { ...router.query, season: selectedSeasonSlug },
    })
  }

  return (
    <div className="space-y-8">
      <CardContainer
        extra={
          show.seasons?.length ? (
            <Select
              onChange={onSeasonChange}
              options={show.seasons
                .filter(season => season.episodeCount > 0)
                .map(season => ({
                  label: season.groupName
                    ? `${season.groupName}: ${season.title}`
                    : season.title,
                  value: season.slug,
                }))}
              value={_seasonSlug}
              placeholder={t('selectSeason')}
            />
          ) : null
        }
        title={title}
      >
        {_episodes.map(episode => (
          <EpisodeCard
            episode={episode}
            key={episode._id}
            showDescription={showDescription}
            enableAnimation={enableAnimations}
          />
        ))}
      </CardContainer>
      {(_loading || _itemsLoaded < _count) && (
        <div className="flex justify-center">
          <Button
            icon={_loading ? 'spinner-third' : ''}
            iconClass={_loading ? 'animate-spin' : ''}
            label={_loading ? t('common:loading') : t('common:loadMore')}
            onClick={handleLoadMore}
            disabled={_loading}
          />
        </div>
      )}
    </div>
  )
}

EpisodesList.propTypes = {
  episodes: PropTypes.shape({
    items: PropTypes.array,
    count: PropTypes.number,
  }),
  episodeDetailPageId: PropTypes.string,
  limit: PropTypes.number,
  onLoadMoreEpisodes: PropTypes.func,
  show: PropTypes.object,
  sortField: PropTypes.string,
  sortOrder: PropTypes.string,
  title: PropTypes.string,
  showDescription: PropTypes.bool,
}
