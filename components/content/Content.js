import { useContentClasses } from 'ui/helpers/useContentClasses'

import useValueAtBreakpoint from 'ui/helpers/useValueAtBreakpoint'

import blocks from './blocks'
import React from 'react'

/**
 * Component to render the page content of web modules
 * @param {Object} props Component props
 * @param {String} props.id Content id, aka "block id". Default is 'ROOT', the root node of the content tree.
 * @param {Object} props.page Current page object
 * @returns {React.Component} The content component
 */
export default function Content({ id = 'ROOT', page, ...otherProps }) {
  const { content = {}, pageData = {} } = page || {}
  const node = content[id]

  const className = useContentClasses(node?.props, content[node?.parent])

  const visibilityValue = useValueAtBreakpoint(
    node?.props?.visibility,
    'visible'
  )

  if (!node || node?.hidden || visibilityValue === 'hidden') {
    return null
  }

  const NodeRender = blocks[node.displayName]
  if (!NodeRender) {
    console.warn('Missing node render', node.displayName) // eslint-disable-line no-console
    return null
  }

  const props = {
    ...node.props,
    ...otherProps, // Include other props passed to the Content component directly (like when a partent component is passing props to its children, or with React.Children.map)
    pageData,
    blockId: id,
    className,
  }

  for (const [nodeKey, nodeId] of Object.entries(node.linkedNodes)) {
    props[nodeKey] = <Content id={nodeId} key={nodeId} page={page} />
  }

  return (
    <NodeRender {...props} key={id}>
      {node.nodes.length > 0 &&
        node.nodes.map(childNodeId => (
          <Content id={childNodeId} key={childNodeId} page={page} />
        ))}
    </NodeRender>
  )
}
