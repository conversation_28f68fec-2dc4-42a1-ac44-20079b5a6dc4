import React from 'react'
import PropTypes from 'prop-types'

import { signOut, useSession } from 'next-auth/react'

import { useMatchMedia } from 'hooks/useMatchMedia'
import { media } from 'utils/media'

import { usePageContext } from './PageProvider'
import { ItemLevel1, ItemLevel2 } from 'ui/navigation/Navigation'
import { useTranslation } from 'next-i18next'
import ButtonGroup from 'ui/buttons/ButtonGroup'
import Button from 'ui/buttons/Button'

export default function UserMenu() {
  const { t } = useTranslation()
  const pageContext = usePageContext()
  const { data: session, status } = useSession()

  const isLoggedIn = Boolean(session?.user && status === 'authenticated')

  const isLg = useMatchMedia(media.lg)

  const loginPageUrl = pageContext?.site?.auth?.pages?.loginPageUrl ?? '/login'
  const registerPageUrl =
    pageContext?.site?.auth?.pages?.registerPageUrl ?? '/register'
  const accountPageUrl =
    pageContext?.site?.auth?.pages?.userAccountPageUrl ?? '/account'

  return (
    <>
      {!isLoggedIn && (
        <>
          {isLg ? (
            <ButtonGroup className="ml-6">
              <Button
                url={loginPageUrl}
                label={t('signIn')}
                icon="user"
                variant="hollow"
                size="sm"
              />
              <Button url={registerPageUrl} label={t('register')} size="sm" />
            </ButtonGroup>
          ) : (
            <>
              <ItemLevel1 url={registerPageUrl} label={t('register')} />
              <ItemLevel1 url={loginPageUrl} label={t('signIn')} />
            </>
          )}
        </>
      )}
      {isLoggedIn && (
        <>
          {isLg && <div className="ml-1">|</div>}
          <ItemLevel1 label={session?.user?.name || t('myAccount')} icon="user">
            <ItemLevel2 label={t('account')} url={accountPageUrl} />
            <ItemLevel2
              label={t('logout')}
              className="cursor-pointer"
              onClick={() => {
                signOut({ callbackUrl: '/' })
              }}
            />
          </ItemLevel1>
        </>
      )}
    </>
  )
}

UserMenu.propTypes = {
  authEnabled: PropTypes.bool,
}
