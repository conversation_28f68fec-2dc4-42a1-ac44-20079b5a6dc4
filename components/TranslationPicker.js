import PropTypes from 'prop-types'
import React from 'react'

import { Listbox } from '@headlessui/react'

import Icon from 'ui/icons/Icon'

export default function TranslationPicker({ translations }) {
  const translationEntries = Object.entries(translations || {}).filter(
    ([, { site }]) => site !== undefined
  )
  if (translationEntries.length <= 1) {
    return null
  }

  const lang = Object.keys(translations).find(
    key => translations[key].site.current
  )

  return (
    <div className="relative flex flex-row items-center space-x-2 rtl:space-x-reverse">
      <Icon name="globe" />

      <Listbox value="current" onChange={() => null}>
        <Listbox.Button>
          {translations[lang]?.language?.nativeName || ''}
        </Listbox.Button>
        <Listbox.Options className="absolute bottom-8 -left-2 overflow-hidden rounded-md bg-white text-primary-900 shadow ring-0">
          {translationEntries.map(([lang, { site, page, language }]) => (
            <Listbox.Option
              key={lang}
              value={site.current ? 'current' : lang}
              disabled={site.current}
              as={React.Fragment}
            >
              {({ active }) => (
                <li>
                  {site.current ? (
                    <span className="block cursor-not-allowed px-4 py-2 font-semibold text-gray-400 hover:bg-gray-200 hover:text-gray-500">
                      {language.nativeName}
                    </span>
                  ) : (
                    <a
                      href={`https://${site.domain}${page?.path || ''}`}
                      target={`language-${lang}`}
                      className={`block px-4 py-2 ${
                        active ? 'bg-primary-500 text-primary-50' : ''
                      }`}
                    >
                      {language.nativeName}
                    </a>
                  )}
                </li>
              )}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </Listbox>
    </div>
  )
}
TranslationPicker.propTypes = {
  translations: PropTypes.object,
}
