import React from 'react'
import { useTranslation } from 'next-i18next'

import Button from 'ui/buttons/Button'

export default function DeleteConfirmationModal({
  show,
  videoToDelete,
  onConfirm,
  onCancel,
}) {
  const { t } = useTranslation('pwa')
  if (!show || !videoToDelete) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        {/* Background overlay */}
        <button
          type="button"
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity cursor-default"
          onClick={onCancel}
          aria-label={t('cancel')}
        />

        {/* Modal panel */}
        <div className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
          <div className="sm:flex sm:items-start">
            <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
                />
              </svg>
            </div>
            <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
              <h3 className="text-base font-semibold leading-6 text-gray-900">
                {t('deleteConfirmationTitle')}
              </h3>
              <div className="mt-2">
                <p className="text-sm text-gray-500">
                  {t('deleteConfirmationMessage', {
                    title: videoToDelete.title
                      ? `"${videoToDelete.title}"`
                      : t('thisVideo'),
                  })}
                </p>
              </div>
            </div>
          </div>
          <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse sm:gap-3">
            <Button
              onClick={onConfirm}
              variant="danger"
              label={t('delete')}
              className="w-full sm:w-auto"
            />
            <Button
              onClick={onCancel}
              variant="secondary"
              label={t('cancel')}
              className="mt-3 sm:mt-0 w-full sm:w-auto"
            />
          </div>
        </div>
      </div>
    </div>
  )
}
