import React, { useContext } from 'react'
import useDarkMode from 'ui/helpers/useDarkMode'

const PageContext = React.createContext()

/**
 * Provides the current page data to all children
 * @param {Object} props Component props
 * @param {Object} props.page The current page data
 * @param {React.ReactNode} props.children The children to render
 */
export default function PageProvider({ children, page }) {
  const { darkMode, onToggleDarkMode } = useDarkMode(page?.design?.appearance)

  return (
    <PageContext.Provider
      value={{
        ...page,
        darkMode,
        toggleDarkMode: onToggleDarkMode,
      }}
    >
      {children}
    </PageContext.Provider>
  )
}

/**
 * Hook to access the current page context data
 * @returns {Object} The current page context data, including the page data and dark mode state
 */
export function usePageContext() {
  return useContext(PageContext)
}
