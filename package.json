{"name": "hope-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "dotenv -e .env.local next dev", "dev:local-prod": "dotenv -e .env.local-prod next dev", "dev:local-prod-scalingo": "dotenv -e .env.local-prod-scalingo next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@floating-ui/react": "^0.26.9", "@headlessui/react": "^1.7.18", "@mapbox/mapbox-gl-geocoder": "^5.0.2", "@maptiler/client": "^1.8.1", "@maptiler/geocoding-control": "^1.3.2", "@maptiler/sdk": "^2.2.0", "@next/bundle-analyzer": "^14.1.0", "@serwist/next": "^9.2.0", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@vimeo/player": "^2.21.0", "axios": "^0.27.2", "body-parser": "^1.20.3", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dinero.js": "^1.9.1", "disqus-react": "^1.1.7", "embla-carousel-autoplay": "^8.1.6", "embla-carousel-fade": "^8.1.6", "embla-carousel-react": "^8.1.6", "html-react-parser": "^5.1.1", "i18next": "^23.8.2", "lodash": "^4.17.21", "mapbox-gl": "^3.5.1", "next": "^14.2.26", "next-auth": "^4.24.5", "next-i18next": "^15.2.0", "next-seo": "^6.4.0", "nodemailer": "^6.9.9", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.5", "react-i18next": "^14.0.4", "react-map-gl": "^7.1.7", "react-query": "^3.39.3", "react-share": "^5.0.3", "tailwind-merge": "^3.3.1", "video.js": "^8.10.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@trivago/prettier-plugin-sort-imports": "^4.2.0", "@types/howler": "^2.2.12", "@types/video.js": "^7.3.58", "autoprefixer": "^10.4.21", "dotenv-cli": "^7.3.0", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.34", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.5.11", "serwist": "^9.2.0", "tailwindcss": "^3.4.1"}}