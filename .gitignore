# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.local*
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# Editor files
.idea

# NVM
.nvmrc

# Serwist
public/sw*
public/swe-worker*